name: Azure Static Web Apps CI/CD

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - "projects/product-studio/*"
      - "package.json"
      - "package-lock.json"
      - "angular.json"
      - "tsconfig.json"

variables:
  - group: elder-wand-variables # Variable group containing shared variables
  - name: npm_config_cache
    value: $(Pipeline.Workspace)/.npm

jobs:
  - job: build_product_studio
    displayName: "Build Product Studio"
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - template: templates/node-build-steps.yml

      - script: |
          # Replace environment variables in the production environment file
          sed -i "s|\$(PRODUCT_STUDIO_URL)|$(PRODUCT_STUDIO_URL)|g" projects/product-studio/src/environments/environment.prod.ts
          sed -i "s|\$(API_URL)|$(API_URL)|g" projects/product-studio/src/environments/environment.prod.ts

          # Build with environment variables
          PRODUCT_STUDIO_URL="$(PRODUCT_STUDIO_URL)" npm run build:product-studio
          echo "Checking build output directory:"
          ls -la dist
          echo "Checking product-studio build directory:"
          ls -la dist/product-studio
        displayName: "Build Product Studio"
        env:
          NODE_ENV: production
          PRODUCT_STUDIO_URL: $(PRODUCT_STUDIO_URL)
          API_URL: $(API_URL)
          NODE_AUTH_TOKEN: $(Azure_DevOPS_PAT)

      - task: CopyFiles@2
        inputs:
          sourceFolder: 'dist/product-studio'
          contents: '**/*'
          targetFolder: '$(Build.ArtifactStagingDirectory)/product-studio'
        displayName: 'Copy Build Artifacts'

      - task: PublishBuildArtifacts@1
        inputs:
          pathToPublish: '$(Build.ArtifactStagingDirectory)/product-studio'
          artifactName: 'product-studio'
        displayName: 'Publish Build Artifacts'

  - job: deploy_product_studio
    displayName: "Deploy Product Studio"
    dependsOn: build_product_studio
    condition: succeeded()
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - checkout: self
        displayName: 'Checkout Repository'

      - download: current
        artifact: 'product-studio'
        displayName: 'Download Build Artifacts'

      - script: |
          echo "Current directory:"
          pwd
          echo "Pipeline workspace:"
          echo $(Pipeline.Workspace)
          echo "Checking downloaded artifacts:"
          ls -la $(Pipeline.Workspace)/product-studio
          echo "Checking working directory:"
          ls -la $(Pipeline.Workspace)/s
          echo "Checking root directory:"
          ls -la /
        displayName: 'Verify Artifacts'

      - script: |
          # Create deployment directory
          mkdir -p $(Pipeline.Workspace)/deploy
          # Copy artifacts to deployment directory
          cp -r $(Pipeline.Workspace)/product-studio/* $(Pipeline.Workspace)/deploy/
          echo "Checking deployment directory:"
          $(Pipeline.Workspace)/deploy
          ls -la $(Pipeline.Workspace)/deploy
        displayName: 'Prepare Deployment Directory'

      - task: AzureStaticWebApp@0
        inputs:
          azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_PRODUCT_STUDIO)
          app_location: "/"
          cwd: "$(Pipeline.Workspace)/deploy"
          output_location: ""
          skip_app_build: true
          api_location: ""
          deployment_source: "local"
        displayName: "Deploy Product Studio to Azure Static Web Apps"


import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { Router } from '@angular/router';
import SearchBar from '../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../shared/components/data-card/data-card.component';
import { CardData } from '../../shared/models/card.model';
import { FilterBarComponent } from '../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../shared/services/filter.service';
import { FilterConfig } from '../../shared/models/filter.model';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../shared/services/pagination.service';
import { AgentServiceService } from './services/agent-service.service';
import agentsConfigData from './constants/agents.json';

@Component({
  selector: 'app-agents',
  standalone: true,
  imports: [
    CommonModule,
    SearchBar,
    CreateCardComponent,
    DataCardComponent,
    FilterBarComponent,
    PageFooterComponent
  ],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.scss'
})
export class AgentsComponent implements OnInit {
  allAgents: CardData[] = [];
  filteredAgents: CardData[] = [];
  displayedAgents: CardData[] = [];
  agentFilterConfig!: FilterConfig;
  
  // Configuration from JSON
  agentsConfig = agentsConfigData;
  
  // Track filter bar visibility
  isFilterBarVisible: boolean = false;
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = agentsConfigData.pagination.itemsPerPage;
  totalPages: number = 1;
  
  // Map filter IDs to CardData properties
  private filterPropertyMap: {[key: string]: string} = agentsConfigData.filterProperties;

  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router,
    private agentService: AgentServiceService
  ) {}

  ngOnInit(): void {
    this.agentFilterConfig = this.filterService.getFilterConfig('agents');
    this.fetchAgents();
  }

  fetchAgents(): void {
    this.agentService.getAllAgentList().subscribe({
      next: (response) => {
        // Transform the response to match the CardData structure
        this.allAgents = response.agentWithUseCaseDetails.map((agent: any) => {
          // Parse and format the date
          const createdAtDate = new Date(agent.createdAt);
          const formattedDate = `${createdAtDate.getMonth() + 1}/${createdAtDate.getDate()}/${createdAtDate.getFullYear()}`;
  
          return {
            id: agent.id.toString(), // Convert id to string
            title: agent.name, // Map 'name' to 'title'
            tags: [
              { label: agent.org },
              { label: agent.domain },
              { label: agent.project },
              { label: agent.team }
            ], // Map tags in the specified order
            createdDate: formattedDate, // Use the formatted date
            actions: [
              agentsConfigData.actions.execute,
              agentsConfigData.actions.clone,
              agentsConfigData.actions.delete
            ],
          };
        });
  
        this.filteredAgents = [...this.allAgents];
        this.updateDisplayedAgents();
      },
      error: (error) => {
        console.error('Error fetching agents:', error);
      }
    });
  }

  updateDisplayedAgents(): void {
    // Use the pagination service to get displayed items and total pages
    const result = this.paginationService.getPaginatedItems(
      this.filteredAgents,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedAgents = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateAgent(): void {
    console.log('Create Agent clicked');
    this.router.navigate([agentsConfigData.navigation.createRoute]);
  }

  onCardClicked(agentId: string): void {
    console.log(`Agent card clicked: ${agentId}`);
    
    // Find the agent to determine its type
    const agent = this.allAgents.find(a => a.id === agentId);
    if (agent) {
      // Check if it's a collaborative agent
      const isCollaborative = agent.tags.some(tag => tag.label === 'Collaborative');
      
      // Navigate to the appropriate agent page
      const route = isCollaborative ? agentsConfigData.navigation.collaborativeRoute : agentsConfigData.navigation.individualRoute;
      this.router.navigate([route], {
        queryParams: { id: agentId }
      });
    }
  }

  onActionClicked(event: {action: string, cardId: string}): void {
    console.log(`Action ${event.action} clicked for agent: ${event.cardId}`);
    
    if (event.action === 'execute') {
      // Find the agent to determine its type
      const agent = this.allAgents.find(a => a.id === event.cardId);
      if (agent) {
        // Check if it's a collaborative agent
        const isCollaborative = agent.tags.some(tag => tag.label === 'Collaborative');
        
        // Navigate to the appropriate agent page in execute mode
        const route = isCollaborative ? agentsConfigData.navigation.collaborativeRoute : agentsConfigData.navigation.individualRoute;
        this.router.navigate([route], {
          queryParams: { 
            id: event.cardId,
            execute: 'true'
          }
        });
      }
    } else if (event.action === 'clone') {
      // Handle clone action
      console.log(`Cloning agent: ${event.cardId}`);
    } else if (event.action === 'delete') {
      // Handle delete action
      console.log(`Deleting agent: ${event.cardId}`);
    }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: {[key: string]: string}): void {
    // Apply filters to agents
    if (Object.keys(filters).length === 0) {
      this.filteredAgents = [...this.allAgents];
    } else {
      this.filteredAgents = this.filterService.filterData(
        this.allAgents, 
        filters, 
        this.filterPropertyMap
      );
    }
    
    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedAgents();
    
    console.log('Applied filters:', filters);
    console.log('Filtered agents count:', this.filteredAgents.length);
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedAgents();
  }
  
  // Helper to check if we should show the create card (only on first page)
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }
}

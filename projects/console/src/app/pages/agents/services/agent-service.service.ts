import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AgentServiceService {
  private baseUrl = environment.consoleApi;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  }

  constructor(private http: HttpClient) { }

  public getAllAgentList() {
    const url = `${this.baseUrl}/ava/force/agents`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }
}

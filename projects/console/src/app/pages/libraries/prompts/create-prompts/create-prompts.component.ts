import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { AccordionComponent } from '../../../../shared/components/accordion/accordion.component';
import { AccordionItemComponent } from '../../../../shared/components/accordion/accordion-item/accordion-item.component';
import { ChatInterfaceComponent } from '../../../../shared/components/chat-interface/chat-interface.component';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { MOCK_PROMPTS } from '../../../../shared/mock-data/prompt-mock-data';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';
import promptsLabels from '../constants/prompts.json';

@Component({
  selector: 'app-create-prompts',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardComponent,
    FormFieldComponent,
    AccordionComponent,
    AccordionItemComponent,
    ChatInterfaceComponent
  ],
  templateUrl: './create-prompts.component.html',
  styleUrls: ['./create-prompts.component.scss']
})
export class CreatePromptsComponent implements OnInit, OnDestroy {
  // Labels used across the Prompt UI components (titles)
  public promptLabels = promptsLabels.labels;

  // Mode flags
  promptId: string | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  
  promptForm: FormGroup;
  
  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  
  // Subscription
  private executionSubscription: Subscription = new Subscription();
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService
  ) {
    this.promptForm = this.fb.group({
      // Prompt details
      name: [''],
      description: [''],
      
      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      
      // Prompt task
      promptTask: [''],
      
      // Right side
      role: [''],
      backstory: [''],
      goal: [''],
      expectedOutput: [''],
      
      // Examples
      example1Input: [''],
      example1Output: [''],
      
      // Steps
      step1Instruction: ['']
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.promptId = this.route.snapshot.paramMap.get('id');
    const executeParam = this.route.snapshot.queryParamMap.get('execute');
    
    this.isEditMode = !!this.promptId;
    this.isExecuteMode = executeParam === 'true';
    this.showChatInterface = this.isExecuteMode;
    
    if (this.isEditMode && this.promptId) {
      // Load prompt data for editing
      console.log(`Editing prompt with ID: ${this.promptId}`);
      this.loadPromptData(this.promptId);
      
      // If in execute mode, start the execution
      if (this.isExecuteMode) {
        // Initialize messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the prompt testing'
          },
          {
            from: 'user',
            text: 'Test this input'
          },
          {
            from: 'ai',
            text: 'Here is the output'
          }
        ];
        
        // Start execution (after a small delay to ensure UI is ready)
        setTimeout(() => {
          this.toolExecutionService.startExecution(this.promptId!, this.chatMessages);
          
          // Subscribe to execution state changes
          this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {
            if (state.isExecuting && state.toolId === this.promptId) {
              this.chatMessages = state.chatMessages;
            }
          });
        }, 100);
      }
    }
  }
  
  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  onSave(): void {
    console.log('Form data:', this.promptForm.value);
    
    // Get the return page if available
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;
    
    if (this.isEditMode) {
      console.log('Updating existing prompt');
      // this.promptService.updatePrompt(this.promptId, this.promptForm.value);
      this.router.navigate(['/libraries/prompts'], {
        queryParams: { page: pageNumber }
      });
    } else {
      console.log('Creating new prompt');
      // this.promptService.createPrompt(this.promptForm.value);
      this.router.navigate(['/libraries/prompts']);
    }
  }

  onExecute(): void {
    console.log('Executing prompt:', this.promptForm.value);
    if (this.promptId) {
      // If we're already in execute mode with chat interface showing
      if (this.isExecuteMode && this.showChatInterface) {
        // Process the execution
        console.log('Processing execution');
      } else {
        console.log('Entering execute mode, showing chat interface');
        
        // Set flags to show chat interface
        this.isExecuteMode = true;
        this.showChatInterface = true;
        
        // Set the initial messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the prompt testing'
          },
          {
            from: 'user',
            text: 'Test this input'
          },
          {
            from: 'ai',
            text: 'Here is the output'
          }
        ];
        
        // Delay starting the execution service slightly to allow UI to update
        setTimeout(() => {
          console.log('Starting execution service for prompt ID:', this.promptId);
          this.toolExecutionService.startExecution(this.promptId!, this.chatMessages);
        }, 100);
      }
    }
  }

  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode && this.isEditMode) {
      // Return to edit mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
      console.log('Exited execution mode, returning to edit mode');
    } else {
      // Get the return page if available
      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
      const pageNumber = returnPage ? parseInt(returnPage) : 1;
      
      // Exit to prompts list at correct page
      this.router.navigate(['/libraries/prompts'], {
        queryParams: { page: pageNumber }
      });
    }
  }
  
  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.promptForm.get(name) as FormControl;
  }
  
  // Handle file attachment for the prompt task
  handleAttachment(): void {
    console.log('Handling file attachment');
    // Implement file attachment logic here
  }
  
  // Analyze the prompt task
  analyzePrompt(): void {
    console.log('Analyzing prompt:', this.promptForm.get('promptTask')?.value);
    // Implement prompt analysis logic here
  }
  
  // Load prompt data from mock data
  loadPromptData(promptId: string): void {
    // In a real app, this would use data fetched from a service
    const prompt = MOCK_PROMPTS?.find(p => p.id === promptId);
    
    if (prompt) {
      // Set form values based on the prompt data
      this.promptForm.get('name')?.setValue(prompt.title);
      this.promptForm.get('description')?.setValue(`This is the ${prompt.title} description.`);
      
      // Set filter data based on the prompt properties
      this.promptForm.get('organization')?.setValue('Ascendion');
      this.promptForm.get('domain')?.setValue(prompt.department || 'AI Research');
      this.promptForm.get('project')?.setValue(prompt.project || 'Prompt Engineering');
      this.promptForm.get('team')?.setValue('NLP Team');
      
      // Set sample values for other fields
      this.promptForm.get('promptTask')?.setValue('Generate a conversation about AI safety');
      this.promptForm.get('role')?.setValue('AI Assistant');
      this.promptForm.get('goal')?.setValue('Help users understand AI safety concepts');
      this.promptForm.get('backstory')?.setValue('You are an expert in AI safety and ethics');
      this.promptForm.get('expectedOutput')?.setValue('A clear, informative conversation about AI safety');
    }
  }
  
  // Handle chat messages
  handleChatMessage(message: string): void {
    if (this.promptId && this.isExecuteMode) {
      this.isProcessingChat = true;
      
      // Process through the service - it will handle adding user and AI messages
      this.toolExecutionService.processUserMessage(message);
      
      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
}
<div class="create-prompts-container">
  <form [formGroup]="promptForm">
    <div class="form-layout" [ngClass]="{'three-column-layout': isExecuteMode && showChatInterface}">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Prompt Details Card -->
        <app-card customClass="solid-card" [noPadding]="false" [isClickable]="false" [noHoverEffect]="true">
          <div class="card-content">
            <app-form-field label="{{promptLabels.promptName}}" id="promptName"
              [control]="getControl('name')"></app-form-field>

            <app-form-field label="{{promptLabels.description}}" id="description" [control]="getControl('description')"
              type="textarea"></app-form-field>
          </div>
        </app-card>

        <!-- Assign Filters Card -->
        <app-card customClass="solid-card" [noPadding]="false" [isClickable]="false" [noHoverEffect]="true">
          <div class="card-content">
            <h3 class="section-title">{{promptLabels.assignFilters}}</h3>

            <app-form-field label="{{promptLabels.organization}}" id="organization"
              [control]="getControl('organization')"></app-form-field>

            <app-form-field label="{{promptLabels.domain}}" id="domain"
              [control]="getControl('domain')"></app-form-field>

            <app-form-field label="{{promptLabels.project}}" id="project"
              [control]="getControl('project')"></app-form-field>

            <app-form-field label="{{promptLabels.team}}" id="team" [control]="getControl('team')"></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- What do you want the prompt to do section -->
        <app-card customClass="solid-card" [noPadding]="false" [isClickable]="false" [noHoverEffect]="true">
          <div class="card-content agent-task-container">
            <h3 class="section-title">{{promptLabels.promptPurposeLabel}}</h3>

            <div class="input-with-actions">
              <div class="input-analyze-row">
                <app-form-field placeholder="{{promptLabels.placeholder}}" [control]="getControl('promptTask')"
                  type="textarea" [showAttachIcon]="true" attachIconTooltip="{{promptLabels.uploadAttachmentLabel}}"
                  (attachClick)="handleAttachment()" class="workflow-textarea"></app-form-field>

                <button type="button" class="analyze-button" (click)="analyzePrompt()">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                  {{promptLabels.analyze}}
                </button>
              </div>
            </div>
          </div>
        </app-card>

        <!-- Prompt Configuration Card -->
        <app-card customClass="solid-card" [noPadding]="false" [isClickable]="false" [noHoverEffect]="true">
          <div class="card-content">
            <div class="fields-row">
              <div class="field-col">
                <app-form-field label="{{promptLabels.role}}" id="role" [control]="getControl('role')"></app-form-field>
              </div>

              <div class="field-col">
                <app-form-field label="{{promptLabels.goal}}" id="goal" [control]="getControl('goal')"></app-form-field>
              </div>
            </div>

            <app-form-field label="{{promptLabels.backstory}}" id="backstory" [control]="getControl('backstory')"
              type="textarea"></app-form-field>

            <app-form-field label="{{promptLabels.expectedOutput}}" id="expectedOutput"
              [control]="getControl('expectedOutput')" type="textarea"></app-form-field>
          </div>
        </app-card>

        <!-- Optional Sections -->
        <div class="optional-sections">
          <!-- Examples Section -->
          <div class="accordion-section">
            <app-accordion>
              <app-accordion-item title="{{promptLabels.addExamples}}" [isOpen]="false">
                <div class="example-content">
                  <app-form-field label="{{promptLabels.input}}" id="example1Input"
                    [control]="getControl('example1Input')" type="textarea"></app-form-field>

                  <app-form-field label="{{promptLabels.output}}" id="example1Output"
                    [control]="getControl('example1Output')" type="textarea"></app-form-field>
                </div>
              </app-accordion-item>
            </app-accordion>
          </div>

          <!-- Intermediate Steps Section -->
          <div class="accordion-section">
            <app-accordion>
              <app-accordion-item title="{{promptLabels.promptAddSteps}}" [isOpen]="false">
                <div class="step-content">
                  <app-form-field label="{{promptLabels.instruction}}" id="step1Instruction"
                    [control]="getControl('step1Instruction')" type="textarea"></app-form-field>
                </div>
              </app-accordion-item>
            </app-accordion>
          </div>
        </div>

        <!-- Buttons at bottom of middle column -->
        <div class="middle-column-buttons">
          <button type="button" class="exit-button" (click)="onExit()">
            {{promptLabels.exit}}
          </button>
          <!-- Show different buttons based on mode -->
          <ng-container *ngIf="!isEditMode">
            <button type="button" class="save-button" (click)="onSave()">
              {{promptLabels.save}}
            </button>
          </ng-container>
          <ng-container *ngIf="isEditMode && !isExecuteMode">
            <button type="button" class="execute-button" (click)="onExecute()">
              {{promptLabels.execute}}
            </button>
          </ng-container>
          <ng-container *ngIf="isEditMode && isExecuteMode">
            <button type="button" class="execute-button" (click)="onSave()">
              {{promptLabels.update}}
            </button>
          </ng-container>
        </div>
      </div>

      <!-- Right Column (new) - Chat Interface -->
      <div class="chat-column" *ngIf="isExecuteMode && showChatInterface">
        <div class="chat-column-content">
          <app-card customClass="solid-card" [noPadding]="false" [isClickable]="false" [noHoverEffect]="true">
            <div class="card-content">
              <h3 class="section-title">{{promptLabels.playGround}}</h3>
              <h4 class="playground-title">{{promptLabels.promptTesting}}</h4>

              <!-- Chat Interface -->
              <div class="chat-container">
                <app-chat-interface [messages]="chatMessages" [isLoading]="isProcessingChat"
                  (messageSent)="handleChatMessage($event)">
                </app-chat-interface>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  </form>
</div>
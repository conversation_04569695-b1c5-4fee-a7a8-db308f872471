import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { of, map, catchError, Subject, takeUntil } from 'rxjs';

import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';

//import { MOCK_KNOWLEDGE_BASE } from '../../../shared/mock-data/knowledge-base-mock-data';
import { <PERSON>A<PERSON>, CardData } from '../../../shared/models/card.model';
import { FilterConfig } from '../../../shared/models/filter.model';
import { FilterService } from '../../../shared/services/filter.service';
import { PaginationService } from '../../../shared/services/pagination.service';
import { KnowledgeBaseService } from '../../../shared/services/knowledge-base.service';
import { formatToDisplayDate } from '../../../shared/utils/date-utils';
import knowledgeBaseLabels from '../knowledge-base/constants/knowledge-base.json';
import { KNOWLEDGE_BASE_ACTIONS } from './knowledge-base-actions';

@Component({
  selector: 'app-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    CreateCardComponent,
    DataCardComponent,
    FilterBarComponent,
    PageFooterComponent,
    SearchBar
  ],
  templateUrl: './knowledge-base.component.html',
  styleUrls: ['./knowledge-base.component.scss']
})
export class KnowledgeBaseComponent implements OnInit, OnDestroy {
  // Labels used across the Knowledge Base UI components (titles)
  public kbLabels = knowledgeBaseLabels.labels;

  // Data
  allKnowledgeBase: CardData[] = [];
  filteredKnowledgeBase: CardData[] = [];
  displayedKnowledgeBase: CardData[] = [];

  // Filter
  filterConfig!: FilterConfig;
  showFilterBar = false;
  searchTerm = '';

  // Pagination
  currentPage = 1;
  itemsPerPage = 12;

  // Controls loading spinner
  public isLoading: boolean = false;


  // Map filter IDs to data properties for filtering
  private filterPropertyMap: { [key: string]: string } = {
    'userType': 'userType',
    'client': 'client',
    'department': 'department',
    'role': 'role',
    'project': 'project',
    'category': 'category'
  };

  // Used to clean up subscriptions
  private destroy$ = new Subject<void>();

  constructor(
    private filterService: FilterService,
    private knowledgeBaseService: KnowledgeBaseService,
    private paginationService: PaginationService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.fetchAllKnowledge();
  }

  fetchAllKnowledge() {
    this.filterConfig = this.filterService.getFilterConfig('workflows'); // Reuse workflows filter config for now
    this.isLoading = true; // Start loading

    this.knowledgeBaseService.fetchAllKnowledge().pipe(
      map((response: CardData[]) => {
        return response.map((item: any) => {
          const { collectionName, createdDate, ...restWithoutCollection } = item;
          delete restWithoutCollection['collectionName']; // Ensure it's removed from the final object

          const formattedDate = formatToDisplayDate(createdDate);

          const actions: CardAction[] = KNOWLEDGE_BASE_ACTIONS;

          return {
            title: collectionName,
            createdDate: formattedDate,
            actions,
            ...restWithoutCollection
          };
        });
      }),
      catchError((error) => {
        console.error('Error fetching knowledge:', error);
        this.isLoading = false;
        return of([]); // Return empty list on error
      }),
      takeUntil(this.destroy$) // Clean up on component destroy
    ).subscribe({
      next: (res) => {
        this.allKnowledgeBase = res;
        this.filteredKnowledgeBase = [...this.allKnowledgeBase];
        this.updateDisplayedKnowledgeBase();
      },
      error: (err) => {
        console.error('Subscription error:', err);
      },
      complete: () => {
        this.isLoading = false; // End loading
      }
    });
  }

  updateDisplayedKnowledgeBase(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredKnowledgeBase,
      this.currentPage,
      this.itemsPerPage
    );

    this.displayedKnowledgeBase = paginationResult.displayedItems;
  }

  onCreateKnowledgeBase(): void {
    console.log('Create Knowledge Base clicked');
    // Navigate to create knowledge base page
    this.router.navigate(['/libraries/knowledge-base/create']);
  }

  onCardClicked(knowledgeBaseId: string): void {
    console.log('Knowledge Base clicked:', knowledgeBaseId);
    // Navigate to knowledge base details page
    // this.router.navigate([`/launch/libraries/knowledge-base/${knowledgeBaseId}`]);
  }

  onActionClicked(event: { action: string, cardId: string }): void {
    console.log('Action clicked:', event.action, 'on card ID:', event.cardId);
    // Handle different actions (execute, clone, delete)
    if (event.action == 'delete' && this.allKnowledgeBase) {
      const collectionObj = this.allKnowledgeBase.find(a => a.id === event.cardId);
      const collectionName = collectionObj?.title;
      if (collectionName) {
        this.knowledgeBaseService.deleteByCollection(collectionName).subscribe(res => {
          if(res) {
            this.fetchAllKnowledge();
          }
        })
      }
    }
  }

  toggleFilterBar(): void {
    this.showFilterBar = !this.showFilterBar;
  }

  onFilterChange(filters: { [key: string]: string }): void {
    this.filteredKnowledgeBase = this.filterService.filterData(
      this.allKnowledgeBase,
      filters,
      this.filterPropertyMap
    );

    // Reset pagination when filters change
    this.currentPage = 1;
    this.updateDisplayedKnowledgeBase();
  }

  onSearch(term: string): void {
    this.searchTerm = term;
    // Simple search implementation - filter by title containing the search term
    if (term.trim() === '') {
      this.filteredKnowledgeBase = [...this.allKnowledgeBase];
    } else {
      this.filteredKnowledgeBase = this.allKnowledgeBase.filter(item =>
        item.title.toLowerCase().includes(term.toLowerCase())
      );
    }

    // Reset pagination when search term changes
    this.currentPage = 1;
    this.updateDisplayedKnowledgeBase();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedKnowledgeBase();
  }

  // Cleanup: notify subscribers and complete the destroy$ subject
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

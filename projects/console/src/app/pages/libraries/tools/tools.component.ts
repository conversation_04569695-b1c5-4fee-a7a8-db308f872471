import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { Router, ActivatedRoute } from '@angular/router';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { CardData } from '../../../shared/models/card.model';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../../shared/services/filter.service';
import { FilterConfig } from '../../../shared/models/filter.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { ToolsService } from '../../../shared/services/tools.service';
import toolsText from './constants/tools.json';
import { formatToDisplayDate } from '../../../shared/utils/date-utils';

@Component({
  selector: 'app-tools',
  standalone: true,
  imports: [
    CommonModule,
    SearchBar,
    CreateCardComponent,
    DataCardComponent,
    FilterBarComponent,
    PageFooterComponent,
  ],
  templateUrl: './tools.component.html',
  styleUrl: './tools.component.scss'
})
export class ToolsComponent implements OnInit {
  allTools: CardData[] = [];
  filteredTools: CardData[] = [];
  displayedTools: CardData[] = [];
  toolsFilterConfig!: FilterConfig;

  isFilterBarVisible: boolean = false;

  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;

  private filterPropertyMap: {[key: string]: string} = {
    'userType': 'userType',
    'client': 'client',
    'department': 'department',
    'category': 'category',
    'role': 'role',
    'project': 'project'
  };
  public labels: any = toolsText.labels;
  
  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
    private toolsService: ToolsService
  ) {}

  ngOnInit(): void {
    this.toolsFilterConfig = this.filterService.getFilterConfig('tools');
    this.getAllTools();
  }

  transformApiData(apiTools: any[]): CardData[] {
    return apiTools.map(tool => ({
      id: tool.toolId.toString(),
      title: tool.toolName,
      tags: [{ label: tool.toolDescription }],
      createdDate: formatToDisplayDate(tool.createTimestamp),
      actions: [
        { icon: 'execute', action: 'execute', tooltip: 'Execute' },
        { icon: 'delete', action: 'delete', tooltip: 'Delete' }
      ],
      // Add other properties as needed
    }));
  }

  getAllTools(): void {
    this.toolsService.getUserToolsList().subscribe((response: any) => {
      this.allTools = this.transformApiData(response.tools);
      this.filteredTools = [...this.allTools];
      this.updateDisplayedTools();
    });
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
  }

  updateDisplayedTools(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredTools,
      this.currentPage,
      this.itemsPerPage
    );

    this.displayedTools = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateTool(): void {
    console.log('Create Tool clicked');
    this.router.navigate(['/libraries/tools/create']);
  }

  onCardClicked(toolId: string): void {
    console.log(`Tool card clicked: ${toolId}`);
    this.router.navigate(['/libraries/tools/edit', toolId], {
      queryParams: { returnPage: this.currentPage }
    });
  }

  onActionClicked(event: {action: string, cardId: string}): void {
    console.log(`Action ${event.action} clicked for tool: ${event.cardId}`);

    if (event.action === 'execute') {
      console.log(`Executing tool: ${event.cardId}`);
       this.router.navigate(['/libraries/tools/create', event.cardId], {
      queryParams: { execute: 'true', returnPage: this.currentPage }
    });
    } else if (event.action === 'delete') {
 this.toolsService.deleteTool(parseInt(event.cardId, 10)).subscribe(
      response => {
        console.log('Tool deleted successfully', response);

        // Refresh the list of tools after deletion
        this.ngOnInit();
      },
      error => {
        console.error('Error deleting tool', error);
      }
    );
   }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: {[key: string]: string}): void {
    if (Object.keys(filters).length === 0) {
      this.filteredTools = [...this.allTools];
    } else {
      this.filteredTools = this.filterService.filterData(
        this.allTools,
        filters,
        this.filterPropertyMap
      );
    }

    this.currentPage = 1;
    this.updateDisplayedTools();

    console.log('Applied filters:', filters);
    console.log('Filtered tools count:', this.filteredTools.length);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedTools();
    console.log('Page changed to:', this.currentPage);

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
      queryParamsHandling: 'merge'
    });
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }

  fetchTools(): void {
  this.toolsService.getUserToolsList().subscribe((response: any) => {
    this.allTools = response.tools.map((tool: any) => ({
      id: tool.toolId,
      name: tool.toolName,
      description: tool.toolDescription,
    }));
    this.filteredTools = [...this.allTools];
    this.updateDisplayedTools();
  });
}

}

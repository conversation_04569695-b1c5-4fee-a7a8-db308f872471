{"labels": {"toolName": "Tool Name", "toolClassName": "Tool Class Name", "description": "Description", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "toolClassDefinition": "Tool Class Definition", "note": "Note: For any user-defined tools, if additional Python libraries are required to be installed, please contact the AVA+ Core GenAI team.", "exit": "Exit", "save": "Save", "execute": "Execute", "update": "Update", "playground": "Playground", "createNew": "Create Tool", "noTool": "No tools found matching your criteria", "filters": "Filters"}, "TOOL_PLACEHOLDER": {"toolClassDef": "// Enter Code Here\nimport requests; from pydantic import BaseModel, Field\n\nclass EmailToolSchema(BaseModel):\n    \"\"\"Input schema for EmailTool.\"\"\"\n    receiver: str = Field(..., description=\"Email address of the receiver\")\n    subject: str = Field(..., description=\"Subject of the email\")\n    htmlContent: str = Field(..., description=\"Content of the email in html format with html tags like h1, h2, tables, etc.\")"}}
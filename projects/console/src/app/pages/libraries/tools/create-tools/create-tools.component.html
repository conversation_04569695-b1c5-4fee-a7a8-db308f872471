<div class="create-tools-container">
  <form [formGroup]="toolForm">
    <div class="form-layout" [ngClass]="{'three-column-layout': isExecuteMode && showChatInterface}">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Tool Details Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <app-form-field
              label={{labels.toolName}}
              id="toolName"
              [control]="getControl('name')"
            ></app-form-field>

            <app-form-field
              label={{labels.toolClassName}}
              id="toolClassName"
              [control]="getControl('toolClassName')"
            ></app-form-field>

            <app-form-field
              label={{labels.description}}
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- Content wrapper to match left column height -->
        <div class="middle-column-content">
          <!-- Tool Class Definition Card -->
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content">
              <h3 class="section-title">{{labels.toolClassDefinition}}</h3>

              <!-- Code Editor Area -->
              <div class="code-editor-container">
                <textarea
                  formControlName="classDefinition"
                  [placeholder]="placeholder"
                  class="code-editor"
                ></textarea>
              </div>
              <div class="note">{{labels.note}}</div>

              <!-- Buttons at bottom of middle column -->
              <div class="middle-column-buttons">
                <button type="button" class="exit-button" (click)="onExit()">
                 {{labels.exit}}
                </button>
                <!-- Show different buttons based on mode -->
                <ng-container *ngIf="!isEditMode">
                  <button type="button" class="save-button" (click)="onSave()">
                   {{labels.save}}
                  </button>
                </ng-container>
                <ng-container *ngIf="isEditMode && !isExecuteMode">
                  <button type="button" class="execute-button" (click)="onExecute()">
                    {{labels.execute}}
                  </button>
                </ng-container>
                <ng-container *ngIf="isEditMode && isExecuteMode">
                  <button type="button" class="execute-button" (click)="onSave()">
                    {{labels.save}}
                  </button>
                </ng-container>
              </div>
            </div>
          </app-card>
        </div>
      </div>

      <!-- Right Column (new) - Chat Interface -->
      <div class="chat-column" *ngIf="isExecuteMode && showChatInterface">
        <div class="chat-column-content">
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content">
              <h3 class="section-title">{{labels.playground}}</h3>
              <h4 class="playground-title">{{toolForm.get('name')?.value }}</h4>    
              <!-- Chat Interface -->
              <div class="chat-container">
                <app-chat-interface
                  [messages]="chatMessages"
                  [isLoading]="isProcessingChat"
                  (messageSent)="handleChatMessage($event)">
                </app-chat-interface>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  </form>
</div>

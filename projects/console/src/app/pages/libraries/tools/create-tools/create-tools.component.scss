.create-tools-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

// Chat container styles
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  position: relative; // Needed for absolute positioning of child elements
}

.playground-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-primary);
  margin: 0 0 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Make sure chat interface takes full height
app-chat-interface {
height: 100%;
display: flex;
flex-direction: column;
flex: 1;
}

form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding: 20px;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  transition: all 0.3s ease;
  
  &.three-column-layout {
    .left-column {
      width: 25%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 25%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    
    .middle-column {
      width: 40%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 35%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
  }
  
  @media (max-width: 1400px) {
    gap: 16px;
    padding: 16px;
  }
  
  @media (max-width: 1200px) {
    flex-wrap: wrap;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
    flex-direction: column;
  }

  .left-column, .middle-column, .chat-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100vh - 220px);
    
    @media (max-width: 1400px) {
      gap: 16px;
      height: calc(100vh - 200px);
    }
    
    @media (max-width: 1200px) {
      height: auto;
    }
    
    @media (max-width: 576px) {
      gap: 12px;
      width: 100% !important;
    }
  }

  .left-column {
      width: 40%;
      flex-shrink: 0;
      transition: width 0.3s ease;
    
      @media (max-width: 1400px) {
        width: 40%;
      }
    
      @media (max-width: 1200px) {
        width: 100%;
      }
    
    app-card {
      flex-shrink: 0;
    }
    
    // Make first card (Tool Details) fixed height
    app-card:first-of-type {
      flex: 0 0 auto;
    }
    
    // Make second card (Assign Filters) scrollable
    app-card:last-of-type {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .card-content {
        overflow-y: auto;
      }
    }
  }

  .middle-column {
      width: 60%;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      transition: width 0.3s ease;
    
      @media (max-width: 1400px) {
        width: 60%;
      }
    
      @media (max-width: 1200px) {
        width: 100%;
      }
    
    .middle-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
  
  .chat-column {
        width: 35%;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        background-color: var(--agent-chat-column-bg);
        border-left: 1px solid var(--agent-chat-column-border);
        transition: all 0.3s ease;
        box-shadow: -2px 0 10px var(--agent-chat-column-shadow);
    
        @media (max-width: 1400px) {
          width: 35%;
        }
    
        @media (max-width: 1200px) {
          width: 100%;
          border-left: none;
          border-top: 1px solid var(--agent-chat-column-border);
          box-shadow: 0 -2px 10px var(--agent-chat-column-shadow);
        }
    
    .chat-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  @media (max-width: 576px) {
    padding: 12px;
    gap: 12px;
  }
}

// Middle and chat columns need specific height
// Middle and chat columns need specific height
.middle-column .card-content,
.chat-column .card-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Make sure chat card content takes full height
.chat-column .card-content {
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .chat-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 300px;
    height: 100%;
    overflow: hidden;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
  
  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}

.code-editor-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  
  .code-editor {
    font-family: 'Courier New', monospace;
    line-height: 1.5;
    padding: 16px;
    background-color: var(--form-input-bg);
    border: 1px solid var(--form-input-border);
    border-radius: 4px;
    flex: 1;
    min-height: 350px;
    resize: none;
    width: 100%;
    outline: none;
    color: var(--form-input-color);
    
    &:focus {
      border-color: var(--form-input-focus-border);
      box-shadow: 0 0 0 2px var(--form-input-focus-shadow);
    }
  }
}

.middle-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 0 4px;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
    margin-top: auto;
    margin-bottom: 0;
  }
  
  .exit-button, .save-button, .execute-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }
  
  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);
    
    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }
  
  .save-button, .execute-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);
    
    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
  
  .execute-button {
      background: var(--button-gradient);
      transition: all 0.2s ease;
    
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px var(--dashboard-shadow-hover);
      }
    }
}

::ng-deep app-card {
  display: flex;
  flex-direction: column;
  flex: 1;
  
  .card-container {
    background: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
    box-shadow: 0 2px 4px var(--card-shadow) !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-image: none !important;
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
  }
}

app-card ::ng-deep .card-container {
  display: flex;
  flex-direction: column;
  height: 100%;
} 

.note {
    font-weight: 400;
    color: #A3A7C2;
    font-size: 12px;
}
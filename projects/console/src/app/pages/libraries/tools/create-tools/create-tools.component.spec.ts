import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CreateToolsComponent } from './create-tools.component';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { ChatInterfaceComponent } from '../../../../shared/components/chat-interface/chat-interface.component';
import { ToolsService } from '../../../../shared/services/tools.service';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { PromptEnhanceService } from '../../../../shared/services/prompt-enhance.service';
import { RouterTestingModule } from '@angular/router/testing';
import { of, throwError } from 'rxjs';

describe('CreateToolsComponent', () => {
  let component: CreateToolsComponent;
  let fixture: ComponentFixture<CreateToolsComponent>;

  const mockToolsService = {
    getUserToolDetails: jasmine.createSpy('getUserToolDetails').and.returnValue(of({ tools: [{ toolName: 'Test Tool', toolClassName: 'TestClass', toolDescription: 'Test Description', toolClassDef: 'Test Definition' }] })),
    updateUserTool: jasmine.createSpy('updateUserTool').and.returnValue(of({})),
    addNewUserTool: jasmine.createSpy('addNewUserTool').and.returnValue(of({})),
    testTool: jasmine.createSpy('testTool').and.returnValue(of({ status: 'success', output: 'Test Output' }))
  };

  const mockPromptEnhanceService = {
    modelApi: jasmine.createSpy('modelApi').and.returnValue(of({ response: { choices: [{ text: '{"param1": "type1", "param2": "type2"}' }] } }))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CreateToolsComponent, CardComponent, FormFieldComponent, ChatInterfaceComponent],
      imports: [ReactiveFormsModule, FormsModule, RouterTestingModule],
      providers: [
        { provide: ToolsService, useValue: mockToolsService },
        { provide: ToolExecutionService, useValue: {} },
        { provide: PromptEnhanceService, useValue: mockPromptEnhanceService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CreateToolsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Rendering and UI Test Cases
  it('should render three columns when isExecuteMode and showChatInterface are true', () => {
    component.isExecuteMode = true;
    component.showChatInterface = true;
    fixture.detectChanges();
    const columns = fixture.nativeElement.querySelectorAll('.form-layout > div');
    expect(columns.length).toBe(3);
  });

  it('should render two columns when isExecuteMode or showChatInterface is false', () => {
    component.isExecuteMode = false;
    component.showChatInterface = false;
    fixture.detectChanges();
    const columns = fixture.nativeElement.querySelectorAll('.form-layout > div');
    expect(columns.length).toBe(2);
  });

  it('should render all form fields', () => {
    const formFields = fixture.nativeElement.querySelectorAll('app-form-field');
    expect(formFields.length).toBe(3);
  });

  // Interaction Test Cases
  it('should update form values on input', () => {
    const input = fixture.nativeElement.querySelector('input[id="toolName"]');
    input.value = 'New Tool Name';
    input.dispatchEvent(new Event('input'));
    fixture.detectChanges();
    expect(component.toolForm.get('name').value).toBe('New Tool Name');
  });

  it('should call onExit when exit button is clicked', () => {
    spyOn(component, 'onExit');
    const exitButton = fixture.nativeElement.querySelector('.exit-button');
    exitButton.click();
    expect(component.onExit).toHaveBeenCalled();
  });

  it('should call onSave when save button is clicked', () => {
    component.isEditMode = false;
    fixture.detectChanges();
    spyOn(component, 'onSave');
    const saveButton = fixture.nativeElement.querySelector('.save-button');
    saveButton.click();
    expect(component.onSave).toHaveBeenCalled();
  });

  it('should call onExecute when execute button is clicked', () => {
    component.isEditMode = true;
    component.isExecuteMode = false;
    fixture.detectChanges();
    spyOn(component, 'onExecute');
    const executeButton = fixture.nativeElement.querySelector('.execute-button');
    executeButton.click();
    expect(component.onExecute).toHaveBeenCalled();
  });

  // Service Interaction Test Cases
  it('should load tool data when in edit mode', () => {
    component.isEditMode = true;
    component.toolId = 1;
    component.ngOnInit();
    expect(mockToolsService.getUserToolDetails).toHaveBeenCalledWith(1);
    expect(component.toolForm.value.name).toBe('Test Tool');
  });

  it('should extract parameters and prompt for input', () => {
    component.isExecuteMode = true;
    component.toolForm.patchValue({ classDefinition: 'Test Definition', toolClassName: 'TestClass' });
    component.extractParameters();
    expect(component.parameterState.parameters.length).toBe(2);
    expect(component.chatMessages.some(msg => msg.text.includes('parameter'))).toBeTruthy();
  });

  it('should execute tool with parameters', fakeAsync(() => {
    component.isExecuteMode = true;
    component['parameterState'] = { parameters: [], currentParameterIndex: 0, collectedInputs: { param1: 'value1', param2: 'value2' }, isCollecting: false };
    component.executeToolWithParameters();
    tick();
    expect(component.chatMessages.some(msg => msg.text.includes('Test Output'))).toBeTruthy();
  }));

  // Error Handling Test Cases
  it('should handle errors during tool execution', fakeAsync(() => {
    mockToolsService.testTool.and.returnValue(throwError({ error: { message: 'Execution error' } }));
    component.isExecuteMode = true;
    component['parameterState'] = { parameters: [], currentParameterIndex: 0, collectedInputs: {}, isCollecting: false };
    component.executeToolWithParameters();
    tick();
    expect(component.chatMessages.some(msg => msg.text.includes('Execution error'))).toBeTruthy();
  }));

  it('should handle invalid parameter input', () => {
    component.isExecuteMode = true;
    component['parameterState'] = { parameters: [{ name: 'param1', type: 'number' }], currentParameterIndex: 0, collectedInputs: {}, isCollecting: true };
    component.handleChatMessage('invalid');
    expect(component.chatMessages.some(msg => msg.text.includes('Invalid number format'))).toBeTruthy();
  });

  it('should handle form submission for saving a tool', () => {
    spyOn(component, 'onSave').and.callThrough();
    component.toolForm.patchValue({ name: 'New Tool', description: 'New Description', toolClassName: 'NewClass', classDefinition: 'New Definition' });
    component.onSave();
    expect(mockToolsService.addNewUserTool).toHaveBeenCalled();
  });

  it('should reset parameter state on exit', () => {
    component.isExecuteMode = true;
    component.isEditMode = true;
    component['parameterState'] = { parameters: [{ name: 'param1', type: 'type1' }], currentParameterIndex: 0, collectedInputs: { param1: 'value1' }, isCollecting: true };
    component.onExit();
    expect(component['parameterState'].parameters.length).toBe(0);
  });

  it('should navigate to tools library on exit when not in edit mode', () => {
    spyOn(component['router'], 'navigate');
    component.isEditMode = false;
    component.onExit();
    expect(component['router'].navigate).toHaveBeenCalledWith(['/libraries/tools'], { queryParams: { page: 1 } });
  });
});

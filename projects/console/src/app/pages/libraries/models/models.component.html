<div class="models-container">
  <!-- Header section -->
  <div class="models-header">
    <div class="header-content">
      <div class="search-section">
        <app-search-bar></app-search-bar>
      </div>
      <div class="action-buttons">
        <button
          class="action-button"
          [class.active-filter]="isFilterBarVisible"
          (click)="toggleFilterBar()"
          [disabled]="isLoading"
        >
          Filters
          <!-- Down arrow when closed, Up arrow when open -->
          <svg
            *ngIf="!isFilterBarVisible"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 10L12 15L17 10"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <svg
            *ngIf="isFilterBarVisible"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 15L12 10L17 15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Filter Bar -->
  <div class="filter-section" *ngIf="isFilterBarVisible && !isLoading">
    <app-filter-bar
      [filterConfig]="modelFilterConfig"
      (filterChange)="onFilterChange($event)"
    >
    </app-filter-bar>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Loading models...</p>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="error && !isLoading">
    <div class="error-message">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
        <line x1="12" y1="8" x2="12" y2="12" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
        <line x1="12" y1="16" x2="12.01" y2="16" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
      </svg>
      <h3>Failed to Load Models</h3>
      <p>{{ error }}</p>
      <button class="retry-button" (click)="retryFetch()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 4v6h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Retry
      </button>
    </div>
  </div>

  <!-- Content Container -->
  <div class="cards-container" *ngIf="!isLoading && !error">
    <!-- Create Model Card - show only on first page -->
    <app-create-card
      *ngIf="showCreateCard"
      label="Create Model"
      (cardClick)="onCreateModel()"
    >
    </app-create-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredModels.length === 0">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="11" cy="11" r="8" stroke="#6b7280" stroke-width="2"/>
        <path d="M21 21l-4.35-4.35" stroke="#6b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <h3>No models found</h3>
      <p>No models found matching your criteria. Try adjusting your filters or create a new model.</p>
    </div>

    <!-- Model Data Cards - Now showing paginated cards -->
    <app-data-card
      *ngFor="let model of displayedModels"
      [data]="model"
      (cardClicked)="onCardClicked($event)"
      (actionClicked)="onActionClicked($event)"
    >
      <!-- Tags -->
      <div class="tags">
        <span class="tag">Model Name: {{ model.model }}</span>
        <span class="tag">Model Type: {{ model.modelType }}</span>
        <span class="tag">AI Engine: {{ model.aiEngine }}</span>
      </div>

      <!-- Buttons -->
      <div class="buttons">
        <button (click)="onActionClicked({action: 'edit', cardId: model.id})">Edit</button>
        <button (click)="onActionClicked({action: 'delete', cardId: model.id})">Delete</button>
        <button (click)="onActionClicked({action: 'duplicate', cardId: model.id})">Duplicate</button>
      </div>
    </app-data-card>
  </div>

  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredModels.length > 0 && !isLoading && !error"
    [totalItems]="filteredModels.length + (showCreateCard ? 1 : 0)"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  ></app-page-footer>
</div>

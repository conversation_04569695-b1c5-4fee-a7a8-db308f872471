import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CreateModelsComponent } from './create-models.component';
import { ModelService } from '../../../../shared/services/model.service';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs';

describe('CreateModelsComponent', () => {
  let component: CreateModelsComponent;
  let fixture: ComponentFixture<CreateModelsComponent>;
  let modelService: ModelService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CreateModelsComponent],
      imports: [ReactiveFormsModule, FormsModule, RouterTestingModule, HttpClientTestingModule],
      providers: [ModelService]
    }).compileComponents();

    fixture = TestBed.createComponent(CreateModelsComponent);
    component = fixture.componentInstance;
    modelService = TestBed.inject(ModelService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load dropdown options on init', () => {
    spyOn(modelService, 'getDropdownOptions').and.returnValue(of([{ value: 'test', label: 'Test' }]));
    component.ngOnInit();
    expect(component.aiEngines.length).toBeGreaterThan(0);
  });

  it('should display correct input fields based on AI engine selection', () => {
    component.modelForm.get('aiEngine')?.setValue('AzureOpenAI');
    expect(component.inputVisibility.AzureOpenAI).toBeTrue();
  });

  it('should update model name options based on AI engine selection', () => {
    component.aiEngines = [{ value: 'AzureOpenAI', label: 'Azure OpenAI' }];
    component.modelDropdownOptions = [{ value: 'model1', label: 'Model 1' }];
    component.modelForm.get('aiEngine')?.setValue('AzureOpenAI');
    expect(component.modelNames.length).toBeGreaterThan(0);
  });

  it('should save model successfully', () => {
    spyOn(modelService, 'saveModel').and.returnValue(of({}));
    component.modelForm.setValue({
      name: 'Test Model',
      modelDeploymentName: 'Test Model',
      description: 'Test Description',
      modelType: 'Generative',
      aiEngine: 'AzureOpenAI',
      model: 'model1',
      modelName: 'model1',
      // Fill other required fields
    });
    component.onSave();
    expect(modelService.saveModel).toHaveBeenCalled();
  });
});

.create-models-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 20px;
  flex: 1;
  overflow: hidden;
  align-items: stretch;
  height: 100%;
  overflow-y: auto;
  
  @media (max-width: 1400px) {
    gap: 20px;
  }
  
  @media (max-width: 1200px) {
    gap: 16px;
    padding: 16px;
  }
  
  @media (max-width: 992px) {
    flex-direction: column;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }

  .left-column, .right-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100vh - 180px);
    max-height: 800px;
    
    @media (max-width: 1200px) {
      gap: 16px;
      height: calc(100vh - 160px);
    }
    
    @media (max-width: 992px) {
      width: 100%;
      height: auto;
      max-height: none;
    }
    
    @media (max-width: 576px) {
      gap: 12px;
    }
  }

  .left-column {
    width: 23%;
    flex-shrink: 0;
    overflow-y: auto;
    
    @media (max-width: 1400px) {
      width: 30%;
    }
    
    @media (max-width: 1200px) {
      width: 40%;
    }
    
    app-card {
      flex-shrink: 0;
    }
    
    app-card:first-of-type {
      flex: 0 0 auto;
    }
    
    app-card:last-of-type {
      flex: 1;
    }
  }

  .right-column {
    width: 77%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    
    @media (max-width: 1400px) {
      width: 70%;
    }
    
    @media (max-width: 1200px) {
      width: 60%;
    }
  }
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  
  @media (max-width: 576px) {
    padding: 12px;
    gap: 12px;
  }
}

.section-title, .selection-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
  
  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}

.selection-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

// Model selection container styling
.model-selection-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-bottom: 24px;
  
  @media (max-width: 992px) {
    flex-direction: column;
    gap: 16px;
  }
}

.engine-selection,
.model-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// Dropdown styling
.dropdown-container {
  margin-top: 8px;
}

.dropdown-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--form-input-border);
  border-radius: 8px;
  background-color: var(--form-input-bg);
  font-size: 14px;
  color: var(--form-input-color);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
  
  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border);
  }
}

// Parameters container
.parameters-container {
  margin-top: 16px;
}

// Parameter form styling
.parameter-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 16px;
}

.param-row {
  display: flex;
  flex-direction: row;
  gap: 24px;
  width: 100%;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.param-field {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 0 0 50%;
  width: 100%;
  margin-right: 4%;
}

.param-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.param-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--form-label-color);
}

.param-input {
  padding: 10px 12px;
  border: 1px solid var(--form-input-border);
  border-radius: 6px;
  font-size: 14px;
  background-color: var(--form-input-bg);
  color: var(--form-input-color);
  
  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border);
  }
}

// Removing the previous dropdown styling that's no longer needed
.dropdown, .dropdown-selected, .dropdown-icon, .dropdown-arrow {
  display: none;
}

// Radio buttons
.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-label {
  font-size: 14px;
  margin-left: 4px;
  color: var(--form-label-color);
}

// Bottom buttons
.right-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px;
  margin-top: auto;
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }
  
  .exit-button, .save-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }
  
  awe-button button span{
    color:white;
  }

  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);
    
    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }
  
  .save-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);
    
    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
}

::ng-deep app-card {
  display: flex;
  flex-direction: column;
  flex: 1;
  
  .card-container {
    background: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
    box-shadow: 0 2px 4px var(--card-shadow) !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-image: none !important;
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
  }
}

// Empty state styling
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140px;
  color: var(--text-secondary);
  font-size: 14px;
  font-style: italic;
  text-align: center;
  background-color: var(--agent-tools-empty-bg);
  border-radius: 6px;
  border: 1px dashed var(--agent-tools-empty-border);
} 
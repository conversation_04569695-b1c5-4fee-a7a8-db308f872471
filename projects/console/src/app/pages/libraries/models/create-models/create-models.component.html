<div class="create-models-container">
  <form [formGroup]="modelForm">
    <div class="form-layout">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Model Details Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <app-form-field
              label={{labels.name}}
              id="modelName"
              [control]="getControl('modelDeploymentName')"
            ></app-form-field>

            <app-form-field
              label={{labels.description}}
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Right Column -->
      <div class="right-column">
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <div class="model-selection-container">
              <div class="engine-selection">
                <h3 class="selection-title">{{labels.aiEngine}}</h3>
                <p class="selection-description">
                  Choose the preferred AI Engine
                </p>
                <div class="dropdown-container">
                  <select
                    class="dropdown-select"
                    [formControl]="getControl('aiEngine')"
                  >
                    <option
                      *ngFor="let engine of aiEngines"
                      [value]="engine.value"
                    >
                      {{ engine.label }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="model-selection">
                <h3 class="selection-title">{{labels.modelName}}</h3>
                <p class="selection-description">
                  Choose the preferred Model Engine
                </p>
                <div class="dropdown-container">
                  <select
                    class="dropdown-select"
                    [formControl]="getControl('modelName')"
                  >
                    <option
                      *ngFor="let model of modelNames"
                      [value]="model.value"
                    >
                      {{ model.label }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
            <div class="param-field">
              <label class="param-label">{{labels.modelType}}</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    value="Generative"
                    [formControl]="getControl('modelType')"
                  />
                  <span class="radio-label">{{labels.generative}}</span>
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    value="Embedding"
                    [formControl]="getControl('modelType')"
                  />
                  <span class="radio-label">{{labels.embedding}}</span>
                </label>
              </div>
            </div>
          
            <div class="parameters-container">
              <div class="parameter-form">
                <!-- Azure OpenAI Fields -->
                <div *ngIf="inputVisibility.AzureOpenAI" class="param-row">
                  <div class="param-field">
                    <label class="param-label">{{labels.baseurl}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('baseurl')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.llmDeploymentName}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('llmDeploymentName')"
                      placeholder=""
                    />
                  </div>
                  <div class="param-field">
                    <label class="param-label">{{labels.apiKey}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('apiKey')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.apiVersion}}</label>
                    <select
                      class="dropdown-select"
                      [formControl]="getControl('apiVersion')"
                    >
                      <option
                        *ngFor="let option of apiVersionOptions"
                        [value]="option.value"
                      >
                        {{ option.label }}
                      </option>
                    </select>
                  </div>
                </div>

                <!-- Amazon Bedrock Fields -->
                <div *ngIf="inputVisibility.AmazonBedrock" class="param-row">
                  <div class="param-field">
                    <label class="param-label">{{labels.awsAccessKey}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('awsAccessKey')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.awsSecretKey}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('awsSecretKey')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.awsRegion}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('awsRegion')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.bedrockModelId}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('bedrockModelId')"
                      placeholder=""
                    />
                  </div>
                </div>

                <!-- Google AI Fields -->
                <div *ngIf="inputVisibility.GoogleAI" class="param-row">
                  <div class="param-field">
                    <label class="param-label">{{labels.gcpProjectId}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('gcpProjectId')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.gcpLocation}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('gcpLocation')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.vertexAIEndpoint}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('vertexAIEndpoint')"
                      placeholder=""
                    />
                  </div>
                </div>

                <!-- DaOpenSource AI Fields -->
                <div *ngIf="inputVisibility.DaOpenSourceAI" class="param-row">
                  <div class="param-field">
                    <label class="param-label">{{labels.serviceUrl}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('serviceUrl')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.apiKeyEncoded}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('apiKeyEncoded')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">{{labels.headerName}}</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('headerName')"
                      placeholder=""
                    />
                  </div>
                </div>

                <!-- BNY Fields -->
                <div *ngIf="inputVisibility.BNY" class="param-row">
                  <!-- Add BNY specific fields here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Buttons at bottom of right column -->
          <div class="right-column-buttons">
            <button type="button" class="exit-button" (click)="onExit()">
              Exit
            </button>
            <button type="button" class="save-button" (click)="onSave()">
              {{ isEditMode ? "Update" : "Save" }}
            </button>
          </div>
        </app-card>
      </div>
    </div>
  </form>
</div>

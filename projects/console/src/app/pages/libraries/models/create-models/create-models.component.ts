import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { HttpClient } from '@angular/common/http';
import { ModelService } from '../../../../shared/services/model.service';
import { ButtonComponent } from '@awe/play-comp-library';
import  modelText from '../constants/models.json';

@Component({
  selector: 'app-create-models',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardComponent,
    FormFieldComponent,
    
  ],
  templateUrl: './create-models.component.html',
  styleUrls: ['./create-models.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CreateModelsComponent implements OnInit {
  modelId: string | null = null;
  isEditMode: boolean = false;
  modelForm: FormGroup;
  aiEngines: { value: string, label: string }[] = [];
  modelDropdownOptions: { value: string, label: string }[] = [];
  amazonDropdownOptions: { value: string, label: string }[] = [];
  googleDropdownOptions: { value: string, label: string }[] = [];
  daDropdownOptions: { value: string, label: string }[] = [];
  bnyDropdownOptions: { value: string, label: string }[] = [];
  apiVersionOptions: { value: string, label: string }[] = [];
  modelNames: { value: string, label: string }[] = [];

  public inputVisibility = {
    AzureOpenAI: false,
    AmazonBedrock: false,
    GoogleAI: false,
    DaOpenSourceAI: false,
    BNY: false,
  };

  public mode: 'view' | 'add' = 'add';
  public labels : any=modelText.labels;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private modelService: ModelService
  ) {
    this.modelForm = this.fb.group({
      name: [''],
      modelDeploymentName: [''],
      description: [''],
      modelType: ['Generative'],
      model: [''],
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      baseurl: [''],
      llmDeploymentName: [''],
      apiKey: [''],
      apiVersion: [''],
      awsAccessKey: [''],
      awsSecretKey: [''],
      awsRegion: [''],
      bedrockModelId: [''],
      gcpProjectId: [''],
      gcpLocation: [''],
      vertexAIEndpoint: [''],
      serviceUrl: [''],
      apiKeyEncoded: [''],
      headerName: [''],
      aiEngine: [''],
      modelName: ['']
    });
  }

  ngOnInit(): void {
    this.modelId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.modelId;

    // Using the generic method to load all dropdowns
    this.modelService.getDropdownOptions('AI Engine').subscribe({
      next: (options) => this.aiEngines = options,
      error: (error) => console.error('Error fetching AI engines:', error)
    });

    this.modelService.getDropdownOptions('AzureOpenAI Model').subscribe({
      next: (options) => this.modelDropdownOptions = options,
      error: (error) => console.error('Error fetching Azure models:', error)
    });

    this.modelService.getDropdownOptions('AmazonBedrock Model').subscribe({
      next: (options) => this.amazonDropdownOptions = options,
      error: (error) => console.error('Error fetching Amazon models:', error)
    });

    this.modelService.getDropdownOptions('GoogleAI Model').subscribe({
      next: (options) => this.googleDropdownOptions = options,
      error: (error) => console.error('Error fetching Google models:', error)
    });

    this.modelService.getDropdownOptions('DaOpenSourceAI Model').subscribe({
      next: (options) => this.daDropdownOptions = options,
      error: (error) => console.error('Error fetching DaOpenSource models:', error)
    });

    this.modelService.getDropdownOptions('BNY Model').subscribe({
      next: (options) => this.bnyDropdownOptions = options,
      error: (error) => console.error('Error fetching BNY models:', error)
    });

    this.modelService.getDropdownOptions('Api Version', true).subscribe({
      next: (options) => this.apiVersionOptions = options,
      error: (error) => console.error('Error fetching API versions:', error)
    });

    this.modelForm.get('aiEngine')?.valueChanges.subscribe(engine => {
      console.log('Selected AI Engine:', engine);
      this.onAiEngineChange(engine);
    });

    this.modelForm.get('modelName')?.valueChanges.subscribe(modelName => {
      console.log('Selected Model Name:', modelName);
      this.modelForm.patchValue({
        model: modelName,
        name: modelName ? `${modelName} Model` : ''
      }, { emitEvent: false });
    });

    if (this.isEditMode && this.modelId) {
      this.loadModelData(this.modelId);
    }
  }

  loadModelData(modelId: string): void {
    this.modelService.getOneModeById(modelId).subscribe({
      next: (modelData: any) => {
        this.onAiEngineChange(modelData.aiEngine);

        this.modelForm.patchValue({
          name: modelData.modelDeploymentName,
          modelDeploymentName: modelData.modelDeploymentName,
          description: modelData.modelDescription || modelData.description,
          modelType: modelData.modelType,
          aiEngine: modelData.aiEngine,
          model: modelData.model,
          modelName: modelData.model,
          baseurl: modelData.baseurl,
          llmDeploymentName: modelData.llmDeploymentName,
          apiKey: modelData.apiKey,
          apiVersion: modelData.apiVersion,
          awsAccessKey: modelData.awsAccessKey,
          awsSecretKey: modelData.awsSecretKey,
          awsRegion: modelData.awsRegion,
          bedrockModelId: modelData.bedrockModelId,
          gcpProjectId: modelData.gcpProjectId,
          gcpLocation: modelData.gcpLocation,
          vertexAIEndpoint: modelData.vertexAIEndpoint,
          serviceUrl: modelData.serviceUrl,
          apiKeyEncoded: modelData.apiKeyEncoded,
          headerName: modelData.headerName,
          organization: modelData.organization,
          domain: modelData.domain,
          project: modelData.project,
          team: modelData.team
        });

        if (this.mode === 'view') {
          this.modelForm.disable();
        }
      },
      error: (error: any) => {
        console.error('Error loading model data:', error);
      }
    });
  }

  onAiEngineChange(selectedEngine: string) {
    if (selectedEngine) {
      this.displayInputField(selectedEngine);
      this.updateModelNameOptions(selectedEngine);
      this.modelForm.patchValue({
        modelName: '',
        model: ''
      }, { emitEvent: false });
    }
  }

  displayInputField(data: string) {
    Object.keys(this.inputVisibility).forEach(key => {
      this.inputVisibility[key as keyof typeof this.inputVisibility] = false;
    });

    if (this.inputVisibility.hasOwnProperty(data)) {
      this.inputVisibility[data as keyof typeof this.inputVisibility] = true;
    }
  }

  updateModelNameOptions(engine: string): void {
    switch (engine) {
      case 'GoogleAI':
        this.modelNames = [...this.googleDropdownOptions];
        break;
      case 'AmazonBedrock':
        this.modelNames = [...this.amazonDropdownOptions];
        break;
      case 'AzureOpenAI':
        this.modelNames = [...this.modelDropdownOptions];
        break;
      case 'DaOpenSourceAI':
        this.modelNames = [...this.daDropdownOptions];
        break;
      case 'BNY':
        this.modelNames = [...this.bnyDropdownOptions];
        break;
      default:
        this.modelNames = [];
        break;
    }
  }

  onSave(): void {
    if (this.modelForm.valid) {
      const payload = this.modelForm.value;

      if (this.isEditMode && this.modelId) {
        this.modelService.updateModel(this.modelId, payload).subscribe({
          next: (response) => {
            console.log('Model updated successfully:', response);
            this.router.navigate(['/libraries/models']);
          },
          error: (error) => {
            console.error('Error updating model:', error);
          }
        });
      } else {
        this.modelService.saveModel(payload).subscribe({
          next: (response) => {
            console.log('Model saved successfully:', response);
            this.router.navigate(['/libraries/models']);
          },
          error: (error) => {
            console.error('Error saving model:', error);
          }
        });
      }
    } else {
      console.log('Form is invalid');
      this.markFormGroupTouched();
    }
  }

  markFormGroupTouched() {
    Object.keys(this.modelForm.controls).forEach(key => {
      const control = this.modelForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    if (this.mode === 'add') {
      this.modelForm.reset({
        aiEngine: '',
        modelType: 'Generative'
      });
      Object.keys(this.inputVisibility).forEach(key => {
        this.inputVisibility[key as keyof typeof this.inputVisibility] = false;
      });
    }
    this.router.navigate(['/libraries/models']);
  }

  onExit(): void {
    this.router.navigate(['/libraries/models']);
  }

  getControl(name: string): FormControl {
    return this.modelForm.get(name) as FormControl;
  }

  get formControls() {
    return this.modelForm.controls;
  }

  get aiEngine() {
    return this.formControls['aiEngine'];
  }

  get shouldDisplayInput(): boolean {
    return this.aiEngine.value === 'AzureOpenAI' ||
           this.aiEngine.value === 'AmazonBedrock' ||
           this.aiEngine.value === 'GoogleAI' ||
           this.aiEngine.value === 'DaOpenSourceAI' ||
           this.aiEngine.value === 'BNY' ||
           this.mode === 'view';
  }
}

<div class="workflows-container">
  <!-- Header section -->
  <div class="workflows-header">
    <div class="header-content">
      <div class="search-section">
        <app-search-bar></app-search-bar>
      </div>
      <div class="action-buttons">
        <button class="action-button" [class.active-filter]="isFilterBarVisible" (click)="toggleFilterBar()">
          {{workFlowLabels.filters}} 
          <!-- Down arrow when closed, Up arrow when open -->
          <svg *ngIf="!isFilterBarVisible" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <svg *ngIf="isFilterBarVisible" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 15L12 10L17 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Filter Bar -->
  <div class="filter-section" *ngIf="isFilterBarVisible">
    <app-filter-bar 
      [filterConfig]="workflowFilterConfig"
      (filterChange)="onFilterChange($event)">
    </app-filter-bar>
  </div>
  
  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>{{workFlowLabels.loadingText}}</p>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Workflow Card - show only on first page -->
    <app-create-card 
      *ngIf="showCreateCard"
      label="Create Workflow" 
      (cardClick)="onCreateWorkflow()">
    </app-create-card>
    
    <!-- No results message -->
    <div class="no-results" *ngIf="filteredWorkflows.length === 0">
      {{workFlowLabels.noResults}}
    </div>
    
    <!-- Workflow Data Cards - Now showing paginated cards -->
    <app-data-card 
      *ngFor="let workflow of displayedWorkflows" 
      [data]="workflow"
      (cardClicked)="onCardClicked($event)"
      (actionClicked)="onActionClicked($event)">
    </app-data-card>
  </div>
  
  <!-- Page Footer with Pagination - use totalPages from the service -->
  <app-page-footer
    *ngIf="filteredWorkflows.length > 0"
    [totalItems]="filteredWorkflows.length + 1" 
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  ></app-page-footer>
</div> 
{"labels": {"pageTitle": "Collaborative Agent", "name": "Name", "description": "Description", "assignFilters": "Assign <PERSON>s", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "agentTask": "What do you want the <PERSON> to do?", "suggestedPrompt": "Suggested Prompt", "role": "Role", "goal": "Goal", "descriptionPrompt": "Description", "backstory": "Backstory", "expectedOutput": "Expected Output", "modelConfiguration": "Model Configuration", "model": "Model", "temperature": "Temperature", "topP": "Top P", "maxRpm": "Max RPM", "maxToken": "<PERSON>", "maxIteration": "Max Iteration", "maxExecutionTime": "Max Execution Time", "knowledgeBase": "Knowledge Base", "retriever": "Retriever", "splitSize": "Split Size", "parentSplitSize": "Parent Split Size", "childSplitSize": "Child Split Size", "tools": "Tools", "uploadedFiles": "Uploaded Files", "collaborators": "Collaborators", "addCollaborator": "Add Collaborator", "removeCollaborator": "Remove Collaborator", "execute": "Execute", "exit": "Exit", "save": "Save", "cancel": "Cancel", "edit": "Edit", "analyze": "Analyze", "enhance": "<PERSON><PERSON>ce", "addNew": "Add New", "delete": "Delete", "choosePrompt": "Choose Prompt", "startWithTemplate": "Start with a Template", "instructionText": "You can use the \"Agent Description\" section to describe the agent's role, goal, and expected output to automatically create agents.", "orText": "or", "attachFileTooltip": "Attach a file or document", "sparkleEmoji": "✨", "noModelSelected": "No model selected. Please choose from the dropdown above.", "tokensUsed": "Tokens used", "uploadNote": "Note: upload only .pdf,.txt,.docx,.pptx,.html,.xlsx file", "dragDropOrUpload": "Drag and Drop your File(s) Or Upload File(s)", "fileName": "File Name", "fileSize": "File Size", "uploadDate": "Upload Date", "action": "Action", "selectedTools": "Selected Tools", "noToolsSelected": "No tools selected. Please choose from the dropdown above.", "noKnowledgebaseSelected": "No knowledge base selected. Please choose from the dropdown above.", "playground": "Playground", "individualAgentTesting": "Individual Agent Testing"}, "placeholders": {"name": "Enter agent name", "description": "Enter agent description", "organization": "Select organization", "domain": "Select domain", "project": "Select project", "team": "Select team", "workflow": "Please describe what you want the agent to do...", "role": "Role", "goal": "Goal", "descriptionPrompt": "Description", "backstory": "Backstory", "expectedOutput": "Expected Output", "model": "Select model", "knowledgeBase": "Select knowledge base", "collaboratorName": "Enter collaborator name", "collaboratorEmail": "Enter collaborator email"}, "options": {"promptOptions": [{"value": "default", "label": "Choose Prompt"}, {"value": "ruby-developer", "label": "Senior <PERSON>"}, {"value": "python-developer", "label": "Python Developer"}, {"value": "data-scientist", "label": "Data Scientist"}, {"value": "frontend-developer", "label": "Frontend Developer"}], "toolOptions": [{"value": "tool1", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "tool2", "label": "File Writer"}, {"value": "tool3", "label": "Code Interpreter"}, {"value": "tool4", "label": "Web Browser"}, {"value": "tool5", "label": "Database Query"}], "retrieverOptions": ["<PERSON><PERSON><PERSON>", "Parent Doc"]}, "defaultValues": {"agentType": "collaborative", "temperature": 0.3, "topP": 0.95, "maxRpm": 0, "maxToken": 4000, "maxIteration": 1, "maxExecutionTime": 30, "selectedRetriever": "<PERSON><PERSON><PERSON>", "splitSize": 0.3, "parentSplitSize": 0.3, "childSplitSize": 0.3, "promptTemplate": "default", "role": "Senior <PERSON>", "goal": "Analyze dependencies and create a comprehensive dependency graph", "description": "This prompt helps you understand and visualize the relationships between different files in your Ruby on Rails project", "backstory": "Understanding the dependency structure of a Rails project is crucial for maintaining, optimizing, and scaling the application. A clear dependency graph helps developers identify potential bottlenecks, circular dependencies, and opportunities for code refactoring, ultimately leading to a more efficient and maintainable codebase.", "expectedOutput": "Only a JSON file containing the complete dependency graph for all Ruby on Rails files in the project."}, "mockData": {"uploadedFiles": [{"name": "documentation.pdf", "type": "pdf", "size": "2.4 MB", "date": "2023-05-21"}, {"name": "api-reference.docx", "type": "docx", "size": "1.8 MB", "date": "2023-05-20"}]}, "validation": {"required": "This field is required", "temperature": {"min": 0, "max": 1}, "topP": {"min": 0, "max": 1}, "maxRpm": {"min": 0}, "maxToken": {"min": 0}, "maxIteration": {"min": 0}, "maxExecutionTime": {"min": 0}}, "navigation": {"backRoute": "/launch/agents"}, "icons": {"edit": "edit-icon", "analyze": "analyze-icon", "enhance": "enhance-icon", "add": "add-icon", "remove": "remove-icon", "execute": "execute-icon", "exit": "exit-icon"}}
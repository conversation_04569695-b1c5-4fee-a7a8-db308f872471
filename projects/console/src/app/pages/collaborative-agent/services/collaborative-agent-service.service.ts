import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CollaborativeAgentServiceService {
  private baseUrl = environment.consoleApi;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  }

  constructor(private http: HttpClient) { }

  public getGenerativeModel() {
    const url = `${this.baseUrl}/ava/force/model?modelType=Generative`
    return this.http.get(url, this.headers).pipe(
      map((Response: any) => {
        return Response;
      })
    )
  }
}
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormArray, FormControl } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { FormFieldComponent } from '../../shared/components/form-field/form-field.component';
import { SelectDropdownComponent, SelectOption } from '../../shared/components/select-dropdown/select-dropdown.component';
import { DataCardComponent } from '../../shared/components/data-card/data-card.component';
import { ChatInterfaceComponent } from '../../shared/components/chat-interface/chat-interface.component';
import { ChatMessage } from '../../shared/components/chat-window/chat-window.component';
import { ToolExecutionService } from '../../shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';
import { RefDataService } from '../../shared/services/ref-data.service';
import { IndividualAgentServiceService } from './services/individual-agent-service.service';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import individualAgentConfig from './constants/individual-agent.json';

@Component({
  selector: 'app-individual-agent',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, CardComponent, FormFieldComponent, SelectDropdownComponent, DataCardComponent, ChatInterfaceComponent],
  templateUrl: './individual-agent.component.html',
  styleUrl: './individual-agent.component.scss'
})
export class IndividualAgentComponent implements OnInit, OnDestroy {
  agentForm: FormGroup;
  showSuggestedPrompt: boolean = false;
  
  // Configuration from JSON
  config = individualAgentConfig;
  
  // Mode flags for execution and chat interface
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  agentId: string | null = null;
  
  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  
  // Subscription
  private executionSubscription: Subscription = new Subscription();
  
  // Options for the prompt dropdown
  promptOptions: SelectOption[] = individualAgentConfig.options.promptOptions;

  // Options for the model dropdown
  modelOptions: SelectOption[] = [];

  // Knowledge Base options
  knowledgeBaseOptions: SelectOption[] = [];

  // Tool options
  toolOptions: SelectOption[] = individualAgentConfig.options.toolOptions;

  // Selected tools
  selectedTools: any[] = [];

  // Retriever options for the knowledge base
  retrieverOptions: string[] = individualAgentConfig.options.retrieverOptions;
  
  // Selected knowledge base and related properties
  selectedKnowledgeBase: string | null = null;
  selectedRetriever: string = individualAgentConfig.defaultValues.selectedRetriever;
  splitSize: number = individualAgentConfig.defaultValues.splitSize;
  parentSplitSize: number = individualAgentConfig.defaultValues.parentSplitSize;
  childSplitSize: number = individualAgentConfig.defaultValues.childSplitSize;
  
  // Mock uploaded files
  uploadedFiles: any[] = individualAgentConfig.mockData.uploadedFiles;

  // Selected model and configuration
  selectedModel: string | null = null;
  temperature: number = individualAgentConfig.defaultValues.temperature;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private toolExecutionService: ToolExecutionService,
    private refDataService: RefDataService,
    private individualAgentService: IndividualAgentServiceService,
    private sharedApiService: SharedApiServiceService
  ) {
    this.agentForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', [Validators.required]],
      agentType: [individualAgentConfig.defaultValues.agentType, [Validators.required]],
      workflow: ['', [Validators.required]],
      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      // Collaborators still kept for potential use later
      collaborators: this.fb.array([this.createCollaborator()]),
      // Suggested prompt form
      promptForm: this.fb.group({
        promptTemplate: [individualAgentConfig.defaultValues.promptTemplate],
        role: [individualAgentConfig.defaultValues.role],
        goal: [individualAgentConfig.defaultValues.goal],
        description: [individualAgentConfig.defaultValues.description],
        backstory: [individualAgentConfig.defaultValues.backstory],
        expectedOutput: [individualAgentConfig.defaultValues.expectedOutput]
      }),
      // Model configuration form
      modelConfig: this.fb.group({
        model: [''],
        temperature: [individualAgentConfig.defaultValues.temperature, [Validators.required, Validators.min(individualAgentConfig.validation.temperature.min), Validators.max(individualAgentConfig.validation.temperature.max)]],
        topP: [individualAgentConfig.defaultValues.topP, [Validators.required, Validators.min(individualAgentConfig.validation.topP.min), Validators.max(individualAgentConfig.validation.topP.max)]],
        maxRpm: [individualAgentConfig.defaultValues.maxRpm, [Validators.required, Validators.min(individualAgentConfig.validation.maxRpm.min)]],
        maxToken: [individualAgentConfig.defaultValues.maxToken, [Validators.required, Validators.min(individualAgentConfig.validation.maxToken.min)]],
        maxIteration: [individualAgentConfig.defaultValues.maxIteration, [Validators.required, Validators.min(individualAgentConfig.validation.maxIteration.min)]],
        maxExecutionTime: [individualAgentConfig.defaultValues.maxExecutionTime, [Validators.required, Validators.min(individualAgentConfig.validation.maxExecutionTime.min)]]
      })
    });
  }

  // Getters for form controls as FormControl to resolve TypeScript errors
  get nameControl(): FormControl {
    return this.agentForm.get('name') as FormControl;
  }

  get descriptionControl(): FormControl {
    return this.agentForm.get('description') as FormControl;
  }

  get workflowControl(): FormControl {
    return this.agentForm.get('workflow') as FormControl;
  }

  get organizationControl(): FormControl {
    return this.agentForm.get('organization') as FormControl;
  }

  get domainControl(): FormControl {
    return this.agentForm.get('domain') as FormControl;
  }

  get projectControl(): FormControl {
    return this.agentForm.get('project') as FormControl;
  }

  get teamControl(): FormControl {
    return this.agentForm.get('team') as FormControl;
  }

  get collaborators() {
    return this.agentForm.get('collaborators') as FormArray;
  }

  get promptFormGroup(): FormGroup {
    return this.agentForm.get('promptForm') as FormGroup;
  }

  get modelConfigGroup(): FormGroup {
    return this.agentForm.get('modelConfig') as FormGroup;
  }

  // Getters for prompt form controls
  get roleControl(): FormControl {
    return this.promptFormGroup.get('role') as FormControl;
  }

  get goalControl(): FormControl {
    return this.promptFormGroup.get('goal') as FormControl;
  }

  get descriptionPromptControl(): FormControl {
    return this.promptFormGroup.get('description') as FormControl;
  }

  get backstoryControl(): FormControl {
    return this.promptFormGroup.get('backstory') as FormControl;
  }

  get expectedOutputControl(): FormControl {
    return this.promptFormGroup.get('expectedOutput') as FormControl;
  }

  // Getters for model config controls
  get topPControl(): FormControl {
    return this.modelConfigGroup.get('topP') as FormControl;
  }

  get maxRpmControl(): FormControl {
    return this.modelConfigGroup.get('maxRpm') as FormControl;
  }

  get maxTokenControl(): FormControl {
    return this.modelConfigGroup.get('maxToken') as FormControl;
  }

  get maxIterationControl(): FormControl {
    return this.modelConfigGroup.get('maxIteration') as FormControl;
  }

  get maxExecutionTimeControl(): FormControl {
    return this.modelConfigGroup.get('maxExecutionTime') as FormControl;
  }

  createCollaborator(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      role: ['', Validators.required],
      priority: [0, [Validators.required, Validators.min(0), Validators.max(10)]]
    });
  }

  addCollaborator(): void {
    this.collaborators.push(this.createCollaborator());
  }

  removeCollaborator(index: number): void {
    if (this.collaborators.length > 1) {
      this.collaborators.removeAt(index);
    }
  }

  onSubmit(): void {
    if (this.agentForm.valid) {
      console.log('Form submitted:', this.agentForm.value);
      // Process form data and navigate
      this.router.navigate(['/launch/agents']);
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.agentForm);
    }
  }
  
  // Helper method to mark all controls in a form group as touched
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        const formArray = control as FormArray;
        for (let i = 0; i < formArray.length; i++) {
          if (formArray.at(i) instanceof FormGroup) {
            this.markFormGroupTouched(formArray.at(i) as FormGroup);
          } else {
            formArray.at(i).markAsTouched();
          }
        }
      } else {
        control?.markAsTouched();
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/launch/agents/create']);
  }
  
  ngOnInit(): void {
    this.getRoleList(); // Call the method to populate the dropdown
    this.fetchModelOptions()
    this.fetchKnowledgeBaseOptions();
    // Get agent ID and execution mode from route params
    this.route.queryParams.subscribe(params => {
      this.agentId = params['id'];
      
      // Check if we should start in execute mode
      if (params['execute'] === 'true') {
        this.isExecuteMode = true;
        this.showChatInterface = true;
        
        // Start execution service with the agent ID
        setTimeout(() => {
          // Initialize the chat messages
          this.chatMessages = [
            {
              from: 'ai',
              text: 'Welcome to the Individual Agent. How can I assist you today?'
            }
          ];
          
          this.toolExecutionService.startExecution(this.agentId!, this.chatMessages);
          
          // Subscribe to execution state changes
          this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {
            if (state.isExecuting && state.toolId === this.agentId) {
              this.chatMessages = state.chatMessages;
            }
          });
        }, 100);
      } else {
        // Regular initialization for non-execute mode
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Welcome to the Individual Agent. How can I assist you today?'
          }
        ];
      }
    });
  }

  getRoleList() {
    this.refDataService.getRefdataList('Roles').subscribe({
      next: (options: {value: string, label: string}[]) => {
        this.promptOptions = [...this.promptOptions, ...options]; 
        
      },
      error: (error) => {
        console.error('Error fetching roles:', error);
      }
    });
  }

  fetchModelOptions(): void {
    this.sharedApiService.getConfigLabels().subscribe({
      next: (response) => {
        if (response && response.categoryLabels) {
          const modelCategory = response.categoryLabels.find((category: any) => category.categoryName === 'Model');
  
          if (modelCategory) {
            const modelLabel = modelCategory.labels.find((label: any) => label.labelName === 'Model');
  
            if (modelLabel && modelLabel.labelValues) {
              const labelValues = modelLabel.labelValues.split(';');
              this.modelOptions = labelValues.map((value: string) => {
                const [id, name] = value.split('=');
                return { value: id, label: name };
              });

              console.log('Fetched model options:', this.modelOptions);
            }
          }
        } else {
          console.error('Unexpected response structure:', response);
        }
      },
      error: (error) => {
        console.error('Error fetching model options:', error);
      }
    });
  }
  
  
  fetchKnowledgeBaseOptions(): void {
    this.sharedApiService.getConfigLabels().subscribe({
      next: (response) => {
        // Check if the response has the expected structure
        if (response && response.categoryLabels) {
          // Find the "In Context Learning (ICL)" category
          const iclCategory = response.categoryLabels.find((category: any) => category.categoryName === 'In Context Learning (ICL)');
  
          if (iclCategory) {
            // Find the "Knowledge Base" label within the category
            const knowledgeBaseLabel = iclCategory.labels.find((label: any) => label.labelName === 'Knowledge Base');
  
            if (knowledgeBaseLabel && knowledgeBaseLabel.labelValues) {
              // Parse the labelValues string to extract knowledge base options
              const labelValues = knowledgeBaseLabel.labelValues.split(';');
              this.knowledgeBaseOptions = labelValues.map((value: string) => {
                const [id, name] = value.split('=');
                return { value: id, label: name };
              });
  
              // Log the fetched knowledge base options for debugging
              console.log('Fetched knowledge base options:', this.knowledgeBaseOptions);
            }
          }
        } else {
          console.error('Unexpected response structure:', response);
        }
      },
      error: (error) => {
        console.error('Error fetching knowledge base options:', error);
      }
    });
  }
  
  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
    
    // Stop execution if it's running
    if (this.isExecuteMode) {
      this.toolExecutionService.stopExecution();
    }
  }

  // Add a method to handle the analyze action
  analyzeWorkflow(): void {
    if (this.workflowControl.valid && this.workflowControl.value) {
      console.log('Analyzing workflow:', this.workflowControl.value);
      // Toggle visibility of suggested prompt
      this.showSuggestedPrompt = true;
      
      // In a real application, you would call an API to get suggested prompts
      // For now, we're just showing the mock data from our form
    }
  }
  
  // Add method to show template options
  showTemplateOptions(): void {
    // Show the suggested prompt and model sections
    this.showSuggestedPrompt = true;
    
    // Pre-select template option
    this.promptFormGroup.get('promptTemplate')?.setValue('ruby-developer');
    this.onPromptChange('ruby-developer');
    
    // Do not select a default model - let user make their selection
    // Leave selectedModel as null until user explicitly chooses
    this.modelConfigGroup.get('model')?.setValue('default');
    this.selectedModel = null;
  }

  // Handle file attachment
  handleAttachment(): void {
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.txt,.pdf,.doc,.docx,.csv,.json';
    
    // Handle file selection
    fileInput.addEventListener('change', (event: Event) => {
      const files = (event.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        const selectedFile = files[0];
        console.log('File selected:', selectedFile.name);
        
        // Here you would typically upload the file or process it
        // For now, just add the filename to the textarea
        const currentValue = this.workflowControl.value || '';
        const fileReference = `\n\nAttached file: ${selectedFile.name}`;
        this.workflowControl.setValue(currentValue + fileReference);
      }
    });
    
    // Trigger file selection dialog
    fileInput.click();
  }

  // Methods for handling prompt actions
  onPromptChange(value: string | string[]): void {
    // Ensure we're working with a single string value
    const selectedValue = Array.isArray(value) ? value[0] : value;
    console.log('Selected prompt:', selectedValue);
    
    // Set the selected prompt template
    this.promptFormGroup.get('promptTemplate')?.setValue(selectedValue);
    
    // Update prompt fields based on the selected template
    if (selectedValue === 'Software Developer') {
      this.roleControl.setValue('Senior Software Developer');
      this.goalControl.setValue('Analyze dependencies and create a comprehensive dependency graph');
      this.descriptionPromptControl.setValue('This prompt helps you understand and visualize the relationships between different files in your Ruby on Rails project');
      this.backstoryControl.setValue('Understanding the dependency structure of a Rails project is crucial for maintaining, optimizing, and scaling the application. A clear dependency graph helps developers identify potential bottlenecks, circular dependencies, and opportunities for code refactoring, ultimately leading to a more efficient and maintainable codebase.');
      this.expectedOutputControl.setValue('Only a JSON file containing the complete dependency graph for all Ruby on Rails files in the project.');
    } else if (selectedValue === 'Angular Developer') {
      this.roleControl.setValue('Angular Developer');
      this.goalControl.setValue('Analyze Angular code and suggest improvements');
      this.descriptionPromptControl.setValue('This prompt helps identify code quality issues and suggest improvements to Angular codebases');
      this.backstoryControl.setValue('Maintaining high code quality is essential for long-term maintainability and reducing technical debt. Having an expert review code can identify patterns, anti-patterns, and opportunities for performance improvements.');
      this.expectedOutputControl.setValue('A comprehensive analysis report highlighting issues and suggested improvements with code examples.');
    }
  }
  
  editPrompt(): void {
    console.log('Edit prompt clicked');
    // Implement edit functionality
  }
  
  enhanceBackstory(): void {
    console.log('Enhance backstory clicked');
    // Implement enhance functionality
    const currentBackstory = this.promptFormGroup.get('backstory')?.value;
    // Simulating an enhanced backstory (in real app, this would come from an API)
    const enhancedBackstory = currentBackstory + ' Additionally, understanding dependencies is vital for security audits and ensuring proper isolation of components.';
    this.promptFormGroup.get('backstory')?.setValue(enhancedBackstory);
  }
  
  enhanceOutput(): void {
    console.log('Enhance output clicked');
    // Implement enhance functionality
    const currentOutput = this.promptFormGroup.get('expectedOutput')?.value;
    // Simulating an enhanced output (in real app, this would come from an API)
    const enhancedOutput = currentOutput + ' The JSON should be formatted with clear parent-child relationships and include metadata about each file.';
    this.promptFormGroup.get('expectedOutput')?.setValue(enhancedOutput);
  }

  // Methods for handling model configuration
  onModelChange(value: string | string[]): void {
    if (!value) return;
    
    // Ensure we're working with a single string value
    const selectedValue = Array.isArray(value) ? value[0] : value;
    console.log('Selected model:', selectedValue);
    this.selectedModel = selectedValue;
    this.modelConfigGroup.get('model')?.setValue(selectedValue);
    
    // Update model parameters based on default values for the selected model
    if (selectedValue === 'claude-sonnet') {
      this.temperature = 0.3;
      this.topPControl.setValue(0.95);
      this.maxRpmControl.setValue(0);
      this.maxTokenControl.setValue(4000);
      this.maxIterationControl.setValue(1);
      this.maxExecutionTimeControl.setValue(30);
    } else if (selectedValue === 'claude-opus') {
      this.temperature = 0.2;
      this.topPControl.setValue(0.9);
      this.maxRpmControl.setValue(0);
      this.maxTokenControl.setValue(8000);
      this.maxIterationControl.setValue(1);
      this.maxExecutionTimeControl.setValue(30);
    } else if (selectedValue === 'gpt-4') {
      this.temperature = 0.7;
      this.topPControl.setValue(0.95);
      this.maxRpmControl.setValue(0);
      this.maxTokenControl.setValue(4000);
      this.maxIterationControl.setValue(1);
      this.maxExecutionTimeControl.setValue(60);
    }
  }
  
  editModel(): void {
    console.log('Edit model clicked');
    // Implement edit functionality
  }
  
  onTemperatureChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.temperature = parseFloat(value);
    this.modelConfigGroup.get('temperature')?.setValue(this.temperature);
  }

  // Knowledge Base methods
  onKnowledgeBaseChange(value: string | string[]): void {
    if (!value) return;
    
    // Ensure we're working with a single string value
    const selectedValue = Array.isArray(value) ? value[0] : value;
    console.log('Selected knowledge base:', selectedValue);
    this.selectedKnowledgeBase = selectedValue;
    
    // In a real application, this would load the selected knowledge base configuration
  }
  
  addNewKnowledgeBase(): void {
    console.log('Add new knowledge base clicked');
    // Navigate to create knowledge base page or open a modal
    // this.router.navigate(['/libraries/knowledge-base/create']);
  }
  
  selectRetriever(retriever: string): void {
    console.log('Selected retriever:', retriever);
    this.selectedRetriever = retriever;
  }
  
  onSplitSizeChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.splitSize = parseFloat(value);
  }
  
  onParentSplitSizeChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.parentSplitSize = parseFloat(value);
  }
  
  onChildSplitSizeChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.childSplitSize = parseFloat(value);
  }
  
  onFileSelected(event: Event): void {
    const files = (event.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        this.addFileToUploadedFiles(file);
      }
    }
  }
  
  onFileDrop(event: DragEvent): void {
    event.preventDefault();
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        this.addFileToUploadedFiles(file);
      }
    }
  }
  
  private addFileToUploadedFiles(file: File): void {
    // Check file type before adding
    const fileType = this.getFileType(file.name);
    const allowedTypes = ['pdf', 'txt', 'docx', 'pptx', 'html', 'xlsx'];
    
    if (allowedTypes.includes(fileType)) {
      // Add to uploaded files list
      this.uploadedFiles.push({
        name: file.name,
        type: fileType,
        size: this.formatFileSize(file.size),
        date: new Date().toISOString().split('T')[0]
      });
    } else {
      console.error('File type not allowed:', fileType);
      // Show error message
    }
  }
  
  private getFileType(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || '';
  }
  
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
  
  deleteFile(file: any): void {
    const index = this.uploadedFiles.indexOf(file);
    if (index !== -1) {
      this.uploadedFiles.splice(index, 1);
    }
  }

  // Tool methods
  onToolSelect(value: string | string[]): void {
    if (!value) return;
    
    // Reset selected tools if an empty array is provided
    if (Array.isArray(value) && value.length === 0) {
      this.selectedTools = [];
      return;
    }
    
    // Handle single or multiple values
    const selectedValues = Array.isArray(value) ? value : [value];
    
    // Create a new array for selected tools
    this.selectedTools = [];
    
    // Process all selected values and map them to tool objects
    for (const val of selectedValues) {
      // Find the tool option from the value
      const toolOption = this.toolOptions.find(option => option.value === val);
      
      if (toolOption) {
        // Create a tool object with data
        this.selectedTools.push({
          id: val,
          name: toolOption.label,
          tags: ['Built-In', 'Ascendion', 'Platform Engineering'],
          createdDate: '2023-05-15'
        });
      }
    }
    
    console.log('Selected tools:', this.selectedTools);
  }
  
  /**
   * Converts a tool object to a CardData format for the data-card component
   */
  convertToolToCardData(tool: any): any {
    return {
      id: tool.id,
      title: tool.name,
      tags: tool.tags.map((tag: string) => ({ label: tag })),
      createdDate: tool.createdDate,
      actions: [{ action: 'delete', icon: 'delete', tooltip: 'Remove Tool' }]
    };
  }
  
  addNewTool(): void {
    console.log('Add new tool clicked');
    // This would typically navigate to a tool creation page or open a modal
    // this.router.navigate(['/libraries/tools/create']);
  }
  
  removeTool(event: any): void {
    // Handle event from app-data-card which contains action and cardId
    const cardId = event.cardId;
    const index = this.selectedTools.findIndex(t => t.id === cardId);
    if (index !== -1) {
      this.selectedTools.splice(index, 1);
    }
  }
  
  // Methods for execution mode and chat interface
  onExecute(): void {
    console.log('Entering execute mode, showing chat interface');
    
    // Set flags to show chat interface
    this.isExecuteMode = true;
    this.showChatInterface = true;
    
    // Initialize the chat interface
    this.chatMessages = [
      {
        from: 'ai',
        text: 'Hi there! I am your individual agent. How can I help you today?'
      }
    ];
    
    // Start the execution service with agent ID
    if (this.agentId) {
      setTimeout(() => {
        this.toolExecutionService.startExecution(this.agentId!, this.chatMessages);
        
        // Subscribe to execution state changes
        this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {
          if (state.isExecuting && state.toolId === this.agentId) {
            this.chatMessages = state.chatMessages;
          }
        });
      }, 100);
    }
  }
  
  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode) {
      // Return to normal mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
      console.log('Exited execution mode, returning to normal mode');
    } else {
      // Navigate away from the page
      this.router.navigate(['/launch/agents']);
    }
  }
  
  // Handle chat messages
  handleChatMessage(message: string): void {
    if (this.agentId && this.isExecuteMode) {
      this.isProcessingChat = true;
      
      // Process through the service - it will handle adding user and AI messages
      this.toolExecutionService.processUserMessage(message);
      
      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
}

<div class="dashboard-container">
  <!-- Welcome section -->
  <div class="welcome-section">
    <div class="user-greeting">
      <h1 class="greeting-text">Hello {{ currentUser.name }}</h1>
      <div class="welcome-text">
        Welcome to <span class="highlight">Console</span
        ><span class="emoji"> 🛠️</span>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Grid Layout -->
  <div
    class="dashboard-content"
    [class.quick-actions-expanded]="quickActionsExpanded"
  >
    <!-- Quick Actions Panel -->
    <div class="quick-actions-wrapper" [class.expanded]="quickActionsExpanded">
      <div class="quick-actions-toggle" (click)="toggleQuickActions()">
        <div class="toggle-button">
          <svg
            [class.rotate]="quickActionsExpanded"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M5 12h14M12 5l7 7-7 7"
              stroke="#6566CD"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <span *ngIf="quickActionsExpanded">Quick Actions</span>
      </div>

      <!-- Expanded view with text labels -->
      <div class="quick-actions-content" *ngIf="quickActionsExpanded">
        <div class="action-buttons">
          <button
            *ngFor="let action of quickActions"
            class="action-button"
            (click)="navigateTo(action.route)"
          >
            <div class="action-icon">
              <img
                [src]="'/svgs/icons/' + action.icon + '.svg'"
                [alt]="action.label"
                width="24"
                height="24"
              />
            </div>
            <span class="action-label">{{ action.label }}</span>
          </button>
        </div>
      </div>

      <!-- Collapsed view with icons only -->
      <div class="quick-actions-icons" *ngIf="!quickActionsExpanded">
        <button
          *ngFor="let action of quickActions"
          class="icon-button"
          (click)="navigateTo(action.route)"
          [title]="action.label"
        >
          <div class="icon-wrapper">
            <img
              [src]="'/svgs/icons/' + action.icon + '.svg'"
              [alt]="action.label"
              width="24"
              height="24"
            />
          </div>
        </button>
      </div>
    </div>

    <!-- KPI Cards Row -->
    <div class="kpi-cards">
      <!-- Total Agents Card -->
      <app-card [customClass]="'kpi-card'" [noHoverEffect]="true">
        <div class="kpi-card-content">
          <div class="kpi-header">
            <h2 class="kpi-title">Total Agents</h2>
            <div class="kpi-icon">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <mask
                  id="mask0_2859_22522"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                >
                  <rect width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_2859_22522)">
                  <path
                    d="M13.5 2.00003C13.5 2.44403 13.307 2.84303 13 3.11803V5.00003H18C18.7956 5.00003 19.5587 5.3161 20.1213 5.87871C20.6839 6.44132 21 7.20438 21 8.00003V18C21 18.7957 20.6839 19.5587 20.1213 20.1214C19.5587 20.684 18.7956 21 18 21H6C5.20435 21 4.44129 20.684 3.87868 20.1214C3.31607 19.5587 3 18.7957 3 18V8.00003C3 7.20438 3.31607 6.44132 3.87868 5.87871C4.44129 5.3161 5.20435 5.00003 6 5.00003H11V3.11803C10.8135 2.95123 10.6717 2.74042 10.5875 2.5048C10.5033 2.26918 10.4794 2.01625 10.5179 1.76902C10.5564 1.52179 10.6562 1.28813 10.8081 1.08931C10.96 0.890493 11.1592 0.732833 11.3876 0.630687C11.6161 0.528541 11.8664 0.485156 12.1159 0.504481C12.3653 0.523806 12.606 0.605228 12.8159 0.741333C13.0259 0.877437 13.1985 1.0639 13.3179 1.28374C13.4374 1.50359 13.5 1.74982 13.5 2.00003ZM0 10H2V16H0V10ZM24 10H22V16H24V10ZM9 14.5C9.39782 14.5 9.77936 14.342 10.0607 14.0607C10.342 13.7794 10.5 13.3979 10.5 13C10.5 12.6022 10.342 12.2207 10.0607 11.9394C9.77936 11.6581 9.39782 11.5 9 11.5C8.60218 11.5 8.22064 11.6581 7.93934 11.9394C7.65804 12.2207 7.5 12.6022 7.5 13C7.5 13.3979 7.65804 13.7794 7.93934 14.0607C8.22064 14.342 8.60218 14.5 9 14.5ZM16.5 13C16.5 12.6022 16.342 12.2207 16.0607 11.9394C15.7794 11.6581 15.3978 11.5 15 11.5C14.6022 11.5 14.2206 11.6581 13.9393 11.9394C13.658 12.2207 13.5 12.6022 13.5 13C13.5 13.3979 13.658 13.7794 13.9393 14.0607C14.2206 14.342 14.6022 14.5 15 14.5C15.3978 14.5 15.7794 14.342 16.0607 14.0607C16.342 13.7794 16.5 13.3979 16.5 13Z"
                    fill="#14161F"
                  />
                </g>
              </svg>
            </div>
          </div>
          <div class="kpi-value">{{ totalAgents }}</div>
          <div class="kpi-subtitle">
            {{ newAgentsCreated }} New Agents Created
          </div>
        </div>
      </app-card>

      <!-- Total Workflow Card -->
      <app-card [customClass]="'kpi-card'" [noHoverEffect]="true">
        <div class="kpi-card-content">
          <div class="kpi-header">
            <h2 class="kpi-title">Total Workflow</h2>
            <div class="kpi-icon">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <mask
                  id="mask0_2859_22537"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                >
                  <rect width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_2859_22537)">
                  <path
                    d="M21 6.5C21 7.42826 20.6313 8.3185 19.9749 8.97487C19.3185 9.63125 18.4283 10 17.5 10C16.5717 10 15.6815 9.63125 15.0251 8.97487C14.3687 8.3185 14 7.42826 14 6.5C14 5.57174 14.3687 4.6815 15.0251 4.02513C15.6815 3.36875 16.5717 3 17.5 3C18.4283 3 19.3185 3.36875 19.9749 4.02513C20.6313 4.6815 21 5.57174 21 6.5ZM3 6.5C3 4.85 3 4.025 3.513 3.513C4.025 3 4.85 3 6.5 3C8.15 3 8.975 3 9.487 3.513C10 4.025 10 4.85 10 6.5C10 8.15 10 8.975 9.487 9.487C8.975 10 8.15 10 6.5 10C4.85 10 4.025 10 3.513 9.487C3 8.975 3 8.15 3 6.5ZM3 17.5C3 15.85 3 15.025 3.513 14.513C4.025 14 4.85 14 6.5 14C8.15 14 8.975 14 9.487 14.513C10 15.025 10 15.85 10 17.5C10 19.15 10 19.975 9.487 20.487C8.975 21 8.15 21 6.5 21C4.85 21 4.025 21 3.513 20.487C3 19.975 3 19.15 3 17.5ZM14 17.5C14 15.85 14 15.025 14.513 14.513C15.025 14 15.85 14 17.5 14C19.15 14 19.975 14 20.487 14.513C21 15.025 21 15.85 21 17.5C21 19.15 21 19.975 20.487 20.487C19.975 21 19.15 21 17.5 21C15.85 21 15.025 21 14.513 20.487C14 19.975 14 19.15 14 17.5ZM17.5 10V14ZM14 17.5H10ZM10 6.5H14Z"
                    fill="#14161F"
                  />
                  <path
                    d="M17.5 10C18.4283 10 19.3185 9.63125 19.9749 8.97487C20.6313 8.3185 21 7.42826 21 6.5C21 5.57174 20.6313 4.6815 19.9749 4.02513C19.3185 3.36875 18.4283 3 17.5 3C16.5717 3 15.6815 3.36875 15.0251 4.02513C14.3687 4.6815 14 5.57174 14 6.5M17.5 10C16.5717 10 15.6815 9.63125 15.0251 8.97487C14.3687 8.3185 14 7.42826 14 6.5M17.5 10V14M14 6.5H10M10 6.5C10 4.85 10 4.025 9.487 3.513C8.975 3 8.15 3 6.5 3C4.85 3 4.025 3 3.513 3.513C3 4.025 3 4.85 3 6.5C3 8.15 3 8.975 3.513 9.487C4.025 10 4.85 10 6.5 10C8.15 10 8.975 10 9.487 9.487C10 8.975 10 8.15 10 6.5ZM10 17.5C10 15.85 10 15.025 9.487 14.513C8.975 14 8.15 14 6.5 14C4.85 14 4.025 14 3.513 14.513C3 15.025 3 15.85 3 17.5C3 19.15 3 19.975 3.513 20.487C4.025 21 4.85 21 6.5 21C8.15 21 8.975 21 9.487 20.487C10 19.975 10 19.15 10 17.5ZM10 17.5H14M14 17.5C14 15.85 14 15.025 14.513 14.513C15.025 14 15.85 14 17.5 14M14 17.5C14 19.15 14 19.975 14.513 20.487C15.025 21 15.85 21 17.5 21C19.15 21 19.975 21 20.487 20.487C21 19.975 21 19.15 21 17.5C21 15.85 21 15.025 20.487 14.513C19.975 14 19.15 14 17.5 14"
                    stroke="#14161F"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
              </svg>
            </div>
          </div>
          <div class="kpi-value">{{ totalWorkflows }}</div>
          <div class="kpi-subtitle">
            {{ newWorkflowsCreated }} New Workflows Created
          </div>
        </div>
      </app-card>

      <!-- Total Users Card -->
      <app-card [customClass]="'kpi-card'" [noHoverEffect]="true">
        <div class="kpi-card-content">
          <div class="kpi-header">
            <h2 class="kpi-title">Total Users</h2>
            <div class="kpi-icon">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <mask
                  id="mask0_2859_22573"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                >
                  <rect width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_2859_22573)">
                  <path
                    d="M16.1058 20.0481L15.8828 18.7211C15.6443 18.6506 15.4198 18.5584 15.2095 18.4444C14.9993 18.3302 14.7993 18.1872 14.6095 18.0154L13.3422 18.4559L12.6308 17.2461L13.6655 16.4001C13.5937 16.1411 13.5577 15.886 13.5577 15.6346C13.5577 15.3835 13.5937 15.1284 13.6655 14.8694L12.6405 14.0039L13.352 12.7944L14.6095 13.2444C14.7928 13.0725 14.9913 12.9312 15.2048 12.8204C15.4183 12.7094 15.6443 12.6186 15.8828 12.5481L16.1058 11.2214H17.5288L17.7423 12.5481C17.9808 12.6186 18.2067 12.712 18.4202 12.8281C18.6336 12.9441 18.8319 13.0931 19.0152 13.2751L20.273 12.7944L20.9845 14.0346L19.9595 14.9001C20.0313 15.1386 20.0673 15.3886 20.0673 15.6501C20.0673 15.9116 20.0313 16.1616 19.9595 16.4001L20.9943 17.2461L20.2828 18.4559L19.0152 18.0154C18.8256 18.1872 18.6256 18.3302 18.4152 18.4444C18.2051 18.5584 17.9808 18.6506 17.7423 18.7211L17.5288 20.0481H16.1058ZM2.5 19.3079V17.0846C2.5 16.5796 2.62883 16.1223 2.8865 15.7126C3.14417 15.303 3.50383 14.986 3.9655 14.7616C4.79617 14.3476 5.69358 14.0018 6.65775 13.7241C7.62175 13.4466 8.73583 13.3079 10 13.3079H10.2923C10.3794 13.3079 10.4666 13.3181 10.5538 13.3386C10.1666 14.3078 10.0035 15.328 10.0645 16.3991C10.1253 17.4703 10.4308 18.4399 10.9808 19.3079H2.5ZM16.8077 17.5386C17.3321 17.5386 17.7804 17.3524 18.1528 16.9799C18.5253 16.6074 18.7115 16.159 18.7115 15.6346C18.7115 15.1103 18.5253 14.662 18.1528 14.2896C17.7804 13.9171 17.3321 13.7309 16.8077 13.7309C16.2834 13.7309 15.835 13.9171 15.4625 14.2896C15.09 14.662 14.9038 15.1103 14.9038 15.6346C14.9038 16.159 15.09 16.6074 15.4625 16.9799C15.835 17.3524 16.2834 17.5386 16.8077 17.5386ZM10 11.6924C9.03467 11.6924 8.21 11.3504 7.526 10.6664C6.842 9.98255 6.5 9.15788 6.5 8.19238C6.5 7.22705 6.842 6.40238 7.526 5.71838C8.21 5.03438 9.03467 4.69238 10 4.69238C10.9653 4.69238 11.79 5.03438 12.474 5.71838C13.158 6.40238 13.5 7.22705 13.5 8.19238C13.5 9.15788 13.158 9.98255 12.474 10.6664C11.79 11.3504 10.9653 11.6924 10 11.6924Z"
                    fill="#1C1B1F"
                  />
                </g>
              </svg>
            </div>
          </div>
          <div class="kpi-value">{{ totalUsers }}</div>
          <div class="kpi-subtitle">{{ newUsersAdded }} New Used Added</div>
        </div>
      </app-card>
    </div>

    <!-- Data Tables Row -->
    <div class="data-tables-row">
      <!-- User Logs -->
      <app-card [customClass]="'data-card'" [noHoverEffect]="true">
        <div class="data-card-content">
          <h2 class="data-card-title">User Logs</h2>

          <div class="table-container">
            <table class="data-table user-logs-table">
              <thead>
                <tr>
                  <th>Username</th>
                  <th>Security Token</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let user of userLogs">
                  <td>
                    <div class="user-cell">
                      <span class="username">{{ user.username }}</span>
                    </div>
                  </td>
                  <td>{{ user.securityToken }}</td>
                  <td>
                    <span
                      class="status-badge"
                      [ngClass]="user.status.toLowerCase()"
                    >
                      {{ user.status }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="view-more">
            <a href="#">View More</a>
          </div>
        </div>
      </app-card>

      <!-- Most Used Models -->
      <app-card [customClass]="'data-card'" [noHoverEffect]="true">
        <div class="data-card-content">
          <h2 class="data-card-title">Most Used Models</h2>

          <div class="table-container">
            <table class="data-table models-table">
              <thead>
                <tr>
                  <th>Model</th>
                  <th>Publisher</th>
                  <th>Agents Used</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let model of modelUsage">
                  <td>{{ model.name }}</td>
                  <td>
                    <div class="publisher-cell">
                      <span>{{ model.publisher.name }}</span>
                    </div>
                  </td>
                  <td>{{ model.agentsCount }} Agents</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="view-more">
            <a href="#">View More</a>
          </div>
        </div>
      </app-card>
    </div>

    <!-- Approvals Panel -->
    <div class="approvals-panel">
      <app-card [customClass]="'approvals-card'" [noHoverEffect]="true">
        <div class="approvals-content">
          <h2 class="approvals-title">Pending Approvals</h2>

          <div class="approvals-list">
            <div
              *ngFor="let approval of pendingApprovals"
              class="approval-item"
            >
              <div class="approval-info">
                <span class="approval-name">{{ approval.name }}</span>
              </div>
              <button class="approve-button" (click)="approveItem(approval.id)">
                Approve
              </button>
            </div>
          </div>

          <div class="view-more">
            <a href="#">View More</a>
          </div>
        </div>
      </app-card>
    </div>
  </div>

  <!-- Footer -->
  <div class="dashboard-footer">
    <div class="copyright-text">
      © {{ currentYear }} Ascendion. All Rights Reserved.
    </div>
  </div>
</div>

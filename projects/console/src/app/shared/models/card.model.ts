export interface CardTag {
  label: string;
  type?: 'primary' | 'secondary'; 
}

export interface CardAction {
  icon: string;  
  action: string; 
  tooltip?: string;
}

export interface CardData {
  id: string;
  title: string;
  tags: CardTag[]; 
  createdDate: string; 
  actions?: CardAction[]; 
  userType?: string;
  client?: string;
  department?: string;
  role?: string;
  project?: string;
  category?: string; 
  model?: string; 
  modelType?: string; 
  aiEngine?: string;
} 

export interface Model {
    id: number
    modelDeploymentName: string
    model: string
    llmDeploymentName: string
    apiKey: string
    baseurl: string
    apiVersion: string
    modelType: string
}
 
export interface GetModelResponse {
    model:Model
}
 
export interface GetAllModelResponse {
    models: Model[]
}
 
export interface GuardrailParams {
    id?: number;
    mode?: string;
}
 
export interface Guardrail {
    id?: number,
    name: string;
    content: string;
    description: string;
}
 
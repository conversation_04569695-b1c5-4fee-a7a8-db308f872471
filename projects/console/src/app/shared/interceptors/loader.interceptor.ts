import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { LoaderService } from '../services/loader/loader.service';

export const LoaderInterceptor: HttpInterceptorFn = (request, next) => {
  const loaderService = inject(LoaderService);

  const excludedEndpoints = [
    '/auth/login-url',  // SSO login initiation endpoint
    '/auth/logout-url', // SSO logout initiation endpoint
  ];
  const shouldExclude = excludedEndpoints.some(endpoint => request.url.includes(endpoint));

  if (!shouldExclude) {
    loaderService.serviceStarted(request.url);
  }

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      // Handle error but still hide loader
      return throwError(() => error);
    }),
    finalize(() => {
      // Hide loader when request completes (success or error) - only if not excluded
      if (!shouldExclude) {
        loaderService.serviceCompleted(request.url);
      }
    })
  );
};
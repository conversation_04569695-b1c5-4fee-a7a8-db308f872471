import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { of, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SharedApiServiceService {
  private baseUrl = environment.consoleApi;
  private embeddingUrl = environment.consoleEmbeddingApi;
  private configData: any = null;

  private readonly HttpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  }
  constructor(private http: HttpClient) { }

  public getConfigLabels(chatbot: boolean = false) {
    const headers = this.HttpOptions
    const url = `${this.baseUrl}/ava/force/label`;
    let param = new HttpParams()
    if(chatbot) {
      param = param.append('chatBot',chatbot)
    }
    if(this.configData) {
      return of(this.configData);
    } else {
      return this.http.get(url, {params: param, headers: headers.headers}).pipe(
        map((response: any) => {
          this.configData = response;
          return response; 
        })
      )
    }
  }

  public getExistingKnowledgeBaase() {
    const headers =  this.HttpOptions
    const url = `${this.embeddingUrl}/ava/force/knowledge`;
    return this.http.get(url, headers);
}
}

import { TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { PromptsService } from './prompts.service';
import { environment } from '../../../environments/environment';

describe('PromptsService', () => {
  let service: PromptsService;
  let httpController: HttpTestingController;

  const mockResponse = [
    {
      id: '1',
      title: 'PL1 Code Conversion to .Net',
      tags: [
        { label: 'Ascendion' },
        { label: 'Platform Engineering' },
        { label: 'Digital Ascender' },
        { label: 'Revamp Demo' }
      ],
      createdDate: '1/2/2025',
      actions: [
        { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
        { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
      ],
      // Filter properties
      category: 'code-conversion',
      client: 'ascendion',
      department: 'platform-engineering',
      role: 'digital-ascender',
      project: 'revamp-demo'
    },
    {
      id: '2',
      title: 'Upgrade Angular package.json',
      tags: [
        { label: 'Ascendion' },
        { label: 'Platform Engineering' },
        { label: 'Digital Ascender' },
        { label: 'Revamp Demo' }
      ],
      createdDate: '1/2/2025',
      actions: [
        { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
        { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
      ],
      // Filter properties
      category: 'upgrade',
      client: 'ascendion',
      department: 'platform-engineering',
      role: 'digital-ascender',
      project: 'revamp-demo'
    }
  ];

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PromptsService,
        provideHttpClient(),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(PromptsService);
    httpController = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpController.verify(); // Ensures no outstanding requests
  });

  it('should return prompts on success', () => {
    service.fetchAllPrompts().subscribe((res) => {
      expect(res).toEqual(mockResponse);
    });

    const req = httpController.expectOne(`${environment.consoleApi}/ava/force/prompts`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should return empty array on error', () => {
    service.fetchAllPrompts().subscribe((res) => {
      expect(res).toEqual([]); // Fallback
    });

    const req = httpController.expectOne(`${environment.consoleApi}/ava/force/prompts`);
    req.flush(
      { message: 'Network error' },
      { status: 500, statusText: 'Internal Server Error' });
  });

  it('should contain actions with correct properties for each prompt', (done) => {
    service.fetchAllPrompts().subscribe((res) => {
      expect(res.length).toBe(2);

      res.forEach(prompt => {
        expect(prompt.actions).toBeTruthy();
        expect(prompt.actions?.length).toBe(2);

        prompt.actions?.forEach(action => {
          expect(['execute', 'delete']).toContain(action.action);
          expect(['execute', 'delete']).toContain(action.icon);
        });
      });

      done();
    });

    const req = httpController.expectOne(`${environment.consoleApi}/ava/force/prompts`);
    req.flush(mockResponse);
  });

});

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { PromptEnhanceService } from './prompt-enhance.service';
import { environment } from '../../../environments/environment';

describe('PromptEnhanceService', () => {
  let service: PromptEnhanceService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PromptEnhanceService]
    });

    service = TestBed.inject(PromptEnhanceService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify(); // Ensure that there are no outstanding requests
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should send a POST request with correct payload', () => {
    const prompt = 'Test prompt';
    const mode = 'test mode';
    const promptOverride = true;
    const useCaseIdentifier = 'test case';
    const mockResponse = { success: true };

    service.modelApi(prompt, mode, promptOverride, useCaseIdentifier).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.instructionApi}/ava/force/individualAgent/execute`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      prompt: prompt,
      mode: mode,
      promptOverride: promptOverride,
      useCaseIdentifier: useCaseIdentifier,
      userSignature: '<EMAIL>'
    });

    req.flush(mockResponse);
  });

  it('should send a POST request with default parameters', () => {
    const prompt = 'Test prompt';
    const mockResponse = { success: true };

    service.modelApi(prompt).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.instructionApi}/ava/force/individualAgent/execute`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      prompt: prompt,
      mode: '', // Default empty string
      promptOverride: false, // Default false
      useCaseIdentifier: '', // Default empty string
      userSignature: '<EMAIL>'
    });

    req.flush(mockResponse);
  });

  it('should handle errors', () => {
    const prompt = 'Test prompt';
    const errorResponse = new ErrorEvent('Network error', {
      message: 'simulated network error'
    });

    service.modelApi(prompt).subscribe({
      error: (error) => {
        expect(error).toBeTruthy();
      }
    });

    const req = httpMock.expectOne(`${environment.instructionApi}/ava/force/individualAgent/execute`);
    req.error(errorResponse);
  });
});

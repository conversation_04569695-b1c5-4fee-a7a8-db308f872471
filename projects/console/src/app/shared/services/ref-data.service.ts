// ref-data.service.ts
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class RefDataService {
  private baseUrl = environment.consoleApi;
  private readonly HttpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  }

  constructor(private http: HttpClient) { }

  public getRefdataList(refValue: string) {
    const url = `${this.baseUrl}/ava/force/refdata?ref_key=${refValue}`;
    return this.http.get(url, this.HttpOptions).pipe(
      map((res: any) => {
        const dropdownOptions = JSON.parse(res.value);
        const optionsArray = Object.keys(dropdownOptions).map(key => ({ value: key, label: dropdownOptions[key] }));
        return optionsArray;
      })
    );
  }
}

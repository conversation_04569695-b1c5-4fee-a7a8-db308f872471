import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ToolsService } from './tools.service';
import { environment } from '../../../environments/environment';

describe('ToolsService', () => {
  let service: ToolsService;
  let httpMock: HttpTestingController;
  let oldApiServiceUrl = 'https://avaplus-dev.avateam.io/force/platform';


  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ToolsService]
    });

    service = TestBed.inject(ToolsService);
    httpMock = TestBed.inject(HttpTestingController);
  });
  

  afterEach(() => {
    httpMock.verify(); // Ensure that there are no outstanding requests
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch user tools list', () => {
    const mockResponse = { tools: [{ toolId: 1, toolName: 'Tool 1' }] };

    service.getUserToolsList().subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.apiUrlAdmin}/ava/force/userTools`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should fetch user tool details', () => {
    const toolId = 1;
    const mockResponse = { tools: [{ toolId: 1, toolName: 'Tool 1' }] };

    service.getUserToolDetails(toolId).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.apiUrlAdmin}/ava/force/userTools?toolId=${toolId}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should add a new user tool', () => {
    const payload = { toolName: 'New Tool', toolClassName: 'NewClass', toolDescription: 'Description', toolClassDef: 'Definition' };
    const mockResponse = { success: true };

    service.addNewUserTool(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.apiUrlAdmin}/ava/force/userTools`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(payload);
    req.flush(mockResponse);
  });

  it('should update a user tool', () => {
    const payload = { toolId: 1, toolName: 'Updated Tool', toolClassName: 'UpdatedClass', toolDescription: 'Updated Description', toolClassDef: 'Updated Definition' };
    const mockResponse = { success: true };

    service.updateUserTool(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.apiUrlAdmin}/ava/force/userTools`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(payload);
    req.flush(mockResponse);
  });

  it('should delete a tool', () => {
    const toolId = 1;
    const mockResponse = { success: true };

    service.deleteTool(toolId).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.apiUrlAdmin}/ava/force/userTools?toolId=${toolId}`);
    expect(req.request.method).toBe('DELETE');
    req.flush(mockResponse);
  });

  it('should test a tool with parameters', () => {
    const payload = { class_definition: 'Test Definition', class_name: 'TestClass', inputs: {} };
    const mockResponse = { status: 'success', output: 'Test Output' };

    service.testTool(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${oldApiServiceUrl}/pipeline/api/v1/test_tool`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(payload);
    req.flush(mockResponse);
  });
});

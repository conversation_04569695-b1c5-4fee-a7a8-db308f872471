import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoaderService {
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  private inFlightHttpCalls: string[] = [];
  public loaderPull: boolean = true;

  /**
   * Observable to track loading state
   */
  get isLoading$(): Observable<boolean> {
    return this.isLoadingSubject.asObservable();
  }

  /**
   * Get current loading state
   */
  get isLoading(): boolean {
    return this.isLoadingSubject.value;
  }

  /**
   * Show loader
   */
  show(): void {
    this.isLoadingSubject.next(true);
  }

  /**
   * Hide loader
   */
  hide(): void {
    this.isLoadingSubject.next(false);
  }

  /**
   * Add an in-flight HTTP request
   */
  private addInflightHttpRequest(url: string): void {
    if (!this.isAnyHttpRequestPending()) {
      // Show loader when first http request starts
      this.show();
    }
    this.inFlightHttpCalls.push(url);
  }

  /**
   * Remove a completed HTTP request
   */
  private removeCompletedHttpRequest(url: string): void {
    this.inFlightHttpCalls = this.inFlightHttpCalls.filter((inFlightUrl) => inFlightUrl !== url);
    // Hide the loader only if all the requests are completed
    if (!this.isAnyHttpRequestPending()) {
      this.hide();
    }
  }

  /**
   * Check if any HTTP request is pending
   */
  private isAnyHttpRequestPending(): boolean {
    return this.inFlightHttpCalls.length > 0;
  }

  /**
   * Service started - called when a request begins
   */
  serviceStarted(url: string): void {
    if (this.loaderPull) {
      this.addInflightHttpRequest(url);
    }
  }

  /**
   * Service completed - called when a request ends
   */
  serviceCompleted(url: string): void {
    this.removeCompletedHttpRequest(url);
  }

  /**
   * Reset loader state (useful for error handling)
   */
  reset(): void {
    this.inFlightHttpCalls = [];
    this.isLoadingSubject.next(false);
  }
} 
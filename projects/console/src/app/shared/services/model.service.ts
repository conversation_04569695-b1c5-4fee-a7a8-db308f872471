import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { environment } from '../../../environments/environment';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Model, GetAllModelResponse, GetModelResponse } from '../models/card.model';

@Injectable({
  providedIn: 'root',
})
export class ModelService {
  private apiServiceUrl  = environment.consoleApi;
  private readonly HttpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };

  getAllModelList(): Observable<Model[]> {
    const url = `${this.apiServiceUrl }/ava/force/model`
    return this.http.get<GetAllModelResponse>(url, this.HttpOptions).pipe(
      map((response) => {
        return response.models;
      })
    );
  }

  public getDropdownOptions(refKey: string, reverseLabel: boolean = false) {
  const url = `${this.apiServiceUrl }/ava/force/refdata?ref_key=${encodeURIComponent(refKey)}`;
  return this.http.get(url, this.HttpOptions).pipe(
    map((res: any) => {
      const dropdownOptions = JSON.parse(res.value);
      const optionsArray = Object.keys(dropdownOptions).map(key => {
        return reverseLabel
          ? { value: dropdownOptions[key], label: key }
          : { value: key, label: dropdownOptions[key] };
      });
      return optionsArray;
    })
  );
}

  saveModel(payload: Model): Observable<any> {
    const url = `${this.apiServiceUrl }/ava/force/model`
    return this.http.post(url, payload, this.HttpOptions);
  }

  updateModel(id: string, payload: Model): Observable<any> {
    const url = `${this.apiServiceUrl }/ava/force/model?modelId=${id}`;
    return this.http.put(url, payload, this.HttpOptions);
  }

  getOneModeById(id: string | number) {
    const url = `${this.apiServiceUrl }/ava/force/model`
    let param = new HttpParams();
    param = param.append('modelId', id);
    return this.http.get<GetModelResponse>(url, { params: param, headers: this.HttpOptions.headers }).pipe(
      map((response) => {
        return response.model;
      })
    );
  }
  constructor(private http: HttpClient) { }
}

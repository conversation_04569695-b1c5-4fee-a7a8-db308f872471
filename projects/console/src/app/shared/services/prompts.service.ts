import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { catchError, map, Observable, of } from 'rxjs';
import { CardData } from '../../shared/models/card.model';

@Injectable({
  providedIn: 'root'
})

export class PromptsService {
  private apiServiceUrl = environment.consoleApi;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };
  private http = inject(HttpClient); //  Injecting HttpClient

  constructor() { }

  /**
   * Fetches all prompt data from the API
   * 
   * @returns Observable emitting an array of CardData items
   * - On success: returns the fetched data
   * - On error: logs the error and returns an empty array
   */
  fetchAllPrompts(): Observable<CardData[]> {
    const url = `${this.apiServiceUrl}/ava/force/prompts`;
    return this.http.get<CardData[]>(
      url, this.headers  //  Pass headers correctly
    ).pipe(
      map((response: CardData[]) => {
        return response; //  Return the response data
      }),
      catchError((error: any) => {
        console.error('API error:', error); //  Log the API error
        return of([]); //  Fallback: return an empty array on error
      })
    );
  }
}

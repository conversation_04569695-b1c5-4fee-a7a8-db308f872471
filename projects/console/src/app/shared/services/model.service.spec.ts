import { TestBed } from '@angular/core/testing';

import { ModelService } from './model.service';

describe('ModelService', () => {
  let service: ModelService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ModelService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should retrieve all models', () => {
    const mockModels: Model[] = [
      { id: '1', model: 'Model 1', modelType: 'Type 1', aiEngine: 'Engine 1' },
      { id: '2', model: 'Model 2', modelType: 'Type 2', aiEngine: 'Engine 2' }
    ];

    service.getAllModelList().subscribe(models => {
      expect(models.length).toBe(2);
      expect(models).toEqual(mockModels);
    });

    const req = httpMock.expectOne(`${baseUrl}/ava/force/model`);
    expect(req.request.method).toBe('GET');
    req.flush({ models: mockModels });
  });

  it('should retrieve model dropdown options', () => {
    const mockDropdownOptions = [{ value: 'option1', label: 'Option 1' }];

    service.getModelDropdown().subscribe(options => {
      expect(options.length).toBe(1);
      expect(options).toEqual(mockDropdownOptions);
    });

    const req = httpMock.expectOne(`${baseUrl}/ava/force/refdata?ref_key=AzureOpenAI Model`);
    expect(req.request.method).toBe('GET');
    req.flush({ value: JSON.stringify({ option1: 'Option 1' }) });
  });

  it('should save a model', () => {
    const mockModel: Model = { id: '1', model: 'Model 1', modelType: 'Type 1', aiEngine: 'Engine 1' };

    service.saveModel(mockModel).subscribe(response => {
      expect(response).toBeTruthy();
    });

    const req = httpMock.expectOne(`${baseUrl}/ava/force/model`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockModel);
    req.flush({});
  });

  it('should update a model', () => {
    const mockModel: Model = { id: '1', model: 'Model 1', modelType: 'Type 1', aiEngine: 'Engine 1' };

    service.updateModel('1', mockModel).subscribe(response => {
      expect(response).toBeTruthy();
    });

    const req = httpMock.expectOne(`${baseUrl}/ava/force/model?modelId=1`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(mockModel);
    req.flush({});
  });

  it('should retrieve a single model by ID', () => {
    const mockModel: Model = { id: '1', model: 'Model 1', modelType: 'Type 1', aiEngine: 'Engine 1' };

    service.getOneModeById('1').subscribe(model => {
      expect(model).toEqual(mockModel);
    });

    const req = httpMock.expectOne(req => req.params.has('modelId') && req.url === `${baseUrl}/ava/force/model`);
    expect(req.request.method).toBe('GET');
    req.flush({ model: mockModel });
  });
});

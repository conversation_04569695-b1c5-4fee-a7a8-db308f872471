import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { catchError, map, Observable, of } from 'rxjs';
import { CardData } from '../../shared/models/card.model';

@Injectable({
  providedIn: 'root'
})

export class WorkflowService {
  private apiServiceUrl = environment.consoleApi;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };
  private http = inject(HttpClient); //  Injecting HttpClient

  constructor() { }

  /**
   * Fetches all prompt data from the API
   * 
   * @returns Observable emitting an array of CardData items
   * - On success: returns the fetched data
   * - On error: logs the error and returns an empty array
   */
  fetchAllWorkflows(): Observable<CardData[]> {
    const url = `${this.apiServiceUrl}/ava/force/workflow`;
    return this.http.get<CardData[]>(
      url, this.headers  //  Pass headers correctly
    ).pipe(
      map((response: CardData[]) => {
        return response; //  Return the response data
      }),
      catchError((error: any) => {
        console.error('API error:', error); //  Log the API error
        return of([]); //  Fallback: return an empty array on error
      })
    );
  }

  
  /**
 * Deletes a workflow by its ID.
 * @param workflowId The ID of the workflow to delete.
 * @returns Observable of the delete operation result.
 */
  deleteWorkflowById(workflowId: string): Observable<any> {
    console.log(workflowId);
    const url = `${this.apiServiceUrl}/ava/force/workflow?workflowId=${workflowId}`;
    return this.http.delete(url, this.headers);
  }

  /**
 * Fetches the dropdown list options based on the level number and parent level ID.
 * @param levelNum The hierarchical level number to retrieve options for.
 * @param parentLvlId The ID of the parent level to filter the options.
 * @returns Observable of an array of dropdown options with `value` and `label`.
 */

  getDropdownList(levelNum: number, parentLvlId: number) {
    const url = `${this.apiServiceUrl}/ava/force/level?levelNum=${levelNum}&parentLvlId=${parentLvlId}`;
    return this.http.get(url, this.headers).pipe(
      map((res: any) => {
        const optionsArray = (res?.levels || []).map((item: any) => ({
          value: item.levelId,
          label: item.name
        }));
        return optionsArray;
      })
    );
  }

}

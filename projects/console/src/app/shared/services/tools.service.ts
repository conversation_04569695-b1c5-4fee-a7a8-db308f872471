import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { map } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ToolsService {
  private apiServiceUrl = environment.consoleApi;
  private headers = { headers: new HttpHeaders({
    'Content-Type': 'application/json',
  })};
  private oldApiServiceUrl = 'https://avaplus-dev.avateam.io/force/platform';

  constructor(private http: HttpClient) { }
  
  /* GET API to fetch the list of user-defined tools. */
  public getUserToolsList() {
    const url = `${this.apiServiceUrl}/ava/force/userTools`;

    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  public getUserToolDetails(id: number) {
    const url = `${this.apiServiceUrl}/ava/force/userTools?toolId=${id}`;

    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  /* POST API to create a new tool. */
  public addNewUserTool(payload: any) {
    const url = `${this.apiServiceUrl}/ava/force/userTools`;

    return this.http.post(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /* PUT API to update a tool. */
  public updateUserTool(payload: any) {
    const url = `${this.apiServiceUrl}/ava/force/userTools`;

    return this.http.put(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /* DELETE API to Delete a tool. */
  public deleteTool(id: number) {
    const url = `${this.apiServiceUrl}/ava/force/userTools?toolId=${id}`;
    return this.http.delete(url, this.headers);
  }

   /* POST API to test the tool with parameters. */
   public testTool(payload: any) {
    const url = `${this.oldApiServiceUrl}/pipeline/api/v1/test_tool`;

    return this.http.post(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }
}

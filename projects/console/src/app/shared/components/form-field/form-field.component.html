<div class="form-field">
  <label *ngIf="label" [for]="id" class="form-label">{{ label }}</label>

  <ng-container [ngSwitch]="type">
    <!-- Text input -->
    <div class="input-container" *ngSwitchCase="'text'">
      <input
        [id]="id"
        [formControl]="control"
        [placeholder]="placeholder"
        class="form-input"
        [ngClass]="{ 'input-with-icon': rightIcon }"
        type="text"
        [attr.maxlength]="maxlength"
        [attr.readonly]="readonly ? true : null"
        [attr.disabled]="disabled ? true : null"
      />
      <!-- Right icon -->
      <div *ngIf="rightIcon" class="right-icon" [title]="rightIconTooltip" (click)="onRightIconClick($event)">
        <img
          [src]="rightIcon"
          [style.width]="rightIconWidth"
          [style.height]="rightIconHeight"
          [alt]="rightIconTooltip"
          style="cursor: pointer;"
        />
      </div>
    </div>

    <!-- Email input -->
    <div class="input-container" *ngSwitchCase="'email'">
      <input
        [id]="id"
        [formControl]="control"
        [placeholder]="placeholder"
        class="form-input"
        [ngClass]="{ 'input-with-icon': rightIcon }"
        type="email"
        [attr.maxlength]="maxlength"
        [attr.readonly]="readonly ? true : null"
        [attr.disabled]="disabled ? true : null"
      />
      <!-- Right icon -->
      <div *ngIf="rightIcon" class="right-icon" [title]="rightIconTooltip" (click)="onRightIconClick($event)">
        <img
          [src]="rightIcon"
          [style.width]="rightIconWidth"
          [style.height]="rightIconHeight"
          [alt]="rightIconTooltip"
          style="cursor: pointer;"
        />
      </div>
    </div>

    <!-- Password input -->
    <div class="input-container" *ngSwitchCase="'password'">
      <input
        [id]="id"
        [formControl]="control"
        [placeholder]="placeholder"
        class="form-input"
        [ngClass]="{ 'input-with-icon': rightIcon }"
        type="password"
        [attr.maxlength]="maxlength"
        [attr.readonly]="readonly ? true : null"
        [attr.disabled]="disabled ? true : null"
      />
      <!-- Right icon -->
      <div *ngIf="rightIcon" class="right-icon" [title]="rightIconTooltip" (click)="onRightIconClick($event)">
        <img
          [src]="rightIcon"
          [style.width]="rightIconWidth"
          [style.height]="rightIconHeight"
          [alt]="rightIconTooltip"
          style="cursor: pointer;"
        />
      </div>
    </div>

    <!-- Number input -->
    <div class="input-container" *ngSwitchCase="'number'">
      <input
        [id]="id"
        [formControl]="control"
        [placeholder]="placeholder"
        class="form-input"
        [ngClass]="{ 'input-with-icon': rightIcon }"
        type="number"
        [attr.min]="min"
        [attr.max]="max"
        [attr.readonly]="readonly ? true : null"
        [attr.disabled]="disabled ? true : null"
      />
      <!-- Right icon -->
      <div *ngIf="rightIcon" class="right-icon" [title]="rightIconTooltip" (click)="onRightIconClick($event)">
        <img
          [src]="rightIcon"
          [style.width]="rightIconWidth"
          [style.height]="rightIconHeight"
          [alt]="rightIconTooltip"
          style="cursor: pointer;"
        />
      </div>
    </div>

    <!-- Textarea -->
    <div class="textarea-container" *ngSwitchCase="'textarea'">
      <textarea
        [id]="id"
        [formControl]="control"
        [placeholder]="placeholder"
        class="form-textarea"
        [ngClass]="{ 'textarea-with-icon': showAttachIcon }"
        rows="4"
        [attr.maxlength]="maxlength"
        [attr.readonly]="readonly ? true : null"
        [attr.disabled]="disabled ? true : null"
      ></textarea>
      <div
        *ngIf="showAttachIcon"
        class="attach-icon"
        (click)="onAttachClick($event)"
        [title]="attachIconTooltip"
      >
        <svg
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path
            d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66l-9.2 9.19a2 2 0 01-2.83-2.83l8.49-8.48"
          ></path>
        </svg>
      </div>
    </div>

    <!-- Select dropdown -->
    <select
      *ngSwitchCase="'select'"
      [id]="id"
      [formControl]="control"
      class="form-input"
      [attr.disabled]="disabled ? true : null"
    >
      <option value="" disabled selected>{{ placeholder }}</option>
      <option *ngFor="let option of options" [value]="option.value">
        {{ option.label }}
      </option>
    </select>

    <!-- Checkbox -->
    <div *ngSwitchCase="'checkbox'" class="checkbox-container">
      <input
        [id]="id"
        [formControl]="control"
        type="checkbox"
        class="form-checkbox"
        [attr.disabled]="disabled ? true : null"
      />
      <label [for]="id" class="checkbox-label" *ngIf="placeholder">{{
        placeholder
      }}</label>
    </div>
  </ng-container>

  <!-- Hint text -->
  <div *ngIf="hint" class="hint-text">{{ hint }}</div>

  <!-- Error messages -->
  <div
    *ngIf="control && control.invalid && control.touched"
    class="error-message"
  >
    {{ getErrorMessage() }}
  </div>
</div>

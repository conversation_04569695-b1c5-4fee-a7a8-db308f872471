import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormControl } from '@angular/forms';

export interface SelectOption {
  value: string;
  label: string;
}

@Component({
  selector: 'app-select-dropdown',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './select-dropdown.component.html',
  styleUrls: ['./select-dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectDropdownComponent),
      multi: true
    }
  ]
})
export class SelectDropdownComponent implements ControlValueAccessor {
  @Input() options: SelectOption[] = [];
  @Input() placeholder: string = 'Select an option';
  @Input() label: string = '';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() isMultiSelect: boolean = false;
  
  @Output() selectionChange = new EventEmitter<string | string[]>();
  
  isOpen: boolean = false;
  selectedOption: SelectOption | null = null;
  selectedOptions: SelectOption[] = [];
  
  // ControlValueAccessor implementation
  private onChange: any = () => {};
  private onTouched: any = () => {};
  
  writeValue(value: string | string[] | null): void {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      this.selectedOption = null;
      this.selectedOptions = [];
      return;
    }

    if (this.isMultiSelect && Array.isArray(value)) {
      this.selectedOptions = this.options.filter(option => value.includes(option.value));
      this.selectedOption = null;
    } else if (!this.isMultiSelect && typeof value === 'string') {
      this.selectedOption = this.options.find(option => option.value === value) || null;
      this.selectedOptions = [];
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  
  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
  
  toggleDropdown(): void {
    if (!this.disabled) {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.onTouched();
      }
    }
  }
  
selectOption(option: SelectOption, event?: Event): void {
  if (this.isMultiSelect) {
    // Prevent dropdown from closing in multi-select mode
    event?.stopPropagation();

    const isSelected = this.selectedOptions.some(item => item.value === option.value);

    if (isSelected) {
      // Remove the selected option
      this.selectedOptions = this.selectedOptions.filter(item => item.value !== option.value);
    } else {
      // Add the new option
      this.selectedOptions = [...this.selectedOptions, option];
    }

    const selectedValues = this.selectedOptions.map(opt => opt.value);
    this.onChange(selectedValues);
    this.selectionChange.emit(selectedValues);
  } else {
    // Single select mode
    if (this.selectedOption?.value !== option.value) {
      this.selectedOption = option;
      this.onChange(option.value);
      this.selectionChange.emit(option.value);
    }
    this.isOpen = false;
  }
}

  
  isOptionSelected(option: SelectOption): boolean {
    if (this.isMultiSelect) {
      return this.selectedOptions.some(item => item.value === option.value);
    } else {
      return this.selectedOption?.value === option.value;
    }
  }
  
  getSelectedLabel(): string {
    if (this.isMultiSelect) {
      if (this.selectedOptions.length === 0) {
        return this.placeholder;
      } else if (this.selectedOptions.length === 1) {
        return this.selectedOptions[0].label;
      } else {
        return `${this.selectedOptions.length} items selected`;
      }
    } else {
      return this.selectedOption ? this.selectedOption.label : this.placeholder;
    }
  }
  
  closeDropdown(): void {
    this.isOpen = false;
  }
} 
.select-container {
  position: relative;
  width: 100%;
  font-family: sans-serif;
  margin-bottom: 10px;
}

.select-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--dropdown-text);
}

.select-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  border: 1px solid var(--dropdown-border);
  border-radius: 8px;
  background-color: var(--dropdown-bg);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 42px;
  
  &:hover {
    border-color: var(--dropdown-focus-border);
  }
  
  &.open {
    border-color: var(--dropdown-focus-border);
    box-shadow: 0 0 0 2px var(--dropdown-focus-shadow);
  }
  
  &.required {
    border-color: var(--dropdown-required-border);
  }
  
  &.multi-select {
    .selected-value {
      max-width: calc(100% - 20px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.selected-value {
  font-size: 14px;
  color: var(--dropdown-text);
  
  .placeholder {
    color: var(--dropdown-placeholder);
    font-style: italic;
  }
}

.select-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    transition: transform 0.2s ease;
    
    &.open {
      transform: rotate(180deg);
    }
  }
}

.dropdown-menu {
  position: relative;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  background-color: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  border-radius: 8px;
  box-shadow: 0 4px 10px var(--dropdown-shadow);
  z-index: 100;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 10px 14px;
  font-size: 14px;
  color: var(--dropdown-text);
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: var(--dropdown-hover-bg);
  }
  
  &.selected {
    background-color: var(--dropdown-selected-bg);
    color: var(--dropdown-selected-text);
    font-weight: 500;
  }
}

.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  .select-field {
    cursor: not-allowed;
    background-color: var(--dropdown-disabled-bg);
    
    &:hover {
      border-color: var(--dropdown-border);
    }
  }
}

/* Multi-select specific styles */
.option-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.checkbox-container {
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid var(--dropdown-checkbox-border);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &.checked {
    background-color: var(--dropdown-checkbox-bg);
    border-color: var(--dropdown-checkbox-bg);
  }
} 
.table__grid--container {
  width: 100%;
  overflow: auto;
  border:  0.5px solid var(--table-grid-border-color);
  border-radius: 8px;
  background: var(--table-grid-background-color);
  box-shadow: 0 4px 6px -1px var(--table-grid-shadow-color);
  font-size: 14px;
  line-height: 1.5;
  position: relative;
}

.table__grid--wrapper {
  width: fit-content;
  min-width: 100%;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.table__header {
  display: flex;
  background: var(--table-grid-header-background);
  border-bottom: 2px solid var(--table-grid-header-border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Body Styles */
.table__body {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.table__row {
  display: flex;
  border-bottom: 1px solid var(--table-grid-row-border-color);
  transition: background-color 0.2s ease;
}

.table__row:last-child {
  border-bottom: none;
}

.table__row.hoverable:hover {
  background-color: var(--table-grid-row-hover-background);
}

.table__cell {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 44px;
  overflow: hidden;
}

.header-cell {
  font-weight: 600;
  color: var(--table-grid-header-text-color);
  user-select: none;
  min-height: 48px;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.data-cell {
  color: var(--table-grid-body-text-color);
}

.cell-content {
  padding: 12px 16px;
  width: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  box-sizing: border-box;
}

.table__header--text,
.table__body--text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Empty State */
.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  border-bottom: none;
  justify-content: center;
}

.empty-cell {
  flex: 1;
  border-right: none;
}

.empty-message {
  text-align: center;
  color: var(--table-grid-empty-text-color);
  font-style: italic;
  padding: 32px 16px;
  width: 100%;
}

.pagination__container {
  margin-top: 1rem;
}
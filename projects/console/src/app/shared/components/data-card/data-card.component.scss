::ng-deep .data-card {
  padding: 16px 20px !important;
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--card-border);
  box-shadow: var(--card-shadow);
  transition: background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
  height: 190px !important; /* Ensure consistent height */
  
  &:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
    border-color: var(--card-hover-border);
  }
}

.data-card-content {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  justify-content: flex-start !important; /* Start from top */
}

.card-header {
  margin-bottom: 10px !important;
  padding: 0 !important;
  height: 42px !important; /* Fixed height for title area */
  overflow: hidden !important;
}

.card-title {
  font-size: 16px !important;
  font-weight: 400 !important;
  color: var(--text-color);
  margin: 0 !important;
  line-height: 1.3 !important;
  word-break: break-word !important;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2!important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  transition: color 0.3s ease;
}

.tags-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  margin-bottom: 8px !important;
  min-height: 72px !important; /* Minimum height for tags section */
  max-height: 72px !important; /* Maximum height for tags section */
  overflow-y: auto !important; /* Allow scrolling if there are many tags */
}

.tags-container {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 6px !important;
  margin-bottom: 8px !important;
  min-height: 24px !important;
  overflow: hidden !important;
}

.tag {
  background-color: var(--tag-bg);
  border-radius: 4px !important;
  padding: 3px 8px !important;
  font-size: 11px !important;
  font-weight: 400 !important;
  color: var(--tag-text);
  border: 1px solid var(--tag-border);
  transition: background-color 0.3s ease, color 0.3s ease, border 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal !important;
  line-height: 1.4em;
  max-height: auto; 
  /* Add subtle hover effect for interactive tags if needed */
  &:hover {
    border-color: var(--primary-start);
  }
}

.subtags-container {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 6px !important;
  min-height: 24px !important;
  overflow: hidden !important;
}

.subtag {
  background-color: var(--subtag-bg);
  border-radius: 4px !important;
  padding: 3px 8px !important;
  font-size: 11px !important;
  color: var(--subtag-text);
  border: 1px solid var(--subtag-border);
  transition: background-color 0.3s ease, color 0.3s ease, border 0.3s ease;
  white-space: nowrap !important; /* Prevent subtag text from wrapping */
}

.card-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-top: auto !important; /* Push to bottom */
  padding: 0 !important;
  height: 30px !important; /* Fixed height for footer */
  width: 100% !important;
}

.created-date {
  font-size: 11px;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-button {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--action-icon-color);
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--action-button-hover);
    color: var(--action-button-hover-color);
    transform: scale(1.05);
  }
  
  &:focus-visible {
    outline: 2px solid var(--primary-start);
    outline-offset: 2px;
  }
  
  svg {
    path, rect, circle {
      fill: currentColor;
      transition: fill 0.3s ease;
    }
  }
}

/* Action-specific icon styles */
.view-icon {
  color: var(--view-icon-color);
}

.edit-icon {
  color: var(--edit-icon-color);
}

.delete-icon {
  color: var(--delete-icon-color);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  ::ng-deep .data-card {
    padding: 12px 16px !important;
    height: 190px !important; /* Maintain fixed height on mobile */
  }
  
  .card-title {
    font-size: 14px !important;
  }
  
  .tag, .subtag {
    font-size: 10px !important;
    padding: 2px 6px !important;
  }
} 
/* HTML: <div class="loader"></div> */
:host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: none;
  visibility: hidden;
  transition: visibility 0.3s ease;
}

:host.loading {
  visibility: visible;
}

/* HTML: <div class="loader"></div> */
.loader {
  width: 70px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: 
    radial-gradient(farthest-side,#000 94%,#0000) top/10px 10px no-repeat,
    conic-gradient(#0000 30%,#000);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 10px),#000 0);
  mask: radial-gradient(farthest-side,#0000 calc(100% - 10px),#000 0);
  animation: l13 1s infinite linear;
}

@keyframes l13{ 
  100%{transform: rotate(1turn)}
}
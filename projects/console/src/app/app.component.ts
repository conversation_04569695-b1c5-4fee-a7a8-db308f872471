import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { BreadcrumbComponent } from './shared/components/breadcrumb/breadcrumb.component';
import { LoaderComponent } from './shared/components/loader/loader.component';
import { ThemeService } from './shared/services/theme/theme.service';
import { AuthTokenService } from './auth/services/auth-token.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent, BreadcrumbComponent, LoaderComponent,CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'console';
  showHeaderAndNav: boolean = true;
  
  constructor(private themeService: ThemeService, private router: Router,private authTokenService: AuthTokenService) {}
  
  ngOnInit(): void {
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    this.router.events.subscribe((event) => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
      } else {
        this.showHeaderAndNav = true;
      }
    });
  }

   ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }
}

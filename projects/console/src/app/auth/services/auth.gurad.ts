import { Injectable, inject } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { TokenStorageService } from './token-storage.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly tokenStorage = inject(TokenStorageService);

  /**
   * Determines if a route can be activated based on authentication status
   * @param route - The route being activated
   * @param state - The router state
   * @returns Observable of boolean or UrlTree indicating if navigation should proceed
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    const loginType = this.tokenStorage.getLoginType();
    if (loginType === 'basic') {
      return this.handleBasicLoginAuth();
    } else {
      return this.handleSSOAuth();
    }
  }

  private handleSSOAuth(): Observable<boolean | UrlTree> {
    const hasAccessToken = !!this.tokenStorage.getAccessToken();
    if (!hasAccessToken) {
      console.info('No access token found, redirecting to login');
      return of(this.router.createUrlTree(['/login']));
    }
    return of(true);
  }

  /**
   * Handles authentication check for basic login flow
   * @returns Observable of boolean or UrlTree indicating if navigation should proceed
   */
  private handleBasicLoginAuth(): Observable<boolean | UrlTree> {
    const hasAccessToken = !!this.tokenStorage.getAccessToken();

    if (!hasAccessToken) {
      console.info('No access token found, redirecting to login');
      return of(this.router.createUrlTree(['/login']));
    }

    return of(true);
  }
}

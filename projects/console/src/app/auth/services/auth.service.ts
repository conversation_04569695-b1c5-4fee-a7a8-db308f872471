import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse} from '@angular/common/http';
import { Router } from '@angular/router';
import {Observable,tap, BehaviorSubject, catchError, throwError, map,of, timer,Subscription,from,} from 'rxjs';
import { TokenStorageService } from './token-storage.service';
import { environment } from '../../../environments/environment';

interface LoginResponse {
  loginUrl: string;
}

interface TokenResponse {
  token_type: string;
  scope: string;
  expires_in: number;
  access_token: string;
  id_token: string;
  refresh_token: string;
  user_name: string;
  email: string;
}

interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);
  private router = inject(Router);
  private tokenStorage = inject(TokenStorageService);
  private apiAuthUrl = environment.consoleApiAuthUrl;
  private authStateSubject = new BehaviorSubject<boolean>(
    this.isAuthenticated(),
  );
  public authState$ = this.authStateSubject.asObservable();
  private refreshSubscription: Subscription | null = null;
  private getAuthHeaders(): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Basic YWRtaW46YWRtaW4xMjM=`,
    });
  }

  public getLoginFlow(): 'sso' | 'basic' {
    return this.tokenStorage.getLoginType() || 'sso';
  }

  loginSSO(redirectUrl: string): Observable<LoginResponse> {
    const url = `${this.apiAuthUrl}/auth/login-url?redirectUrl=${redirectUrl}`;
    return this.http
      .get<LoginResponse>(url, { headers: this.getAuthHeaders() })
      .pipe(
        tap(({ loginUrl }) => {
          window.location.href = loginUrl;
        }),
      );
  }

  exchangeCodeForToken(
    code: string,
    redirectUrl: string,
  ): Observable<TokenPair> {
    const encodedRedirectUrl = encodeURIComponent(redirectUrl);

    const url = `${this.apiAuthUrl}/auth/token?redirectUrl=${encodedRedirectUrl}`;
    return this.http
      .post<TokenResponse>(
        url,
        { code: code },
        { headers: this.getAuthHeaders() },
      )
      .pipe(
        tap((response) => {
          this.tokenStorage.storeLoginType('sso');
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
        }),
        map((response) => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError((error) => {
          console.error('Token exchange failed:', error);
          return throwError(() => error);
        }),
      );
  }

  refreshToken(refreshToken?: string): Observable<TokenPair> {
    const refreshTokenFromStorage = this.tokenStorage.getRefreshToken();
    const url = `${this.apiAuthUrl}/auth/refresh-token`;

    return this.http
      .post<TokenResponse>(
        url,
        { refreshToken: refreshToken ? refreshToken : refreshTokenFromStorage },
        { headers: this.getAuthHeaders() },
      )
      .pipe(
        tap((response) => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
        }),
        map((response) => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError((error) => {
          console.error('Token refresh failed:', error);
          return throwError(() => error);
        }),
      );
  }

  logout(redirectUrl?: string): Observable<any> {
    const idToken = this.tokenStorage.getIdToken();
    const url = `${this.apiAuthUrl}/auth/logout-url?redirectUrl=${redirectUrl}`;

    const headers = this.getAuthHeaders().set('X-ID-TOKEN', idToken || '');

    return this.http.get<any>(url, { headers }).pipe(
      tap(({ logoutUrl }) => {
        this.tokenStorage.clearTokens();
        this.authStateSubject.next(false);
        window.location.href = logoutUrl;
      }),
      catchError((error) => {
        console.error('Logout failed:', error);
        return throwError(() => error);
      })
    );
  }

  isAuthenticated(): boolean {
    return !!this.tokenStorage.getAccessToken();
  }

  basicLoginWithCredentials(
    username: string,
    password: string,
  ): Observable<TokenResponse> {
    const url = `${this.apiAuthUrl}/auth/basic/login`;
    const loginBody = {
      userName: username,
      password: password,
    };

    return this.http
      .post<TokenResponse>(url, loginBody, { headers: this.getAuthHeaders() })
      .pipe(
        tap((response) => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('Basic login failed:', error);
          return throwError(() => error);
        }),
      );
  }

  basicRefreshToken(): Observable<TokenResponse> {
    const refreshToken = this.tokenStorage.getRefreshToken();
    const url = `${this.apiAuthUrl}/auth/basic/refresh/token`;

    return this.http
      .post<TokenResponse>(
        url,
        { refreshToken },
        { headers: this.getAuthHeaders() },
      )
      .pipe(
        tap((response) => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
        }),
        catchError((error) => {
          console.error('Basic token refresh failed:', error);
          return throwError(() => error);
        }),
      );
  }

  private clearAuthState(): void {
    this.tokenStorage.clearTokens();
    this.authStateSubject.next(false);
  }

  basicLogout(): Observable<void> {
    const refreshToken = this.tokenStorage.getRefreshToken();
    if (!refreshToken) {
      this.clearAuthState();
      return of(void 0);
    }

    const url = `${this.apiAuthUrl}/auth/basic/logout`;
    const headers = this.getAuthHeaders().set('X-REFRESH-TOKEN', refreshToken);

    return this.http
      .post(url, null, {
        headers,
        responseType: 'text',
      })
      .pipe(
        map((response) => {
          const normalizedResponse = response.trim();
          if (normalizedResponse !== 'Logout successful.') {
            console.warn('Unexpected logout response:', response);
          }
          return void 0;
        }),
        tap(() => this.clearAuthState()),
        catchError((error) => {
          console.error('Logout API call failed:', error);
          this.clearAuthState();
          return throwError(() => new Error('Failed to logout from server'));
        }),
      );
  }
}

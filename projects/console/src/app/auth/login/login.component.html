<div class="login-container">
  <div class="left-panel">
    <img class='login-image' src="../../../assets/images/login.jpg" alt="">
    <img class="overlay-logo" src="../../../assets/images/ascendionLogo.png" alt="Overlay Logo">
  </div>
  <div class="right-panel">
    <div class="sign-container">
      <div class="heading">
        <h3 class="main-heading">{{labels.labels.main_heading}}</h3>
        <p class="sub-heading">{{labels.labels.sub_heading}}</p>
      </div>

      <form [formGroup]="loginForm">
        <app-form-field [type]="'text'" label="{{labels.labels.username}}" [control]="getControl('username')"
          placeholder="{{labels.placeholders.username}}" [rightIcon]="'../../../assets/images/close.png'"
          rightIconTooltip="Clear username" rightIconWidth="10.57px" rightIconHeight="10.57px"
          (rightIconClick)="clearUsername()">
        </app-form-field>
        <app-form-field label="{{labels.labels.password}}" [type]="showPassword() ? 'text' : 'password'"
          [control]="getControl('password')" id="password" placeholder="{{labels.placeholders.password}}"
          [rightIcon]="showPassword() ? '../../../assets/images/View_Off.png' : '../../../assets/images/View_On.png'"
          rightIconTooltip="Toggle password visibility" rightIconWidth="22px" rightIconHeight="22px"
          (rightIconClick)="togglePasswordVisibility()">
        </app-form-field>

        <!-- <div class="form-options">
            <div class="checkbox-group">
              <app-form-field
                type="checkbox"
                [control]="getControl('keepSignedIn')"
                id="keepSignedIn"
                placeholder="Keep me signed in"
              ></app-form-field>
            </div>
            <a href="#" class="forgot-password-link" (click)="onForgotPassword()">Forgot password?</a>
          </div> -->

        <div class="buttons-container">
          <button type="submit" class="btn btn-primary" (click)="onBasicLogin()"
            [disabled]="isLoading() || loginForm.invalid">
            {{labels.labels.sign_in}}
            <span class="arrow">{{labels.labels.arrow}}</span>
          </button>

          <div class="separator">{{labels.labels.seperator}}</div>

          <button type="button" class="btn btn-secondary" (click)="onCompanyLogin()" [disabled]="isLoading()">
            {{labels.labels.login_with_company}}
          </button>
        </div>
      </form>

      <!-- <p class="trouble-signing-in">
          Still have trouble signing in? <a href="#" (click)="onTroubleSigningIn()">Click Here</a>
        </p> -->
    </div>
  </div>
</div>
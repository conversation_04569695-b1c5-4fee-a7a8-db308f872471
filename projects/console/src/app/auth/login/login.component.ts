import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from '@angular/forms';
import { Router } from '@angular/router';
import { FormFieldComponent } from '../../shared/components/form-field/form-field.component';
import { AuthService } from '../services/auth.service';
import { environment } from '../../../environments/environment';
import { HttpErrorResponse } from '@angular/common/http';
import { TokenStorageService } from '../services/token-storage.service';
import logintext from '../../constants/login.json';


export interface SavedAccount {
  email: string;
  profilePic?: string;
  isSelected?: boolean;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormFieldComponent],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  loginMode = signal<'sso' | 'form'>('sso');
  isLoading = signal(false);
  showPassword =signal(false);
  loginForm: FormGroup;
  errorMessage = signal<string | null>(null);
  public labels: any = logintext;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private tokenStorage: TokenStorageService,
  ) {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', Validators.required],
      keepSignedIn: [false],
    });
  }

  ngOnInit(): void {
    const storedLoginType = this.tokenStorage.getLoginType();
    if (storedLoginType === 'sso' || storedLoginType === 'basic') {
      this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');
    } else {
      this.loginMode.set('sso');
    }
  }

  getControl(name: string): FormControl {
    return this.loginForm.get(name) as FormControl;
  }

  onBasicLogin(): void {
    if (this.loginForm.valid) {
      this.isLoading.set(true);
      this.errorMessage.set(null);

      const { username, password } = this.loginForm.value;
      this.authService.basicLoginWithCredentials(username, password).subscribe({
        next: () => {
          this.tokenStorage.storeLoginType('basic');
          this.router.navigate(['/dashboard']);
        },
        error: (error: HttpErrorResponse) => {
          this.isLoading.set(false);
          this.errorMessage.set('Invalid username or password');
          console.error('Login failed:', error);
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCompanyLogin(): void {
    this.isLoading.set(true);
    this.errorMessage.set(null);
    const redirectUrl = environment.consoleRedirectUrl || window.location.origin;

    this.authService.loginSSO(redirectUrl).subscribe({
      next: () => {
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Login failed:', error);
        this.errorMessage.set('Failed to initiate login with company account.');
        this.isLoading.set(false);
      },
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword.set(!this.showPassword());
  }

  clearInput(fieldName: string): void {
    this.loginForm.get(fieldName)?.setValue('');
    this.loginForm.get(fieldName)?.markAsTouched();
  }

  clearUsername(): void {
    this.clearInput('username');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string | null {
    const field = this.loginForm.get(fieldName);
    if (field?.touched && field?.errors) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return null;
  }

  onForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }

  onTroubleSigningIn(): void {
    this.router.navigate(['/help']);
  }
}

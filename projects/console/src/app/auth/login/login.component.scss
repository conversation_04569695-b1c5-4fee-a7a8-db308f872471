  html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden; /* prevent scrollbars */
  }

  .login-container {
    display: flex;
    height: 100vh;
    width: 100vw;
  }

  .left-panel {
    flex: 0.4;
    overflow: hidden;
  }

  .right-panel {
    flex: 0.6;
    overflow: hidden;
    display: flex;              /* Make the container a flexbox */
    justify-content: center;    /* Center horizontally */
    align-items: center;        /* Center vertically */
    background-color: #f5f5f5;  /* Optional background for testing */
  }


  .login-image {
    width: 100%;   /* fill .left-panel */
    height: 100%;  /* fill .left-panel vertically */
    object-fit: cover; /* maintain aspect ratio and cover entire area */
  }

  .overlay-logo {
    position: absolute;
    top: 34px; /* Adjust this value as needed */
    left: 33px; /* Adjust this value as needed */
    width: 120px; /* Adjust size as needed */
    height: 38px;
    z-index: 2; /* Ensure it stays above the background image */
  }

  .sign-container{
    border-radius: 16px;
    border: 1px solid #DADCE7; /* Neutral/N-200 color */
    width: 509px;
  height: 678px;
  top: 201px;
  left: 1104px;
  border-width: 1px;
  padding: 24px;
  gap: 64px;

  }

  .sub-heading{
    size: 20px;
    color: #858AAD;
  }

  .heading{
    justify-items: center;
  }

  .credentials-container{
    padding-top: 35px;
  }


  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    font-size: 13px;
  .checkbox-group {
      display: flex;
      align-items: center;
      color: #333333;

      input[type="checkbox"] {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 15px;
        height: 15px;
        background-color: #FFFFFF;
        border: 1px solid #B0B0B0;
        border-radius: 3px;
        cursor: pointer;
        position: relative;
        outline: none;
        vertical-align: middle;

        &:checked {
          background-color: #5BA5D9;
          border-color: #5BA5D9;

          &::before {
            content: '✔';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
            line-height: 1;
          }
        }
      }

      label {
        margin-bottom: 0;
        font-weight: normal;
        color: #333333;
        margin-left: 8px;
        cursor: pointer;
      }
    }

      .forgot-password-link {
      color: #333333;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .buttons-container{
    padding: 20px;
    justify-items: center;

    .btn{
            font-size: 18px;
            cursor: pointer;

    }
    .btn-primary {
      background-color: #14161F;
      width: 461px;
      border-radius: 8px;
      height: 52px;

    
        color: #FFFFFF;
    

    }

      .btn-secondary {
      background-color: #DADCE7;
      width: 461px;
      border-radius: 8px;
      height: 52px;


        color: #14161F;

    }

  }

  .separator {
    color: #A3A7C2;
    font-size: 18px;
    display: flex;
    align-items: center;
    text-align: center;
    width: 100%;
    margin: 20px 0; /* adjust as per your need */
  }

  .separator::before,
  .separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #DADCE7; /* Neutral/N-200 or any color you prefer */
  }

  .separator::before {
    margin-right: 10px; /* spacing between line and 'or' */
  }

  .separator::after {
    margin-left: 10px; /* spacing between line and 'or' */
  }

  .trouble-signing-in {
    text-align: center;
    font-size: 12.5px;
    color: #6B7280;
    font-weight: 400;
    margin: 0;

    a {
      color: #374151;
      font-weight: 600;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { Subscription } from 'rxjs';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-callback',
  template: '',
})
export class CallbackComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    const refreshToken = this.route.snapshot.queryParams['refresh_token'];
    const code = this.route.snapshot.queryParams['code'];
    const error = this.route.snapshot.queryParams['error'];

    if (error) {
      console.error('Azure AD returned an error:', error);
      this.router.navigate(['/login'], {
        state: {
          error: `Azure AD login failed: ${this.route.snapshot.queryParams['error_description'] || error}`,
        },
      });
      return;
    }

    if (refreshToken) {
      this.handleTokenRefresh(refreshToken);
    } else if (code) {
      console.log('Authorization code received, length:', code.length);
      this.handleCodeExchange(code);
    } else {
      console.warn(
        'No authorization code or refresh token found in callback URL',
      );
    }
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        this.router.navigate(['/dashboard']);
      },
      error: (err) => {
        console.error('Token refresh failed:', err);
        this.router.navigate(['/login']);
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleCodeExchange(code: string): void {
    const exchangeSub = this.authService
      .exchangeCodeForToken(code, environment.consoleRedirectUrl)
      .subscribe({
        next: () => {
          this.router.navigate(['/dashboard']);
          console.log('Token exchange successful');
        },
        error: (err) => {
          console.error('Token exchange failed:', err);
          this.router.navigate(['/login']);
        },
      });
    this.subscription.add(exchangeSub);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

const { ModuleFederationPlugin } = require("webpack").container;

module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'console',
    globalObject: 'self'
  },
  optimization: {
    runtimeChunk: false,
  },
  experiments: {
    outputModule: true
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.ttf$/,
        use: ['file-loader']
      }
    ]
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'console',
      library: { type: 'module' },
      filename: 'remoteEntry.js',
      exposes: {
        './AgentsComponent': './projects/console/src/app/pages/agents/agents.component.ts',
      },
      shared: {
        '@angular/core': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/common': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/router': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' }
      }
    }),
  ],
};
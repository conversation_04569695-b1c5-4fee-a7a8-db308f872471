import { Pipe, PipeTransform } from '@angular/core';
import { UIDesignNode } from '../components/code-window/services/ui-design-node.service';
/**
 * High-performance memoized pipe for UI Design canvas computations
 * Optimizes heavy calculations for node positioning, transformations, and state management
 */
@Pipe({
  name: 'uiDesignComputation',
  pure: true,
  standalone: true
})
export class UIDesignComputationPipe implements PipeTransform {
  private cache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 200;

  transform(data: any, operation: UIDesignOperation, ...args: any[]): any {
    // Generate cache key
    const cacheKey = this.generateCacheKey(data, operation, args);

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Perform computation
    const result = this.performComputation(data, operation, args);

    // Cache result
    this.cacheResult(cacheKey, result);

    return result;
  }

  private generateCacheKey(data: any, operation: UIDesignOperation, args: any[]): string {
    const dataHash = this.hashData(data);
    const argsHash = args.length > 0 ? this.hashData(args) : '';
    return `${operation}_${dataHash}_${argsHash}`;
  }

  private hashData(data: any): string {
    if (Array.isArray(data)) {
      // For arrays, create hash based on length and first few items
      const sample = data.slice(0, 3).map(item =>
        typeof item === 'object' ? JSON.stringify(item).slice(0, 50) : String(item)
      );
      return `arr_${data.length}_${sample.join('_')}`.slice(0, 100);
    }

    if (typeof data === 'object' && data !== null) {
      // For objects, create hash based on key structure
      const keys = Object.keys(data).slice(0, 5);
      const values = keys.map(key => String(data[key]).slice(0, 20));
      return `obj_${keys.join('_')}_${values.join('_')}`.slice(0, 100);
    }

    return String(data).slice(0, 50);
  }

  private performComputation(data: any, operation: UIDesignOperation, args: any[]): any {
    switch (operation) {
      case 'calculateNodePositions':
        return this.calculateNodePositions(data, args[0]);

      case 'generateCanvasTransform':
        return this.generateCanvasTransform(args[0], args[1], args[2]);

      case 'filterVisibleNodes':
        return this.filterVisibleNodes(data, args[0]);

      case 'calculateNodeBounds':
        return this.calculateNodeBounds(data);

      case 'optimizeNodeLayout':
        return this.optimizeNodeLayout(data, args[0]);

      case 'generateNodeClasses':
        return this.generateNodeClasses(data, args[0]);

      case 'calculateViewportMetrics':
        return this.calculateViewportMetrics(args[0], args[1]);

      case 'processNodeSelection':
        return this.processNodeSelection(data, args[0]);

      default:
        return data;
    }
  }

  private calculateNodePositions(nodes: UIDesignNode[], layoutConfig: any): UIDesignNode[] {
    if (!Array.isArray(nodes)) return [];

    // ✅ OPTIMIZATION: Pre-allocate result array
    const result = new Array(nodes.length);
    const { gridSize = 20, padding = 10 } = layoutConfig || {};

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      result[i] = {
        ...node,
        position: {
          x: Math.round((node.position?.x || 0) / gridSize) * gridSize,
          y: Math.round((node.position?.y || 0) / gridSize) * gridSize
        }
      };
    }

    return result;
  }

  private generateCanvasTransform(zoom: number, panX: number, panY: number): string {
    // ✅ OPTIMIZATION: Use template literal for better performance
    const scale = Math.max(0.1, Math.min(5, zoom));
    const translateX = Math.round(panX * 100) / 100;
    const translateY = Math.round(panY * 100) / 100;

    return `translate(${translateX}px, ${translateY}px) scale(${scale})`;
  }

  private filterVisibleNodes(nodes: UIDesignNode[], viewport: any): UIDesignNode[] {
    if (!Array.isArray(nodes) || !viewport) return nodes;

    // ✅ OPTIMIZATION: Use efficient bounds checking
    const { x, y, width, height } = viewport;
    const result: UIDesignNode[] = [];

    for (const node of nodes) {
      const nodeX = node.position?.x || 0;
      const nodeY = node.position?.y || 0;
      const nodeWidth = node.data?.width || 0;
      const nodeHeight = node.data?.height || 0;

      // Check if node intersects with viewport
      if (nodeX < x + width &&
          nodeX + nodeWidth > x &&
          nodeY < y + height &&
          nodeY + nodeHeight > y) {
        result.push(node);
      }
    }

    return result;
  }

  private calculateNodeBounds(nodes: UIDesignNode[]): { minX: number; minY: number; maxX: number; maxY: number } {
    if (!Array.isArray(nodes) || nodes.length === 0) {
      return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    // ✅ OPTIMIZATION: Single pass calculation
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    for (const node of nodes) {
      const x = node.position?.x || 0;
      const y = node.position?.y || 0;
      const width = node.data?.width || 0;
      const height = node.data?.height || 0;

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + width);
      maxY = Math.max(maxY, y + height);
    }

    return { minX, minY, maxX, maxY };
  }

  private optimizeNodeLayout(nodes: UIDesignNode[], options: any): UIDesignNode[] {
    if (!Array.isArray(nodes)) return [];

    const { autoAlign = false, snapToGrid = true, gridSize = 20 } = options || {};

    // ✅ OPTIMIZATION: Process nodes efficiently
    return nodes.map(node => {
      let x = node.position?.x || 0;
      let y = node.position?.y || 0;

      if (snapToGrid) {
        x = Math.round(x / gridSize) * gridSize;
        y = Math.round(y / gridSize) * gridSize;
      }

      return {
        ...node,
        position: { x, y }
      };
    });
  }

  private generateNodeClasses(node: UIDesignNode, state: any): string[] {
    const classes: string[] = ['ui-design-node'];

    if (node.data?.isLoading) classes.push('loading');
    if (node.dragging) classes.push('dragging');
    if (state?.selected?.includes(node.id)) classes.push('selected');
    if (state?.highlighted?.includes(node.id)) classes.push('highlighted');
    if ((node.data as any)?.hasError) classes.push('error');

    return classes;
  }

  private calculateViewportMetrics(canvasSize: any, zoom: number): any {
    const { width = 0, height = 0 } = canvasSize || {};

    return {
      visibleWidth: width / zoom,
      visibleHeight: height / zoom,
      centerX: width / 2,
      centerY: height / 2,
      zoom: zoom
    };
  }

  private processNodeSelection(nodes: UIDesignNode[], selectionData: any): any {
    if (!Array.isArray(nodes)) return { selected: [], count: 0 };

    const { selectedIds = [], mode = 'single' } = selectionData || {};
    const selectedSet = new Set(selectedIds);

    // ✅ OPTIMIZATION: Use Set for O(1) lookups
    const selected = nodes.filter(node => selectedSet.has(node.id));

    return {
      selected,
      count: selected.length,
      ids: selectedIds,
      mode
    };
  }

  private cacheResult(key: string, result: any): void {
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      // Remove oldest entry (LRU)
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, result);
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; maxSize: number; hitRatio: number } {
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRatio: this.cache.size > 0 ? Math.min(this.cache.size / this.MAX_CACHE_SIZE, 1) : 0
    };
  }
}

// Supporting types
export type UIDesignOperation =
  | 'calculateNodePositions'
  | 'generateCanvasTransform'
  | 'filterVisibleNodes'
  | 'calculateNodeBounds'
  | 'optimizeNodeLayout'
  | 'generateNodeClasses'
  | 'calculateViewportMetrics'
  | 'processNodeSelection';

export interface UIDesignComputationResult {
  data: any;
  cached: boolean;
  computationTime?: number;
}

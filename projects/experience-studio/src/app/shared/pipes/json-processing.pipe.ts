import { Pipe, PipeTransform } from '@angular/core';

/**
 * High-performance memoized pipe for JSON processing operations
 * Optimizes repeated JSON.parse/stringify operations in polling services
 */
@Pipe({
  name: 'jsonProcessing',
  pure: true,
  standalone: true
})
export class JsonProcessingPipe implements PipeTransform {
  private parseCache = new Map<string, any>();
  private stringifyCache = new Map<string, string>();
  private readonly MAX_CACHE_SIZE = 100;

  transform(data: any, operation: JsonOperation, ...args: any[]): any {
    switch (operation) {
      case 'parse':
        return this.safeParse(data);

      case 'stringify':
        return this.safeStringify(data, args[0]);

      case 'parseWithFallback':
        return this.parseWithFallback(data, args[0]);

      case 'extractProperty':
        return this.extractProperty(data, args[0]);

      case 'validateStructure':
        return this.validateStructure(data, args[0]);

      case 'deepClone':
        return this.deepClone(data);

      default:
        return data;
    }
  }

  private safeParse(jsonString: string): any {
    if (typeof jsonString !== 'string') {
      return jsonString;
    }

    // Check cache first
    if (this.parseCache.has(jsonString)) {
      return this.parseCache.get(jsonString);
    }

    try {
      const result = JSON.parse(jsonString);
      this.cacheParseResult(jsonString, result);
      return result;
    } catch (error) {
      // Return original string if parsing fails
      this.cacheParseResult(jsonString, jsonString);
      return jsonString;
    }
  }

  private safeStringify(data: any, space?: number): string {
    if (typeof data === 'string') {
      return data;
    }

    const cacheKey = this.generateStringifyKey(data, space);

    // Check cache first
    if (this.stringifyCache.has(cacheKey)) {
      return this.stringifyCache.get(cacheKey)!;
    }

    try {
      const result = JSON.stringify(data, null, space);
      this.cacheStringifyResult(cacheKey, result);
      return result;
    } catch (error) {
      const fallback = String(data);
      this.cacheStringifyResult(cacheKey, fallback);
      return fallback;
    }
  }

  private parseWithFallback(jsonString: string, fallback: any = null): any {
    const parsed = this.safeParse(jsonString);
    return parsed === jsonString ? fallback : parsed;
  }

  private extractProperty(data: any, propertyPath: string): any {
    if (!data || typeof propertyPath !== 'string') {
      return null;
    }

    // Handle nested property paths like 'data.files.content'
    const properties = propertyPath.split('.');
    let current = data;

    for (const prop of properties) {
      if (current && typeof current === 'object' && prop in current) {
        current = current[prop];
      } else {
        return null;
      }
    }

    return current;
  }

  private validateStructure(data: any, expectedStructure: any): boolean {
    if (!data || !expectedStructure) {
      return false;
    }

    try {
      // Simple structure validation
      if (Array.isArray(expectedStructure)) {
        return Array.isArray(data);
      }

      if (typeof expectedStructure === 'object') {
        if (typeof data !== 'object') {
          return false;
        }

        // Check if all expected properties exist
        for (const key in expectedStructure) {
          if (!(key in data)) {
            return false;
          }
        }
        return true;
      }

      return typeof data === typeof expectedStructure;
    } catch (error) {
      return false;
    }
  }

  private deepClone(data: any): any {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    // Use JSON method for deep cloning (fast for serializable objects)
    try {
      return JSON.parse(JSON.stringify(data));
    } catch (error) {
      // Fallback for non-serializable objects
      return this.manualDeepClone(data);
    }
  }

  private manualDeepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.manualDeepClone(item));
    }

    const cloned: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.manualDeepClone(obj[key]);
      }
    }

    return cloned;
  }

  private generateStringifyKey(data: any, space?: number): string {
    // Create a simple hash for the cache key
    const typeInfo = Array.isArray(data) ? 'array' : typeof data;
    const sizeInfo = Array.isArray(data) ? data.length : Object.keys(data || {}).length;
    const spaceInfo = space || 0;

    return `${typeInfo}_${sizeInfo}_${spaceInfo}`;
  }

  private cacheParseResult(key: string, result: any): void {
    if (this.parseCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.parseCache.keys().next().value;
      if (typeof firstKey === 'string') {
        this.parseCache.delete(firstKey);
      }
    }
    this.parseCache.set(key, result);
  }

  private cacheStringifyResult(key: string, result: string): void {
    if (this.stringifyCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.stringifyCache.keys().next().value;
      if (typeof firstKey === 'string') {
        this.stringifyCache.delete(firstKey);
      }
    }
    this.stringifyCache.set(key, result);
  }

  clearCache(): void {
    this.parseCache.clear();
    this.stringifyCache.clear();
  }

  getCacheStats(): CacheStats {
    return {
      parseCache: {
        size: this.parseCache.size,
        maxSize: this.MAX_CACHE_SIZE,
        hitRatio: this.calculateHitRatio(this.parseCache)
      },
      stringifyCache: {
        size: this.stringifyCache.size,
        maxSize: this.MAX_CACHE_SIZE,
        hitRatio: this.calculateHitRatio(this.stringifyCache)
      }
    };
  }

  private calculateHitRatio(cache: Map<string, any>): number {
    // Simple approximation - in real implementation, you'd track hits/misses
    return cache.size > 0 ? Math.min(cache.size / this.MAX_CACHE_SIZE, 1) : 0;
  }
}

// Supporting types
export type JsonOperation =
  | 'parse'
  | 'stringify'
  | 'parseWithFallback'
  | 'extractProperty'
  | 'validateStructure'
  | 'deepClone';

export interface CacheStats {
  parseCache: {
    size: number;
    maxSize: number;
    hitRatio: number;
  };
  stringifyCache: {
    size: number;
    maxSize: number;
    hitRatio: number;
  };
}

// Utility functions for direct use
export class JsonProcessingUtils {
  private static pipe = new JsonProcessingPipe();

  static safeParse(jsonString: string): any {
    return this.pipe.transform(jsonString, 'parse');
  }

  static safeStringify(data: any, space?: number): string {
    return this.pipe.transform(data, 'stringify', space);
  }

  static extractProperty(data: any, propertyPath: string): any {
    return this.pipe.transform(data, 'extractProperty', propertyPath);
  }

  static validateStructure(data: any, expectedStructure: any): boolean {
    return this.pipe.transform(data, 'validateStructure', expectedStructure);
  }

  static deepClone(data: any): any {
    return this.pipe.transform(data, 'deepClone');
  }

  static clearCache(): void {
    this.pipe.clearCache();
  }

  static getCacheStats(): CacheStats {
    return this.pipe.getCacheStats();
  }
}

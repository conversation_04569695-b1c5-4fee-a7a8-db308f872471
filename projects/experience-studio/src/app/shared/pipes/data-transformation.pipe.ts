import { Pipe, PipeTransform } from '@angular/core';

/**
 * High-performance memoized pipe for data transformations
 * Optimizes repeated array/object operations in components
 */
@Pipe({
  name: 'dataTransformation',
  pure: true,
  standalone: true
})
export class DataTransformationPipe implements PipeTransform {
  private cache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 50;

  transform(data: any, transformationType: TransformationType, ...args: any[]): any {
    // Generate cache key
    const cacheKey = this.generateCacheKey(data, transformationType, args);

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Perform transformation
    const result = this.performTransformation(data, transformationType, args);

    // Cache result
    this.cacheResult(cacheKey, result);

    return result;
  }

  private generateCacheKey(data: any, type: TransformationType, args: any[]): string {
    const dataHash = this.hashData(data);
    const argsHash = args.length > 0 ? this.hashData(args) : '';
    return `${type}_${dataHash}_${argsHash}`;
  }

  private hashData(data: any): string {
    if (Array.isArray(data)) {
      return `arr_${data.length}_${JSON.stringify(data.slice(0, 3))}`.slice(0, 50);
    }
    if (typeof data === 'object' && data !== null) {
      const keys = Object.keys(data).slice(0, 5);
      return `obj_${keys.length}_${keys.join('_')}`.slice(0, 50);
    }
    return String(data).slice(0, 30);
  }

  private performTransformation(data: any, type: TransformationType, args: any[]): any {
    switch (type) {
      case 'fileDataToFileModel':
        return this.transformFileDataToFileModel(data);

      case 'extractFileNames':
        return this.extractFileNames(data);

      case 'normalizeFileNames':
        return this.normalizeFileNames(data);

      case 'createFileIdentifierSets':
        return this.createFileIdentifierSets(data);

      case 'sortFileModels':
        return this.sortFileModels(data, args[0]);

      case 'filterUniqueFiles':
        return this.filterUniqueFiles(data, args[0]);

      default:
        return data;
    }
  }

  private transformFileDataToFileModel(files: any[]): any[] {
    if (!Array.isArray(files)) return [];

    // ✅ OPTIMIZATION: Use map with pre-allocated result array
    const result = new Array(files.length);

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      result[i] = {
        name: file.path || file.fileName || file.name || 'unknown.txt',
        type: 'file',
        content: file.code || file.content || '',
        fileName: file.path || file.fileName || file.name || 'unknown.txt',
        language: this.getLanguageFromPath(file.path || file.fileName || ''),
        path: file.path || file.fileName || file.name || 'unknown.txt'
      };
    }

    return result;
  }

  private extractFileNames(data: any): string[] {
    if (Array.isArray(data)) {
      // ✅ OPTIMIZATION: Direct property access with fallback
      return data.map(item =>
        item.fileName || item.name || item.path || item.title || 'unknown'
      );
    }

    if (typeof data === 'object' && data !== null) {
      return Object.keys(data);
    }

    return [];
  }

  private normalizeFileNames(fileNames: string[]): string[] {
    if (!Array.isArray(fileNames)) return [];

    // ✅ OPTIMIZATION: Pre-allocate result array
    const result = new Array(fileNames.length);

    for (let i = 0; i < fileNames.length; i++) {
      result[i] = fileNames[i]
        .toLowerCase()
        .replace(/[^a-z0-9.]/g, '')
        .trim();
    }

    return result;
  }

  private createFileIdentifierSets(nodes: any[]): FileIdentifierSets {
    if (!Array.isArray(nodes)) {
      return {
        originalTitles: [],
        formattedTitles: [],
        normalizedTitles: [],
        baseNames: [],
        allIdentifiers: new Set()
      };
    }

    // ✅ OPTIMIZATION: Single pass through nodes
    const originalTitles: string[] = [];
    const formattedTitles: string[] = [];
    const normalizedTitles: string[] = [];
    const baseNames: string[] = [];
    const allIdentifiers = new Set<string>();

    for (const node of nodes) {
      const title = node.data?.title || node.title || '';
      const formatted = this.formatFileName(title);
      const normalized = this.normalizeFileName(title);
      const baseName = this.extractBaseName(title);

      originalTitles.push(title);
      formattedTitles.push(formatted);
      normalizedTitles.push(normalized);
      baseNames.push(baseName);

      // Add all variations to set
      allIdentifiers.add(title);
      allIdentifiers.add(formatted);
      allIdentifiers.add(normalized);
      allIdentifiers.add(baseName);
      allIdentifiers.add(title.toLowerCase());
      allIdentifiers.add(formatted.toLowerCase());
      allIdentifiers.add(normalized.toLowerCase());
      allIdentifiers.add(baseName.toLowerCase());
    }

    return {
      originalTitles,
      formattedTitles,
      normalizedTitles,
      baseNames,
      allIdentifiers
    };
  }

  private sortFileModels(files: any[], sortBy: string = 'name'): any[] {
    if (!Array.isArray(files)) return [];

    // ✅ OPTIMIZATION: Use efficient sort with pre-computed keys
    return files.slice().sort((a, b) => {
      const aValue = a[sortBy] || '';
      const bValue = b[sortBy] || '';
      return aValue.localeCompare(bValue);
    });
  }

  private filterUniqueFiles(files: any[], keyProperty: string = 'name'): any[] {
    if (!Array.isArray(files)) return [];

    // ✅ OPTIMIZATION: Use Set for O(1) duplicate detection
    const seen = new Set<string>();
    const result: any[] = [];

    for (const file of files) {
      const key = file[keyProperty];
      if (!seen.has(key)) {
        seen.add(key);
        result.push(file);
      }
    }

    return result;
  }

  private formatFileName(fileName: string): string {
    return fileName.replace(/[_-]/g, ' ').replace(/\.[^/.]+$/, '');
  }

  private normalizeFileName(fileName: string): string {
    return fileName.toLowerCase().replace(/[^a-z0-9.]/g, '').trim();
  }

  private extractBaseName(fileName: string): string {
    return fileName.replace(/\.[^/.]+$/, '');
  }

  private getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase() || '';
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'ts': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c'
    };
    return languageMap[extension] || 'text';
  }

  private cacheResult(key: string, result: any): void {
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      if (typeof firstKey === 'string') {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, result);
  }

  clearCache(): void {
    this.cache.clear();
  }
}

// Supporting types
export type TransformationType =
  | 'fileDataToFileModel'
  | 'extractFileNames'
  | 'normalizeFileNames'
  | 'createFileIdentifierSets'
  | 'sortFileModels'
  | 'filterUniqueFiles';

export interface FileIdentifierSets {
  originalTitles: string[];
  formattedTitles: string[];
  normalizedTitles: string[];
  baseNames: string[];
  allIdentifiers: Set<string>;
}

import { Pipe, PipeTransform } from '@angular/core';

/**
 * High-performance memoized pipe for Monaco Editor optimizations
 * Optimizes language detection, model creation, and editor configuration
 */
@Pipe({
  name: 'monacoOptimization',
  pure: true,
  standalone: true
})
export class MonacoOptimizationPipe implements PipeTransform {
  private cache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 100;

  // Pre-computed language mappings for better performance
  private static readonly LANGUAGE_MAP = new Map([
    ['ts', 'typescript'],
    ['js', 'javascript'],
    ['jsx', 'javascript'],
    ['tsx', 'typescript'],
    ['html', 'html'],
    ['css', 'css'],
    ['scss', 'scss'],
    ['less', 'less'],
    ['json', 'json'],
    ['md', 'markdown'],
    ['yml', 'yaml'],
    ['yaml', 'yaml'],
    ['xml', 'xml'],
    ['svg', 'xml'],
    ['py', 'python'],
    ['java', 'java'],
    ['c', 'c'],
    ['cpp', 'cpp'],
    ['cs', 'csharp'],
    ['go', 'go'],
    ['rs', 'rust'],
    ['rb', 'ruby'],
    ['php', 'php']
  ]);

  transform(data: any, operation: MonacoOperation, ...args: any[]): any {
    // Generate cache key
    const cacheKey = this.generateCacheKey(data, operation, args);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Perform computation
    const result = this.performComputation(data, operation, args);
    
    // Cache result
    this.cacheResult(cacheKey, result);
    
    return result;
  }

  private generateCacheKey(data: any, operation: MonacoOperation, args: any[]): string {
    const dataHash = this.hashData(data);
    const argsHash = args.length > 0 ? this.hashData(args) : '';
    return `${operation}_${dataHash}_${argsHash}`;
  }

  private hashData(data: any): string {
    if (typeof data === 'string') {
      return data.slice(0, 50);
    }
    if (Array.isArray(data)) {
      return `arr_${data.length}_${data.slice(0, 2).join('_')}`.slice(0, 50);
    }
    if (typeof data === 'object' && data !== null) {
      const keys = Object.keys(data).slice(0, 3);
      return `obj_${keys.join('_')}`.slice(0, 50);
    }
    return String(data).slice(0, 30);
  }

  private performComputation(data: any, operation: MonacoOperation, args: any[]): any {
    switch (operation) {
      case 'detectLanguage':
        return this.detectLanguage(data);
      
      case 'optimizeEditorOptions':
        return this.optimizeEditorOptions(data, args[0]);
      
      case 'generateModelConfig':
        return this.generateModelConfig(data, args[0], args[1]);
      
      case 'calculateEditorDimensions':
        return this.calculateEditorDimensions(data, args[0]);
      
      case 'optimizeThemeConfig':
        return this.optimizeThemeConfig(data, args[0]);
      
      case 'processFileContent':
        return this.processFileContent(data, args[0]);
      
      case 'generateEditorLayout':
        return this.generateEditorLayout(data, args[0]);
      
      case 'validateEditorState':
        return this.validateEditorState(data);
      
      default:
        return data;
    }
  }

  private detectLanguage(fileName: string): string {
    if (typeof fileName !== 'string') return 'plaintext';
    
    // ✅ OPTIMIZATION: Use pre-computed Map for O(1) lookup
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ext ? (MonacoOptimizationPipe.LANGUAGE_MAP.get(ext) || 'plaintext') : 'plaintext';
  }

  private optimizeEditorOptions(baseOptions: any, customOptions: any = {}): any {
    // ✅ OPTIMIZATION: Pre-defined optimized defaults
    const optimizedDefaults = {
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      tabSize: 2,
      lineHeight: 22,
      wordWrap: 'on',
      renderLineHighlight: 'line',
      cursorBlinking: 'smooth',
      smoothScrolling: true,
      mouseWheelZoom: true,
      folding: true,
      lineNumbers: 'on',
      glyphMargin: false,
      renderWhitespace: 'none',
      scrollbar: {
        vertical: 'auto',
        horizontal: 'auto',
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10
      }
    };

    // ✅ OPTIMIZATION: Efficient object merging
    return { ...optimizedDefaults, ...baseOptions, ...customOptions };
  }

  private generateModelConfig(content: string, language: string, uri?: string): any {
    const contentLength = content?.length || 0;
    
    return {
      content: content || '',
      language: language || 'plaintext',
      uri: uri,
      metadata: {
        size: contentLength,
        lines: content ? content.split('\n').length : 0,
        isLarge: contentLength > 100000, // Flag for large files
        optimized: true
      }
    };
  }

  private calculateEditorDimensions(containerSize: any, options: any = {}): any {
    const { width = 800, height = 600 } = containerSize || {};
    const { padding = 0, minWidth = 300, minHeight = 200 } = options;
    
    // ✅ OPTIMIZATION: Efficient dimension calculations
    const effectiveWidth = Math.max(minWidth, width - (padding * 2));
    const effectiveHeight = Math.max(minHeight, height - (padding * 2));
    
    return {
      width: effectiveWidth,
      height: effectiveHeight,
      aspectRatio: effectiveWidth / effectiveHeight,
      isCompact: effectiveWidth < 600 || effectiveHeight < 400
    };
  }

  private optimizeThemeConfig(themeName: string, customColors: any = {}): any {
    // ✅ OPTIMIZATION: Pre-defined theme configurations
    const themeConfigs: { [key: string]: any } = {
      'light-theme': {
        base: 'vs',
        colors: {
          'editor.background': '#ffffff',
          'editor.foreground': '#000000',
          'editor.lineHighlightBackground': '#f5f5f5',
          'editorCursor.foreground': '#000000',
          'editor.selectionBackground': '#add6ff',
          ...customColors
        }
      },
      'dark-theme': {
        base: 'vs-dark',
        colors: {
          'editor.background': '#1e1e1e',
          'editor.foreground': '#d4d4d4',
          'editor.lineHighlightBackground': '#2f3137',
          'editorCursor.foreground': '#ffffff',
          'editor.selectionBackground': '#264f78',
          ...customColors
        }
      }
    };

    return themeConfigs[themeName] || themeConfigs['light-theme'];
  }

  private processFileContent(content: string, options: any = {}): any {
    if (typeof content !== 'string') return { content: '', processed: false };
    
    const { maxLength = 1000000, truncate = true } = options;
    
    // ✅ OPTIMIZATION: Handle large files efficiently
    if (content.length > maxLength && truncate) {
      return {
        content: content.substring(0, maxLength) + '\n\n// ... Content truncated for performance',
        truncated: true,
        originalLength: content.length,
        processed: true
      };
    }

    return {
      content: content,
      truncated: false,
      originalLength: content.length,
      processed: true
    };
  }

  private generateEditorLayout(files: any[], layoutType: string = 'tabs'): any {
    if (!Array.isArray(files)) return { layout: 'single', files: [] };
    
    // ✅ OPTIMIZATION: Efficient layout generation based on file count
    if (files.length === 0) {
      return { layout: 'empty', files: [] };
    }
    
    if (files.length === 1) {
      return { layout: 'single', files: files, activeFile: files[0] };
    }
    
    // For multiple files, use tabs layout
    return {
      layout: 'tabs',
      files: files,
      activeFile: files[0],
      tabsVisible: files.length <= 10, // Hide tabs if too many files
      maxTabs: 10
    };
  }

  private validateEditorState(state: any): boolean {
    if (!state || typeof state !== 'object') return false;
    
    // ✅ OPTIMIZATION: Quick validation checks
    const hasRequiredProps = state.content !== undefined && 
                            state.language !== undefined;
    
    const hasValidContent = typeof state.content === 'string';
    const hasValidLanguage = typeof state.language === 'string';
    
    return hasRequiredProps && hasValidContent && hasValidLanguage;
  }

  private cacheResult(key: string, result: any): void {
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      // Remove oldest entry (LRU)
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, result);
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; maxSize: number; hitRatio: number } {
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRatio: this.cache.size > 0 ? Math.min(this.cache.size / this.MAX_CACHE_SIZE, 1) : 0
    };
  }

  // Static utility methods for direct use
  static detectLanguageStatic(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ext ? (MonacoOptimizationPipe.LANGUAGE_MAP.get(ext) || 'plaintext') : 'plaintext';
  }

  static isLargeFile(content: string, threshold: number = 100000): boolean {
    return Boolean(content && content.length > threshold);
  }

  static calculateOptimalFontSize(containerWidth: number): number {
    // ✅ OPTIMIZATION: Calculate optimal font size based on container width
    if (containerWidth < 400) return 12;
    if (containerWidth < 800) return 14;
    if (containerWidth < 1200) return 16;
    return 18;
  }
}

// Supporting types
export type MonacoOperation = 
  | 'detectLanguage'
  | 'optimizeEditorOptions'
  | 'generateModelConfig'
  | 'calculateEditorDimensions'
  | 'optimizeThemeConfig'
  | 'processFileContent'
  | 'generateEditorLayout'
  | 'validateEditorState';

export interface MonacoOptimizationResult {
  data: any;
  cached: boolean;
  optimized: boolean;
  processingTime?: number;
}

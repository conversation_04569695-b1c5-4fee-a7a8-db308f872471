import { Pipe, PipeTransform } from '@angular/core';

import { UIDesignNode } from '../services/wireframe-node-management.service';
import { MultiSelectedNodeData } from '../services/ui-design-selection.service';
/**
 * High-performance memoized pipe for file duplicate analysis
 * Optimizes the heavy computation in identifyTrulyNewFiles method
 */
@Pipe({
  name: 'fileAnalysis',
  pure: true,
  standalone: true
})
export class FileAnalysisPipe implements PipeTransform {
  private cache = new Map<string, FileAnalysisResult>();
  private readonly MAX_CACHE_SIZE = 100;

  transform(
    responseFileNames: string[],
    currentNodes: UIDesignNode[],
    selectedNodes: MultiSelectedNodeData[]
  ): FileAnalysisResult {
    // Generate cache key from input parameters
    const cacheKey = this.generateCacheKey(responseFileNames, currentNodes, selectedNodes);

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Perform optimized analysis
    const result = this.performOptimizedAnalysis(responseFileNames, currentNodes, selectedNodes);

    // Cache result with size management
    this.cacheResult(cacheKey, result);

    return result;
  }

  private generateCacheKey(
    responseFileNames: string[],
    currentNodes: UIDesignNode[],
    selectedNodes: MultiSelectedNodeData[]
  ): string {
    // Create efficient hash-based cache key
    const responseHash = this.hashArray(responseFileNames);
    const nodesHash = this.hashArray(currentNodes.map(n => n.data.title));
    const selectedHash = this.hashArray(selectedNodes.map(n => n.fileName));

    return `${responseHash}_${nodesHash}_${selectedHash}`;
  }

  private hashArray(arr: string[]): string {
    // Simple but effective hash for cache keys
    return arr.sort().join('|').slice(0, 50);
  }

  private performOptimizedAnalysis(
    responseFileNames: string[],
    currentNodes: UIDesignNode[],
    selectedNodes: MultiSelectedNodeData[]
  ): FileAnalysisResult {
    // ✅ OPTIMIZATION: Use Set for O(1) lookups instead of array.includes()
    const existingTitlesSet = new Set(currentNodes.map(node => node.data.title));
    const selectedFileNamesSet = new Set(selectedNodes.map(node => node.fileName));

    // ✅ OPTIMIZATION: Pre-compute normalized versions using Map for O(1) access
    const normalizedExistingMap = new Map<string, string>();
    const normalizedSelectedMap = new Map<string, string>();

    currentNodes.forEach(node => {
      const normalized = this.normalizeFileName(node.data.title);
      normalizedExistingMap.set(normalized, node.data.title);
    });

    selectedNodes.forEach(node => {
      const normalized = this.normalizeFileName(node.fileName);
      normalizedSelectedMap.set(normalized, node.fileName);
    });

    // ✅ OPTIMIZATION: Single pass analysis instead of nested loops
    const newFileNames: string[] = [];
    const duplicatesFiltered: string[] = [];
    const analysisDetails: any[] = [];

    responseFileNames.forEach(fileName => {
      const analysis = this.analyzeFileOptimized(
        fileName,
        existingTitlesSet,
        selectedFileNamesSet,
        normalizedExistingMap,
        normalizedSelectedMap
      );

      analysisDetails.push(analysis);

      if (analysis.isDuplicate) {
        duplicatesFiltered.push(fileName);
      } else {
        newFileNames.push(fileName);
      }
    });

    return {
      newFileNames,
      existingFileNames: Array.from(existingTitlesSet),
      responseFileNames,
      duplicatesFiltered,
      analysisDetails: {
        totalAnalyzed: responseFileNames.length,
        newFilesFound: newFileNames.length,
        duplicatesFiltered: duplicatesFiltered.length,
        individualAnalysis: analysisDetails,
        optimizationUsed: true
      }
    };
  }

  private analyzeFileOptimized(
    fileName: string,
    existingTitlesSet: Set<string>,
    selectedFileNamesSet: Set<string>,
    normalizedExistingMap: Map<string, string>,
    normalizedSelectedMap: Map<string, string>
  ): FileAnalysisItem {
    // ✅ OPTIMIZATION: Early return for selected files
    if (selectedFileNamesSet.has(fileName)) {
      return {
        fileName,
        isDuplicate: true,
        reason: 'File belongs to selected node - should update existing node',
        isSelectedNodeFile: true
      };
    }

    // ✅ OPTIMIZATION: Check normalized selected files
    const normalizedFileName = this.normalizeFileName(fileName);
    if (normalizedSelectedMap.has(normalizedFileName)) {
      return {
        fileName,
        isDuplicate: true,
        reason: 'Normalized file belongs to selected node',
        isSelectedNodeFile: true
      };
    }

    // ✅ OPTIMIZATION: Direct Set lookup instead of array operations
    if (existingTitlesSet.has(fileName)) {
      return {
        fileName,
        isDuplicate: true,
        reason: 'Exact match with existing file',
        isSelectedNodeFile: false
      };
    }

    // ✅ OPTIMIZATION: Normalized lookup using Map
    if (normalizedExistingMap.has(normalizedFileName)) {
      return {
        fileName,
        isDuplicate: true,
        reason: `Normalized match with existing file: ${normalizedExistingMap.get(normalizedFileName)}`,
        isSelectedNodeFile: false
      };
    }

    return {
      fileName,
      isDuplicate: false,
      reason: 'No duplicates found - truly new file',
      isSelectedNodeFile: false
    };
  }

  private normalizeFileName(fileName: string): string {
    // ✅ OPTIMIZATION: Simplified normalization for performance
    return fileName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .trim();
  }

  private cacheResult(key: string, result: FileAnalysisResult): void {
    // ✅ OPTIMIZATION: LRU cache management
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      if (typeof firstKey === 'string') {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, result);
  }

  /**
   * Clear cache when needed (e.g., on major state changes)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Supporting interfaces
export interface FileAnalysisResult {
  newFileNames: string[];
  existingFileNames: string[];
  responseFileNames: string[];
  duplicatesFiltered: string[];
  analysisDetails: any;
}

export interface FileAnalysisItem {
  fileName: string;
  isDuplicate: boolean;
  reason: string;
  isSelectedNodeFile: boolean;
  matchType?: string;
  confidence?: number;
  matchedNodeId?: string;
}

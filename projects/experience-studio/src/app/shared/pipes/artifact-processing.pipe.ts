import { Pipe, PipeTransform } from '@angular/core';

/**
 * High-performance memoized pipe for artifact data processing
 * Optimizes heavy computations in artifact processing methods
 */
@Pipe({
  name: 'artifactProcessing',
  pure: true,
  standalone: true
})
export class ArtifactProcessingPipe implements PipeTransform {
  private cache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 150;

  transform(data: any, operation: ArtifactOperation, ...args: any[]): any {
    // Generate cache key
    const cacheKey = this.generateCacheKey(data, operation, args);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Perform computation
    const result = this.performComputation(data, operation, args);
    
    // Cache result
    this.cacheResult(cacheKey, result);
    
    return result;
  }

  private generateCacheKey(data: any, operation: ArtifactOperation, args: any[]): string {
    const dataHash = this.hashData(data);
    const argsHash = args.length > 0 ? this.hashData(args) : '';
    return `${operation}_${dataHash}_${argsHash}`;
  }

  private hashData(data: any): string {
    if (Array.isArray(data)) {
      return `arr_${data.length}_${JSON.stringify(data.slice(0, 2))}`.slice(0, 80);
    }
    if (typeof data === 'object' && data !== null) {
      const keys = Object.keys(data).slice(0, 5);
      return `obj_${keys.join('_')}`.slice(0, 80);
    }
    return String(data).slice(0, 50);
  }

  private performComputation(data: any, operation: ArtifactOperation, args: any[]): any {
    switch (operation) {
      case 'processProjectOverview':
        return this.processProjectOverview(data);
      
      case 'processLayoutAnalyzed':
        return this.processLayoutAnalyzed(data, args[0]);
      
      case 'processDesignSystem':
        return this.processDesignSystem(data);
      
      case 'validateArtifactStructure':
        return this.validateArtifactStructure(data, args[0]);
      
      case 'extractArtifactContent':
        return this.extractArtifactContent(data, args[0]);
      
      case 'generateArtifactMetadata':
        return this.generateArtifactMetadata(data);
      
      case 'filterDuplicateArtifacts':
        return this.filterDuplicateArtifacts(data, args[0]);
      
      case 'optimizeArtifactData':
        return this.optimizeArtifactData(data);
      
      default:
        return data;
    }
  }

  private processProjectOverview(artifactData: any): any {
    if (!artifactData) return null;

    // ✅ OPTIMIZATION: Early validation
    if (!this.isValidArtifactData(artifactData)) {
      return null;
    }

    try {
      // Handle both old and new formats efficiently
      const parsedData = typeof artifactData.data === 'string' 
        ? JSON.parse(artifactData.data) 
        : artifactData.data;

      // ✅ OPTIMIZATION: Direct property access with fallback
      const content = parsedData.value || parsedData.data || parsedData.content;
      
      if (!content) return null;

      return {
        name: 'Project Overview',
        type: 'markdown',
        content: content,
        processed: true,
        timestamp: Date.now()
      };
    } catch (error) {
      return null;
    }
  }

  private processLayoutAnalyzed(artifactData: any, layoutMapping: any): any {
    if (!artifactData || !layoutMapping) return null;

    // ✅ OPTIMIZATION: Extract layout key efficiently
    const layoutKey = artifactData.layoutKey || artifactData.layoutCode || 'HB';
    
    // Validate layout key against known mappings
    if (!layoutMapping[layoutKey]) {
      return this.createDefaultLayoutArtifact(layoutMapping);
    }

    const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    const layoutName = layoutMapping[layoutKey];

    return {
      name: 'Layout Analyzed',
      type: 'image',
      content: layoutImageUrl,
      layoutData: [{
        key: layoutKey,
        name: layoutName,
        imageUrl: layoutImageUrl
      }],
      processed: true,
      timestamp: Date.now()
    };
  }

  private processDesignSystem(artifactData: any): any {
    if (!artifactData) return null;

    // ✅ OPTIMIZATION: Handle fallback design system efficiently
    const isFallback = artifactData.isFallback || false;
    let tokens = artifactData.tokens || this.generateFallbackTokens();

    // ✅ ENHANCEMENT: Handle new design token structure
    if (artifactData.isNewStructure && artifactData.tokens) {
      // Process new structure with proper validation
      tokens = this.processNewDesignTokenStructure(artifactData.tokens);
    }

    return {
      name: 'Design System',
      type: 'component',
      content: 'Design system tokens and components',
      tokens: tokens,
      isFallback: isFallback,
      isNewStructure: artifactData.isNewStructure || false,
      processed: true,
      timestamp: Date.now()
    };
  }

  /**
   * Process new design token structure from artifact metadata
   * Handles the new format with colors array containing id, name, value, category, editable
   */
  private processNewDesignTokenStructure(tokensData: any): any {
    if (!tokensData) return this.generateFallbackTokens();

    const processedTokens: any = {
      colors: [],
      fonts: tokensData.fonts || [],
      buttons: tokensData.buttons || []
    };

    // Process color tokens from new structure
    if (tokensData.colors && Array.isArray(tokensData.colors)) {
      processedTokens.colors = tokensData.colors.filter((token: any) =>
        this.isValidNewDesignToken(token)
      );
    }

    // If no valid tokens found, use fallback
    if (processedTokens.colors.length === 0) {
      return this.generateFallbackTokens();
    }

    return processedTokens;
  }

  /**
   * Validate new design token structure
   */
  private isValidNewDesignToken(token: any): boolean {
    return token &&
           typeof token.id === 'string' && token.id.trim() !== '' &&
           typeof token.name === 'string' && token.name.trim() !== '' &&
           typeof token.value === 'string' && token.value.trim() !== '' &&
           (typeof token.category === 'string' || token.category === undefined) &&
           (typeof token.editable === 'boolean' || token.editable === undefined);
  }

  private validateArtifactStructure(data: any, expectedType: string): boolean {
    if (!data || typeof data !== 'object') return false;
    
    // ✅ OPTIMIZATION: Quick validation checks
    const hasRequiredFields = data.type && (data.data || data.content || data.value);
    const typeMatches = !expectedType || data.type === expectedType;
    
    return hasRequiredFields && typeMatches;
  }

  private extractArtifactContent(data: any, contentPath: string): any {
    if (!data) return null;

    // ✅ OPTIMIZATION: Efficient nested property access
    const pathParts = contentPath.split('.');
    let current = data;

    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return null;
      }
    }

    return current;
  }

  private generateArtifactMetadata(artifactData: any): any {
    if (!artifactData) return {};

    return {
      id: this.generateArtifactId(artifactData),
      type: artifactData.type || 'unknown',
      size: this.calculateArtifactSize(artifactData),
      created: Date.now(),
      hash: this.hashData(artifactData)
    };
  }

  private filterDuplicateArtifacts(artifacts: any[], keyProperty: string = 'name'): any[] {
    if (!Array.isArray(artifacts)) return [];

    // ✅ OPTIMIZATION: Use Set for O(1) duplicate detection
    const seen = new Set<string>();
    const result: any[] = [];

    for (const artifact of artifacts) {
      const key = artifact[keyProperty];
      if (!seen.has(key)) {
        seen.add(key);
        result.push(artifact);
      }
    }

    return result;
  }

  private optimizeArtifactData(data: any): any {
    if (!data) return data;

    // ✅ OPTIMIZATION: Remove unnecessary properties and optimize structure
    const optimized: any = {
      name: data.name,
      type: data.type,
      content: data.content
    };

    // Only include additional properties if they exist and are meaningful
    if (data.tokens) optimized.tokens = data.tokens;
    if (data.layoutData) optimized.layoutData = data.layoutData;
    if (data.metadata) optimized.metadata = data.metadata;

    return optimized;
  }

  private isValidArtifactData(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           (data.data || data.content || data.value);
  }

  private createDefaultLayoutArtifact(layoutMapping: any): any {
    const defaultKey = 'HB';
    return {
      name: 'Layout Analyzed',
      type: 'image',
      content: `assets/images/layout-${defaultKey}.png`,
      layoutData: [{
        key: defaultKey,
        name: layoutMapping[defaultKey] || 'Header + Body',
        imageUrl: `assets/images/layout-${defaultKey}.png`
      }],
      processed: true,
      isDefault: true,
      timestamp: Date.now()
    };
  }

  private generateFallbackTokens(): any {
    return {
      colors: {
        primary: '#007bff',
        secondary: '#6c757d',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545'
      },
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: {
          small: '14px',
          medium: '16px',
          large: '18px'
        }
      },
      spacing: {
        small: '8px',
        medium: '16px',
        large: '24px'
      }
    };
  }

  private generateArtifactId(data: any): string {
    const timestamp = Date.now();
    const hash = this.hashData(data).slice(0, 8);
    return `artifact_${timestamp}_${hash}`;
  }

  private calculateArtifactSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  private cacheResult(key: string, result: any): void {
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, result);
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; maxSize: number; hitRatio: number } {
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRatio: this.cache.size > 0 ? Math.min(this.cache.size / this.MAX_CACHE_SIZE, 1) : 0
    };
  }
}

// Supporting types
export type ArtifactOperation = 
  | 'processProjectOverview'
  | 'processLayoutAnalyzed'
  | 'processDesignSystem'
  | 'validateArtifactStructure'
  | 'extractArtifactContent'
  | 'generateArtifactMetadata'
  | 'filterDuplicateArtifacts'
  | 'optimizeArtifactData';

export interface ArtifactProcessingResult {
  data: any;
  cached: boolean;
  processingTime?: number;
}

import { Pipe, PipeTransform } from '@angular/core';

/**
 * ✅ TERMINAL FIX: Simplified log processing pipe
 * Optimizes log parsing and formatting while reducing complexity
 */
@Pipe({
  name: 'logProcessing',
  pure: true,
  standalone: true
})
export class LogProcessingPipe implements PipeTransform {
  private cache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 100;

  transform(logs: string[], operation: LogOperation, ...args: any[]): any {
    if (!Array.isArray(logs)) return [];

    // Generate cache key
    const cacheKey = this.generateCacheKey(logs, operation, args);

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Perform operation
    const result = this.performOperation(logs, operation, args);

    // Cache result
    this.cacheResult(cacheKey, result);

    return result;
  }

  private generateCacheKey(logs: string[], operation: LogOperation, args: any[]): string {
    const logsHash = logs.length > 0 ? `${logs.length}_${logs[0].slice(0, 20)}` : 'empty';
    const argsHash = args.length > 0 ? JSON.stringify(args).slice(0, 20) : '';
    return `${operation}_${logsHash}_${argsHash}`;
  }

  private performOperation(logs: string[], operation: LogOperation, args: any[]): any {
    switch (operation) {
      case 'filterDuplicates':
        return this.filterDuplicates(logs);

      case 'formatLogs':
        return this.formatLogs(logs);

      case 'extractErrors':
        return this.extractErrors(logs);

      case 'extractCodeBlocks':
        return this.extractCodeBlocks(logs);

      case 'simplifyLogs':
        return this.simplifyLogs(logs);

      case 'parseTimestamps':
        return this.parseTimestamps(logs);

      default:
        return logs;
    }
  }

  /**
   * ✅ TERMINAL FIX: Simplified duplicate filtering
   * Uses Set for O(1) lookups instead of complex hash checking
   */
  private filterDuplicates(logs: string[]): string[] {
    const seen = new Set<string>();
    const result: string[] = [];

    for (const log of logs) {
      const trimmedLog = log.trim();
      if (trimmedLog && !seen.has(trimmedLog)) {
        seen.add(trimmedLog);
        result.push(log);
      }
    }

    return result;
  }

  /**
   * ✅ TERMINAL FIX: Simplified log formatting
   * Reduces complexity while maintaining functionality
   */
  private formatLogs(logs: string[]): any[] {
    return logs.map((log, index) => {
      const timestamp = this.extractTimestamp(log);
      const level = this.extractLogLevel(log);
      const content = this.extractLogContent(log);

      return {
        id: `log_${index}_${Date.now()}`,
        type: level.toLowerCase(),
        timestamp: timestamp,
        content: content,
        rawLog: log,
        visibleContent: content // For immediate display
      };
    });
  }

  /**
   * ✅ TERMINAL FIX: Extract error logs with enhanced filtering
   */
  private extractErrors(logs: string[]): any[] {
    const errorKeywords = ['ERROR', 'FAILED', 'Exception', 'FATAL', 'CRITICAL', 'CRASH', 'ABORT'];

    return logs
      .filter(log => {
        const upperLog = log.toUpperCase();
        return errorKeywords.some(keyword => upperLog.includes(keyword));
      })
      .map((log, index) => ({
        id: `error_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'error',
        timestamp: this.extractTimestamp(log),
        content: this.extractLogContent(log),
        rawLog: log,
        severity: this.determineSeverity(log),
        formatted: this.formatErrorForTerminal(log)
      }));
  }

  /**
   * Determine error severity level
   */
  private determineSeverity(log: string): 'low' | 'medium' | 'high' | 'critical' {
    const upperLog = log.toUpperCase();
    if (upperLog.includes('FATAL') || upperLog.includes('CRITICAL') || upperLog.includes('CRASH')) {
      return 'critical';
    }
    if (upperLog.includes('ERROR') || upperLog.includes('EXCEPTION')) {
      return 'high';
    }
    if (upperLog.includes('FAILED') || upperLog.includes('ABORT')) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Format error for terminal display
   */
  private formatErrorForTerminal(log: string): string {
    const timestamp = this.extractTimestamp(log) || new Date().toLocaleTimeString();
    const content = this.extractLogContent(log);

    return `[${timestamp}] ERROR: ${content}`;
  }

  /**
   * ✅ TERMINAL FIX: Simplified code block extraction
   * Focuses on clear JSON patterns without over-processing
   */
  private extractCodeBlocks(logs: string[]): any[] {
    const codeBlocks: any[] = [];

    for (const log of logs) {
      // Look for JSON patterns that likely contain code
      if (this.containsCodeData(log)) {
        try {
          const codeData = this.extractCodeFromLog(log);
          if (codeData) {
            codeBlocks.push({
              id: `code_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              type: 'code',
              timestamp: this.extractTimestamp(log),
              path: codeData.path || 'unknown.file',
              content: codeData.content,
              contentLines: codeData.content.split('\n').length,
              contentSize: codeData.content.length,
              rawLog: log
            });
          }
        } catch (error) {
          // Skip malformed JSON, continue processing
          continue;
        }
      }
    }

    return codeBlocks;
  }

  /**
   * ✅ TERMINAL FIX: Simplified log processing
   * Removes complex nested logic for better performance
   */
  private simplifyLogs(logs: string[]): any[] {
    const simplified: any[] = [];

    for (const log of logs) {
      const trimmedLog = log.trim();
      if (!trimmedLog) continue;

      // Skip overly verbose logs
      if (this.isVerboseLog(trimmedLog)) continue;

      // Create simplified log entry
      simplified.push({
        id: `simple_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
        type: this.getSimpleLogType(trimmedLog),
        timestamp: this.extractTimestamp(trimmedLog),
        content: this.cleanLogContent(trimmedLog),
        rawLog: log
      });
    }

    return simplified;
  }

  /**
   * ✅ TERMINAL FIX: Parse timestamps efficiently
   */
  private parseTimestamps(logs: string[]): any[] {
    return logs.map(log => ({
      log: log,
      timestamp: this.extractTimestamp(log),
      parsedTime: this.parseTimestamp(this.extractTimestamp(log))
    }));
  }

  // Helper methods

  private extractTimestamp(log: string): string {
    // Look for timestamp patterns: HH:MM:SS or HH:MM:SS.mmm
    const timestampMatch = log.match(/(\d{2}:\d{2}:\d{2}(?:\.\d{3})?)/);
    return timestampMatch ? timestampMatch[1] : '';
  }

  private extractLogLevel(log: string): string {
    if (log.includes('ERROR')) return 'ERROR';
    if (log.includes('WARN')) return 'WARN';
    if (log.includes('DEBUG')) return 'DEBUG';
    return 'INFO';
  }

  private extractLogContent(log: string): string {
    // Remove timestamp and level prefix
    const parts = log.split(' - ');
    return parts.length >= 3 ? parts.slice(2).join(' - ') : log;
  }

  private containsCodeData(log: string): boolean {
    return log.includes('{') && log.includes('}') &&
           (log.includes('"fileName"') || log.includes('"content"') || log.includes('"path"'));
  }

  private extractCodeFromLog(log: string): { path: string; content: string } | null {
    try {
      const jsonStart = log.indexOf('{');
      const jsonEnd = log.lastIndexOf('}') + 1;

      if (jsonStart === -1 || jsonEnd <= jsonStart) return null;

      const jsonStr = log.substring(jsonStart, jsonEnd);
      const data = JSON.parse(jsonStr);

      // Handle different JSON structures
      if (data.fileName && data.content) {
        return { path: data.fileName, content: data.content };
      }

      if (typeof data === 'object' && !Array.isArray(data)) {
        // Handle key-value pairs where key is path and value is content
        const entries = Object.entries(data);
        if (entries.length > 0) {
          const [path, content] = entries[0];
          return { path, content: String(content) };
        }
      }

      return null;
    } catch {
      return null;
    }
  }

  private isVerboseLog(log: string): boolean {
    // Skip logs that are too verbose or not useful
    const verbosePatterns = [
      'Raw response:',
      'Processing response:',
      'Received data:',
      'Debug info:'
    ];

    return verbosePatterns.some(pattern => log.includes(pattern));
  }

  private getSimpleLogType(log: string): string {
    if (log.includes('ERROR') || log.includes('Failed')) return 'error';
    if (log.includes('WARN') || log.includes('Warning')) return 'warning';
    if (log.includes('Status changed')) return 'status';
    if (log.includes('Progress')) return 'progress';
    return 'info';
  }

  private cleanLogContent(log: string): string {
    // Remove common prefixes and clean up content
    let cleaned = log;

    // Remove timestamp prefix
    cleaned = cleaned.replace(/^\d{2}:\d{2}:\d{2}(?:\.\d{3})?\s*-\s*\w+\s*-\s*/, '');

    // Remove excessive whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    return cleaned;
  }

  private parseTimestamp(timestamp: string): Date | null {
    if (!timestamp) return null;

    try {
      const today = new Date();
      const [time] = timestamp.split('.');
      const [hours, minutes, seconds] = time.split(':').map(Number);

      const date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);
      return date;
    } catch {
      return null;
    }
  }

  private cacheResult(key: string, result: any): void {
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      if (typeof firstKey === 'string') {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, result);
  }

  clearCache(): void {
    this.cache.clear();
  }
}

// Supporting types
export type LogOperation =
  | 'filterDuplicates'
  | 'formatLogs'
  | 'extractErrors'
  | 'extractCodeBlocks'
  | 'simplifyLogs'
  | 'parseTimestamps';

export interface ProcessedLog {
  id: string;
  type: string;
  timestamp: string;
  content: string;
  rawLog: string;
  visibleContent?: string;
  path?: string;
  contentLines?: number;
  contentSize?: number;
}

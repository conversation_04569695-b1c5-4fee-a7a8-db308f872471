export interface ArtifactType {
  id: string;
  name: string;
  description: string;
  extension: string;
  icon?: string;
}

export interface Artifact {
  id: string;
  type: ArtifactType;
  content: string;
  fileName: string;
  timestamp: Date;
  metadata?: {
    [key: string]: any;
  };
}

export interface ArtifactState {
  items: Artifact[];
  selectedId?: string;
  loading: boolean;
  error?: string;
}

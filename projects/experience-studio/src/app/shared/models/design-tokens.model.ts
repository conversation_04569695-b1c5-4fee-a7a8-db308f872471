export enum DesignToken {
  Colors = 'colors',
  Typography = 'typography',
  Shadows = 'shadows',
  Grid = 'grid',
  Spacing = 'spacing',
}

export interface UIDesignAPIResponse {
  tokens: {
    [key: string]: any;
  };
  error?: string;
}

export interface DesignTokenState {
  selected: DesignToken;
  data: {
    [key in DesignToken]?: any;
  };
  loading: boolean;
  error?: string;
}

/**
 * Interface for design tokens from artifact metadata
 * Matches the structure from metadata[].data.data.design_tokens.colors[]
 */
export interface ArtifactDesignToken {
  id: string;
  name: string;
  value: string;
  category: string;
  editable: boolean;
}

/**
 * Interface for design tokens container from artifact metadata
 */
export interface ArtifactDesignTokens {
  colors: ArtifactDesignToken[];
}

/**
 * Interface for artifact metadata containing design tokens
 */
export interface DesignTokenArtifactData {
  data: {
    design_tokens: ArtifactDesignTokens;
  };
  type: 'json';
}

/**
 * Interface for design token edit mode state
 */
export interface DesignTokenEditState {
  isEditMode: boolean;
  editButtonText: 'Edit' | 'Save' | 'Done';
  hasUnsavedChanges: boolean;
  originalValues: Map<string, string>;
}

/**
 * Interface for design token loading state
 */
export interface DesignTokenLoadingState {
  isLoading: boolean;
  showAnalyzingAnimation: boolean;
  hasReceivedTokens: boolean;
  loadingMessage: string;
}

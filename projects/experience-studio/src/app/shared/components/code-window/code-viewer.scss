// ===== VS CODE-LIKE CODE VIEWER MODAL =====
.code-viewer-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.75);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  backdrop-filter: blur(12px);

  .code-viewer-modal {
    background: var(--vscode-bg, #1e1e1e);
    border-radius: 12px;
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(255, 255, 255, 0.05);
    width: 95vw;
    height: 90vh;
    max-width: 1400px;
    max-height: 900px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideInScale 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    border: 1px solid var(--vscode-border, #3c3c3c);

    // VS Code-like Title Bar
    .code-viewer-titlebar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 35px;
      background: var(--vscode-titlebar-bg, #323233);
      border-bottom: 1px solid var(--vscode-border, #3c3c3c);
      padding: 0 16px;
      user-select: none;

      .titlebar-left {
        display: flex;
        align-items: center;

        .file-tab {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 6px 12px;
          background: var(--vscode-tab-bg, #2d2d30);
          border-radius: 6px 6px 0 0;
          border: 1px solid var(--vscode-border, #3c3c3c);
          border-bottom: none;

          .file-icon {
            width: 16px;
            height: 16px;
            filter: invert(0.7);
          }

          .file-name {
            font-size: 13px;
            color: var(--vscode-text, #cccccc);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 500;
          }
        }
      }

      .titlebar-center {
        .app-title {
          font-size: 13px;
          color: var(--vscode-text-muted, #8c8c8c);
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 500;
        }
      }

      .titlebar-right {
        display: flex;
        gap: 8px;

        .action-button {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          border: 1px solid transparent;
          border-radius: 6px;
          background: transparent;
          color: var(--vscode-text, #cccccc);
          font-size: 12px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          cursor: pointer;
          transition: all 0.2s ease;

          .action-icon {
            width: 14px;
            height: 14px;
            filter: invert(0.8);
          }

          &:hover {
            background: var(--vscode-button-hover, #2a2d2e);
            border-color: var(--vscode-border, #3c3c3c);
          }

          &.copy-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
            color: #60a5fa;
          }

          &.download-btn:hover {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #34d399;
          }

          &.close-btn {
            padding: 6px 8px;

            &:hover {
              background: rgba(239, 68, 68, 0.1);
              border-color: rgba(239, 68, 68, 0.3);
              color: #f87171;
            }
          }
        }
      }
    }

    // VS Code-like Editor Area
    .code-editor-container {
      flex: 1;
      display: flex;
      background: var(--vscode-editor-bg, #1e1e1e);
      overflow: hidden;

      .line-numbers {
        background: var(--vscode-gutter-bg, #1e1e1e);
        border-right: 1px solid var(--vscode-border, #3c3c3c);
        padding: 16px 8px;
        min-width: 60px;
        text-align: right;
        user-select: none;
        overflow: hidden;
        top: 0;

        .line-number {
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
          font-size: 13px;
          line-height: 20px;
          color: var(--vscode-line-numbers, #858585);
          padding-right: 16px;
          white-space: nowrap;
        }
      }

      .code-content {
        flex: 1;
        overflow: auto;
        background: var(--vscode-editor-bg, #1e1e1e);

        .code-display {
          margin: 0;
          padding: 16px 20px;
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
          font-size: 13px;
          line-height: 20px;
          color: var(--vscode-text, #d4d4d4);
          background: transparent;
          white-space: pre;
          overflow-wrap: normal;
          word-break: normal;

          code {
            font-family: inherit;
            font-size: inherit;
            color: inherit;
            background: transparent;
            padding: 0;
          }

          // Syntax highlighting
          .html-tag { color: #569cd6; }
          .html-tag-name { color: #4fc1ff; }
          .html-attributes { color: #d4d4d4; }
          .html-attr-name { color: #92c5f8; }
          .html-operator { color: #d4d4d4; }
          .html-attr-value { color: #ce9178; }
          .html-comment { color: #6a9955; font-style: italic; }
          .html-doctype { color: #569cd6; }
        }
      }
    }


  }
}

// Dark theme adjustments
:host-context(.dark-theme) .code-viewer-overlay .code-viewer-modal {
  --vscode-bg: #1e1e1e;
  --vscode-titlebar-bg: #323233;
  --vscode-tab-bg: #2d2d30;
  --vscode-editor-bg: #1e1e1e;
  --vscode-gutter-bg: #1e1e1e;
  --vscode-statusbar-bg: #007acc;
  --vscode-border: #3c3c3c;
  --vscode-text: #d4d4d4;
  --vscode-text-muted: #8c8c8c;
  --vscode-line-numbers: #858585;
  --vscode-button-hover: #2a2d2e;
}

// Light theme adjustments
:host-context(.light-theme) .code-viewer-overlay .code-viewer-modal {
  --vscode-bg: #ffffff;
  --vscode-titlebar-bg: #f3f3f3;
  --vscode-tab-bg: #ffffff;
  --vscode-editor-bg: #ffffff;
  --vscode-gutter-bg: #f8f8f8;
  --vscode-statusbar-bg: #007acc;
  --vscode-border: #e1e1e1;
  --vscode-text: #333333;
  --vscode-text-muted: #6c6c6c;
  --vscode-line-numbers: #237893;
  --vscode-button-hover: #f0f0f0;

  .code-display {
    .html-tag { color: #800000; }
    .html-tag-name { color: #800000; }
    .html-attr-name { color: #ff0000; }
    .html-attr-value { color: #0000ff; }
    .html-comment { color: #008000; }
    .html-doctype { color: #800080; }
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

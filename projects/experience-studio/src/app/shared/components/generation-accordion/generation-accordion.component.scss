.generation-accordion {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Mulish', sans-serif;
  border: 1px solid;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  // Super responsive design with multiple breakpoints
  @media (max-width: 1024px) {
    margin: 7px 0;
    border-radius: 7px;
  }

  @media (max-width: 768px) {
    margin: 6px 0;
    border-radius: 6px;
  }

  @media (max-width: 640px) {
    margin: 5px 0;
    border-radius: 5px;
  }

  @media (max-width: 480px) {
    margin: 4px 0;
    border-radius: 4px;
  }

  @media (max-width: 360px) {
    margin: 3px 0;
    border-radius: 3px;
  }

  // Light theme (default) - Match AI card colors exactly
  &.light {
    background-color: var(--Neutral-N-50, var(--color-background-light)) !important;
    border-color: var(--color-border-light, #e0e0e0) !important;
    color: var(--text-black) !important;

    .accordion-header {

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .version-title {
        color: var(--text-black);
        font-weight: 500;
      }

      .file-count {
        color: var(--text-black);
        opacity: 0.7;
      }

      .timestamp {
        color: var(--text-black);
        opacity: 0.6;
      }

      .expand-icon {
        color: var(--text-black);
        opacity: 0.7;
      }
    }


    .file-name {
      color: var(--text-black) !important;
    }

    .file-item {
      background-color: rgba(0, 0, 0, 0.03) !important;
      border: 1px solid var(--color-border-light, #e0e0e0) !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05) !important;
      }
    }

    .error-text {
      color: #dc3545;
      background-color: #f8d7da;
      border-color: #f5c6cb;
    }

    .error-label {
      color: #721c24;
    }

    .retry-suggestion {
      color: #6c757d;
    }
  }

  // Dark theme - Match AI card colors exactly
  &.dark {
    border-color: var(--color-border-dark, #404040) !important;
    color: var(--text-white) !important;

    .accordion-header {

      &:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }

      .version-title {
        color: var(--text-white);
        font-weight: 500;
      }

      .file-count {
        color: var(--text-white);
        opacity: 0.7;
      }

      .timestamp {
        color: var(--text-white);
        opacity: 0.6;
      }

      .expand-icon {
        color: var(--text-white);
        opacity: 0.7;
      }
    }



    .file-name {
      color: var(--text-white) !important;
    }

    .file-item {
      background-color: rgba(255, 255, 255, 0.03) !important;
      border: 1px solid var(--color-border-dark, #404040) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
    }

    .error-text {
      color: #feb2b2;
      background-color: #742a2a;
      border-color: #9b2c2c;
    }

    .error-label {
      color: #fed7d7;
    }

    .retry-suggestion {
      color: #a0aec0;
    }
  }


  // Error result styling
  &.result-error {
    .accordion-header {
      .result-icon {
        color: #dc3545;
      }
    }
  }

  // Compact mode styling - responsive behavior based on width
  &.compact-mode {
    .header-content {
      .version-title {
        // In compact mode, version title is already shortened via getVersionTitle()
        font-weight: 600; // Make it slightly bolder when compact
      }

      .timestamp {
        // Timestamp is hidden via JavaScript when in compact mode
        // This is a fallback CSS rule
        display: none !important;
      }

      .file-count {
        // Keep file count visible in compact mode but make it smaller
        font-size: 11px;
        opacity: 0.8;

        @media (max-width: 480px) {
          font-size: 10px;
        }

        @media (max-width: 360px) {
          display: none; // Hide on very small screens
        }
      }
    }
  }
}

.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Super responsive padding with multiple breakpoints
  @media (max-width: 1024px) {
    padding: 11px 15px;
  }

  @media (max-width: 768px) {
    padding: 10px 14px;
  }

  @media (max-width: 640px) {
    padding: 9px 12px;
  }

  @media (max-width: 480px) {
    padding: 8px 10px;
  }

  @media (max-width: 360px) {
    padding: 7px 8px;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0; // Allow content to shrink

    .version-title {
      font-weight: 500;
      font-size: 14px;
      flex-shrink: 0;

      @media (max-width: 768px) {
        font-size: 13px;
      }

      @media (max-width: 480px) {
        font-size: 12px;
      }

      @media (max-width: 360px) {
        font-size: 11px;
      }
    }



    .file-count {
      font-size: 12px;
      font-weight: 400;
      flex-shrink: 0;

      @media (max-width: 768px) {
        font-size: 11px;
      }

      @media (max-width: 480px) {
        font-size: 10px;
      }

      @media (max-width: 360px) {
        display: none; // Hide file count on very small screens
      }
    }

    .timestamp {
      font-size: 11px;
      margin-left: auto;
      flex-shrink: 0;
      transition: opacity 0.3s ease, visibility 0.3s ease;

      @media (max-width: 768px) {
        font-size: 10px;
      }

      @media (max-width: 640px) {
        display: none; // Hide timestamp on smaller screens
      }

      // Smooth transition when hidden via JavaScript
      &[style*="display: none"] {
        opacity: 0;
        visibility: hidden;
      }
    }
  }

  .expand-icon {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;

    &.expanded {
      transform: rotate(180deg);
    }

    svg {
      display: block;

      @media (max-width: 480px) {
        width: 14px;
        height: 14px;
      }
    }
  }
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;

  &.expanded {
    max-height: 800px;
    overflow-y: auto;
    opacity: 1;
    transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .content-inner {
    padding: 4px;
  }
}

.success-content {
  .files-list {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &.clickable {
        cursor: pointer;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .light & {
          &:hover {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }

        .dark & {
          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }
      }

      &:not(.clickable) {
        cursor: default;
        opacity: 0.7;
      }

      // Super responsive padding with multiple breakpoints
      @media (max-width: 1024px) {
        padding: 7px 11px;
        border-radius: 3px;
      }

      @media (max-width: 768px) {
        padding: 6px 10px;
        border-radius: 3px;
      }

      @media (max-width: 640px) {
        padding: 5px 9px;
        border-radius: 2px;
      }

      @media (max-width: 480px) {
        padding: 4px 8px;
        border-radius: 2px;
      }

      @media (max-width: 360px) {
        padding: 3px 6px;
        border-radius: 2px;
      }

      .file-name {
        font-size: 13px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        flex: 1;
        word-break: break-all;
        line-height: 1.4;

        @media (max-width: 768px) {
          font-size: 12px;
        }

        @media (max-width: 480px) {
          font-size: 11px;
          line-height: 1.3;
        }

        @media (max-width: 360px) {
          font-size: 10px;
          line-height: 1.2;
        }
      }
    }
  }
}

.error-content {
  .error-message {
    margin-bottom: 12px;

    .error-label {
      font-weight: 600;
      font-size: 13px;
      margin-bottom: 6px;
    }

    .error-text {
      padding: 12px;
      border-radius: 4px;
      font-size: 13px;
      line-height: 1.4;
      border: 1px solid;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  .error-actions {
    .retry-suggestion {
      font-size: 12px;
      font-style: italic;
    }
  }
}

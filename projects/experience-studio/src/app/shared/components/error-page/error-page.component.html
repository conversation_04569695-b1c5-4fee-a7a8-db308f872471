<div class="error-container" [ngClass]="theme" [@fadeInUp]>
  <div class="error-content">
    <!-- ERROR Text -->
    <div class="error-text" [@errorTextAnimation]>
      <div class="error-word-section">
        <!-- E -->
        <div class="error-char-container" (mouseenter)="onLetterMouseEnter(0)" (mouseleave)="onLetterMouseLeave(0)">
          <span class="error-char gray">E</span>
          <div class="char-interactive-area" [ngClass]="{'active': (errorLetters$ | async)?.[0]?.state === 'hovered'}">
            <div class="char-card">
              <span class="char-number black">E</span>
            </div>
            <div class="animation-content">
              Execution Halt
            </div>
          </div>
        </div>

        <!-- R -->
        <div class="error-char-container" (mouseenter)="onLetterMouseEnter(1)" (mouseleave)="onLetterMouseLeave(1)">
          <span class="error-char gray">R</span>
          <div class="char-interactive-area" [ngClass]="{'active': (errorLetters$ | async)?.[1]?.state === 'hovered'}">
            <div class="char-card">
              <span class="char-number black">R</span>
            </div>
            <div class="animation-content">
              Request Issue
            </div>
          </div>
        </div>

        <!-- R -->
        <div class="error-char-container" (mouseenter)="onLetterMouseEnter(2)" (mouseleave)="onLetterMouseLeave(2)">
          <span class="error-char gray">R</span>
          <div class="char-interactive-area" [ngClass]="{'active': (errorLetters$ | async)?.[2]?.state === 'hovered'}">
            <div class="char-card">
              <span class="char-number black">R</span>
            </div>
            <div class="animation-content">
              Result: Failure
            </div>
          </div>
        </div>

        <!-- O -->
        <div class="error-char-container" (mouseenter)="onLetterMouseEnter(3)" (mouseleave)="onLetterMouseLeave(3)">
          <span class="error-char gray">O</span>
          <div class="char-interactive-area" [ngClass]="{'active': (errorLetters$ | async)?.[3]?.state === 'hovered'}">
            <div class="char-card">
              <span class="char-number black">O</span>
            </div>
            <div class="animation-content">
              Operation Blocked
            </div>
          </div>
        </div>

        <!-- R -->
        <div class="error-char-container" (mouseenter)="onLetterMouseEnter(4)" (mouseleave)="onLetterMouseLeave(4)">
          <span class="error-char gray">R</span>
          <div class="char-interactive-area" [ngClass]="{'active': (errorLetters$ | async)?.[4]?.state === 'hovered'}">
            <div class="char-card">
              <span class="char-number black">R</span>
            </div>
            <div class="animation-content">
              Retry/Report
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div class="error-message" [@fadeInUp]>
      <p class="animated-text">{{ animatedDescription$ | async }}<span class="cursor-blink">|</span></p>
      <div class="progress-state" *ngIf="progressState">
        <span class="badge">{{ progressState }}</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <!-- <div class="action-buttons" [@fadeInUp]>
      <button class="retry-button" (click)="onRetryClick()">
        <i class="bi bi-arrow-clockwise"></i> Retry
      </button>
      <button
        class="home-button"
        [ngClass]="{'disabled': !(homeButtonEnabled$ | async)}"
        (click)="onGoHomeClick()"
        [title]="(homeButtonEnabled$ | async) ? 'Go Home' : 'Retry ' + (3 - (retryCount$ | async)!) + ' more times to enable'">
        <i class="bi bi-house"></i> Go Home
        <span class="retry-counter" *ngIf="!(homeButtonEnabled$ | async) && (retryCount$ | async)! > 0">{{ retryCount$ | async }}/3</span>
      </button>
    </div> -->
  </div>
</div>


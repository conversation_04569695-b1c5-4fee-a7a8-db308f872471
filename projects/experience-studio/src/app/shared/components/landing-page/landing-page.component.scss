#main-content-container {
  margin-top: 7%;
  .studio-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    .studio-card {
      border: 1px solid var(--code-viewer-border) !important;
      background-color: var(--code-viewer-bg) !important;
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }
      &:hover {
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        &::before {
          opacity: 1;
        }
      }
      img {
        width: auto;
        height: auto;
        object-fit: contain;
      }
    }

    // Empty state placeholder styling
    .no-cards-placeholder {
      grid-column: 1 / -1; // Span all columns
      min-height: 280px;
      color: var(--text-secondary);

      .placeholder-icon {
        font-size: 3rem;
        opacity: 0.6;
      }

      .placeholder-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
      }

      .placeholder-description {
        font-size: 1rem;
        opacity: 0.8;
        max-width: 400px;
      }
    }
  }
  @media (max-width: 1024px) {
    .studio-cards-grid .studio-card {
      height: 260px;
      img {
        max-height: 120px;
      }
    }
  }
  @media (max-width: 767px) {
    .studio-cards-grid {
      grid-template-columns: 1fr;
      .studio-card {
        height: 240px;
        img {
          max-height: 100px;
        }
      }
    }
  }
  @media (max-width: 480px) {
    .studio-cards-grid .studio-card {
      height: 220px;
      img {
        max-height: 80px;
      }
    }
  }
  @media (min-width: 1420px) {
    .studio-cards-grid {
      max-width: 1200px;
    }
  }
}

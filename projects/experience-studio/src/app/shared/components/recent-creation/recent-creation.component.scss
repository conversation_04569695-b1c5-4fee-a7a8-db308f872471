@use '../../../../assets/styles/mixins' as mixins;

// Efficient text truncation mixins
@mixin text-truncate($lines: 1) {
  display: -webkit-box !important;
  -webkit-line-clamp: $lines !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  @if $lines > 1 { text-overflow: ellipsis !important; }
}

#recent-creation-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;

  // Responsive padding
  @media (max-width: 768px) {
    padding: 16px;
  }

  @media (max-width: 480px) {
    padding: 12px;
  }
  .toggle-layout {
    width: 280px;
    margin: 0 auto; // Center the toggle layout
    border-radius: 12px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(7.5px);
    @include mixins.transition(all 0.3s ease);
    border: 1px solid rgba(155, 155, 155, 0.2);

    // Responsive behavior for smaller screens
    @media (max-width: 480px) {
      width: 240px;
    }

    &:hover {
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
    }
    .toggle-btn {
      background: transparent;
      font-size: 16px;
      border:none;
      cursor: pointer;
      &.active {
        background: linear-gradient(
          90deg,
          var(--prompt-bar-hover-color) 0%,
          var(--prompt-bar-border-color) 100%
        );
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      &:hover:not(.active) {
        @include mixins.button-hover;
      }
    }
  }

  #cards-container {
    width: 100%;
    flex: 1; // Allow container to grow and take available space

    .cards-grid {
      width: 100%;
      @include mixins.transition(all 0.6s cubic-bezier(0.4, 0, 0.2, 1));
      &.slide-recent {
        transform: translateX(0);
        animation: slideInFromLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      &.slide-all {
        transform: translateX(0);
        animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .skeleton-card {
        pointer-events: none;
      }
      .card-content {
        min-height: 13rem;
        cursor: pointer;
        @include mixins.transition(all 0.2s ease);
      }
      .action-timestamp-container {
        position: absolute;
        bottom: 16px;
        left: 16px;
        right: 16px;
        .card-action-btn {
          border-radius: 6px;
          @include mixins.transition(all 0.2s ease);
        }
        .timestamp {
          color: #a1a1aa;
        }
      }

      // Empty state placeholder styling
      .no-projects-placeholder {
        min-height: 300px;
        color: var(--text-secondary);

        .placeholder-icon {
          font-size: 3rem;
          opacity: 0.6;
        }

        .placeholder-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--text-primary);
        }

        .placeholder-description {
          font-size: 1rem;
          opacity: 0.8;
          max-width: 500px;
        }
      }
    }
  }
}

// Themes
:host-context(.dark-theme) {
  .toggle-layout {
    background: var(--feature-card-bg);
  }
  .toggle-btn {
    color: rgba(255, 255, 255, 0.6);
    &:hover:not(.active) {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  .card-action-btn {
    background-color: #33364d;
    color: white;
    &:hover {
      background-color: #3e4154;
    }
  }
  .timestamp {
    color: #a1a1aa;
  }
}

:host-context(.light-theme) {
  .toggle-btn {
    color: rgba(20, 22, 31, 0.6);
    &:hover:not(.active) {
      color: rgba(20, 22, 31, 0.8);
    }
  }
  .card-action-btn {
    background-color: #fcc1dc;
    color: #33364d;
    &:hover {
      background-color: #f9b0d1;
    }
  }
  .timestamp {
    color: var(--main-subtitle);
  }
}

:host ::ng-deep awe-cards {
  width: 100%; // Ensure awe-cards takes full width
  display: block; // Make sure it's a block element

  .awe-card {
    min-height: 150px;
    padding: 0 !important;
    border-radius: 12px !important;
    width: 100%; // Ensure card takes full width of its container
  }
}

.skeleton-title,
.skeleton-text {
  width: 100%;
  border-radius: 4px;
  background: linear-gradient(to right, #f0f0f0 8%, #e0e0e0 18%, #f0f0f0 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;

  &:last-of-type {
    width: 80%;
  }
}

.skeleton-title {
  height: 24px;
  margin-bottom: 12px;
}

.skeleton-text {
  height: 16px;
  margin-bottom: 8px;
}

// Dark theme for skeletons
:host-context(.dark-theme) {
  .skeleton-title,
  .skeleton-text,
  .skeleton-button,
  .skeleton-timestamp {
    background: linear-gradient(to right, #333 8%, #444 18%, #333 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  ::ng-deep awe-heading {
    color: white !important;
    @include text-truncate(1);
  }

  ::ng-deep awe-body-text {
    color: #a1a1aa !important;
    @include text-truncate(2);
  }

  ::ng-deep awe-cards .awe-card {
    @include mixins.card-styles(
      rgba(20, 22, 31, 0.24),
      #292c3d,
      0px 2px 4px 0px rgba(20, 22, 31, 0.08)
    );
  }

  ::ng-deep awe-cards .awe-card:hover {
    border-color: var(--prompt-bar-border-color) !important;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1) !important;
  }
}

:host-context(.light-theme) {
  ::ng-deep awe-heading {
    color: #14161f !important;
  }

  ::ng-deep awe-body-text {
    color: var(--main-subtitle) !important;
  }

  ::ng-deep awe-cards .awe-card {
    @include mixins.card-styles(
      rgba(240, 240, 245, 0.24),
      #f0f0f5,
      0px 2px 4px 0px rgba(0, 0, 0, 0.05)
    );
  }

  ::ng-deep awe-cards .awe-card:hover {
    border-color: var(--prompt-bar-border-color) !important;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08) !important;
  }
}

// Global text truncation rules - applies to all themes
::ng-deep awe-heading {
  @include text-truncate(1);
}

::ng-deep awe-body-text {
  @include text-truncate(2);
}

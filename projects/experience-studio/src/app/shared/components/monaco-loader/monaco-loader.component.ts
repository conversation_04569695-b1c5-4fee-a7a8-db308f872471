import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, inject, signal, DestroyRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MonacoEditorService } from '../../services/monaco-editor/monaco-editor.service';
import { MonacoPreloaderService } from '../../services/monaco-preloader.service';
import { createLogger } from '../../utils/logger';

@Component({
  selector: 'app-monaco-loader',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="monaco-loader-container">
      @if (loadingState() === 'loading') {
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h3>Loading Code Editor...</h3>
          <p>{{ loadingMessage() }}</p>
          <div class="loading-progress">
            <div class="progress-bar" [style.width.%]="loadingProgress()"></div>
          </div>
        </div>
      } @else if (loadingState() === 'error') {
        <div class="error-content">
          <div class="error-icon">⚠️</div>
          <h3>Failed to Load Code Editor</h3>
          <p>{{ errorMessage() }}</p>
          <div class="error-actions">
            <button class="retry-button" (click)="retryLoading()">
              Retry Loading
            </button>
            <button class="fallback-button" (click)="useFallback()">
              Use Simple Editor
            </button>
          </div>
          <details class="error-details">
            <summary>Technical Details</summary>
            <pre>{{ errorDetails() }}</pre>
          </details>
        </div>
      } @else if (loadingState() === 'loaded') {
        <div class="success-content">
          <div class="success-icon">✅</div>
          <h3>Code Editor Ready</h3>
          <p>Monaco Editor loaded successfully</p>
        </div>
      }
    </div>
  `,
  styles: [`
    .monaco-loader-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      padding: 2rem;
      text-align: center;
    }

    .loading-content, .error-content, .success-content {
      max-width: 400px;
      width: 100%;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #007acc;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-progress {
      width: 100%;
      height: 4px;
      background-color: #f0f0f0;
      border-radius: 2px;
      overflow: hidden;
      margin-top: 1rem;
    }

    .progress-bar {
      height: 100%;
      background-color: #007acc;
      transition: width 0.3s ease;
    }

    .error-icon, .success-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .error-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin: 1rem 0;
    }

    .retry-button, .fallback-button {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: background-color 0.2s;
    }

    .retry-button {
      background-color: #007acc;
      color: white;
    }

    .retry-button:hover {
      background-color: #005a9e;
    }

    .fallback-button {
      background-color: #6c757d;
      color: white;
    }

    .fallback-button:hover {
      background-color: #545b62;
    }

    .error-details {
      margin-top: 1rem;
      text-align: left;
    }

    .error-details summary {
      cursor: pointer;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .error-details pre {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 4px;
      font-size: 0.8rem;
      overflow-x: auto;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
    }

    p {
      margin: 0 0 1rem 0;
      color: #666;
    }
  `]
})
export class MonacoLoaderComponent implements OnInit {
  private readonly monacoService = inject(MonacoEditorService);
  private readonly preloaderService = inject(MonacoPreloaderService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('MonacoLoaderComponent');

  // Signals for reactive state management
  readonly loadingState = signal<'idle' | 'loading' | 'loaded' | 'error'>('idle');
  readonly loadingMessage = signal<string>('Initializing code editor...');
  readonly loadingProgress = signal<number>(0);
  readonly errorMessage = signal<string>('');
  readonly errorDetails = signal<string>('');

  ngOnInit(): void {
    this.startLoading();
  }

  private startLoading(): void {
    this.loadingState.set('loading');
    this.loadingMessage.set('Preloading Monaco Editor resources...');
    this.loadingProgress.set(10);

    // Start with preloader
    this.preloaderService.preloadMonaco()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (success) => {
          if (success) {
            this.loadingProgress.set(50);
            this.loadingMessage.set('Initializing editor components...');
            this.loadMonacoEditor();
          } else {
            this.handlePreloadError('Preload failed, trying direct loading...');
            this.loadMonacoEditor();
          }
        },
        error: (error) => {
          this.handlePreloadError(error.message);
          this.loadMonacoEditor(); // Try direct loading as fallback
        }
      });
  }

  private loadMonacoEditor(): void {
    this.loadingProgress.set(70);
    this.loadingMessage.set('Loading Monaco Editor...');

    this.monacoService.loadMonaco()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.loadingProgress.set(100);
          this.loadingMessage.set('Code editor ready!');
          setTimeout(() => {
            this.loadingState.set('loaded');
          }, 500);
        },
        error: (error) => {
          this.handleLoadingError(error);
        }
      });
  }

  private handlePreloadError(message: string): void {
    this.logger.warn('Monaco preload warning:', message);
    // Don't show error for preload failures, just continue
  }

  private handleLoadingError(error: any): void {
    this.logger.error('Monaco loading error:', error);
    this.loadingState.set('error');
    this.errorMessage.set(this.getErrorMessage(error));
    this.errorDetails.set(this.getErrorDetails(error));
  }

  private getErrorMessage(error: any): string {
    if (error.message?.includes('Loading chunk')) {
      return 'Failed to load editor components. This might be due to network issues or cached files.';
    } else if (error.message?.includes('timeout')) {
      return 'Loading timeout. Please check your internet connection and try again.';
    } else if (error.message?.includes('worker')) {
      return 'Failed to initialize editor workers. Some features may be limited.';
    } else {
      return 'An unexpected error occurred while loading the code editor.';
    }
  }

  private getErrorDetails(error: any): string {
    return JSON.stringify({
      message: error.message,
      stack: error.stack?.split('\n').slice(0, 5).join('\n'),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }, null, 2);
  }

  retryLoading(): void {
    this.logger.info('Retrying Monaco Editor loading...');
    this.loadingState.set('idle');
    this.errorMessage.set('');
    this.errorDetails.set('');
    this.loadingProgress.set(0);
    
    // Reset services
    this.preloaderService.reset();
    
    // Retry after a short delay
    setTimeout(() => {
      this.startLoading();
    }, 1000);
  }

  useFallback(): void {
    this.logger.info('Using fallback editor mode...');
    // Emit event or navigate to fallback editor
    // This would be implemented based on your specific needs
    this.loadingState.set('loaded');
  }
}

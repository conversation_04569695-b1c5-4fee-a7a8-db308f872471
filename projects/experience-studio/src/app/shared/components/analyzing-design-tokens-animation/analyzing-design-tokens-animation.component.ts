import { Component, Input, OnInit, OnChanges, ChangeDetectionStrategy, signal, SimpleChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { createLogger } from '../../utils/logger';

export interface ColorTokenData {
  id: string;
  name: string;
  value: string;
  category: string;
}

@Component({
  selector: 'app-analyzing-design-tokens-animation',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="analyzing-design-tokens-container" [ngClass]="theme + '-theme'">
      <!-- Main view is controlled by the isAnalyzing signal -->
      <ng-container *ngIf="isAnalyzing(); else tokensView">
        <div class="analysis-container">
          <div class="analysis-header">
            <h3>Analyzing Design Tokens for You...</h3>
            <div class="loading-spinner"></div>
          </div>

          <!-- Color tokens carousel -->
          <div class="tokens-carousel">
            <div class="tokens-carousel-track">
              <div
                *ngFor="let token of duplicatedTokens(); trackBy: trackByTokenId"
                class="token-item">
                <div class="color-swatch-preview">
                  <div class="color-box-preview" [style.background-color]="token.value"></div>
                  <div class="token-details-preview">
                    <div class="token-name-preview">{{ token.name }}</div>
                    <div class="token-value-preview">{{ token.value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Tokens View: This is shown when isAnalyzing() is false -->
      <ng-template #tokensView>
        <div class="tokens-container">
          <h3>Design Tokens Analyzed</h3>
          <div class="tokens-summary">
            <span>{{ sampleTokens.length }} color tokens identified</span>
          </div>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    /* CSS Variables for theming */
    :root {
      --analyzing-tokens-bg-color: #f4f7fa;
      --analyzing-tokens-text-color: #333;
      --analyzing-tokens-text-secondary: #555;
      --analyzing-tokens-border-color: #d1d9e6;
      --analyzing-tokens-preview-bg: #ffffff;
      --analyzing-tokens-spinner-color: #6d6d6d;
      --analyzing-tokens-mask-gradient: linear-gradient(to right, rgba(244, 247, 250, 0), rgba(244, 247, 250, 1) 15%, rgba(244, 247, 250, 1) 85%, rgba(244, 247, 250, 0));
    }

    .dark-theme {
      --analyzing-tokens-bg-color: #1a1a1a;
      --analyzing-tokens-text-color: #e0e0e0;
      --analyzing-tokens-text-secondary: #a0a0a0;
      --analyzing-tokens-border-color: #333333;
      --analyzing-tokens-preview-bg: #2a2a2a;
      --analyzing-tokens-spinner-color: #888888;
      --analyzing-tokens-mask-gradient: linear-gradient(to right, rgba(26, 26, 26, 0), rgba(26, 26, 26, 1) 15%, rgba(26, 26, 26, 1) 85%, rgba(26, 26, 26, 0));
    }

    .analyzing-design-tokens-container {
      background-color: var(--analyzing-tokens-bg-color);
      color: var(--analyzing-tokens-text-color);
      width: 100%;
      height: 80vh;
      min-height: 400px;
      position: relative;
      overflow: hidden;
      // background: var(--analyzing-bg-color);
      border-radius: 12px;
      padding: 24px;
      box-sizing: border-box;
      display: flex;
      align-items:center;
      justify-content:center;
      flex-direction: column;

    }

    .analysis-container {
      width: 100%;
      max-width: 800px;
      text-align: center;
    }

    .analysis-header {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      margin-bottom: 2.5rem;
    }

    h3 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: var(--analyzing-tokens-text-color);
    }

    .loading-spinner {
      width: 24px;
      height: 24px;
      border: 3px solid var(--analyzing-tokens-border-color);
      border-top-color: var(--analyzing-tokens-spinner-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .tokens-carousel {
      width: 100%;
      overflow: hidden;
      position: relative;
      mask-image: var(--analyzing-tokens-mask-gradient);
      -webkit-mask-image: var(--analyzing-tokens-mask-gradient);
      height: 120px;
    }

    .tokens-carousel-track {
      display: flex;
      width: calc(140px * 32); /* (120px item + 20px gap) * 32 items (8 * 4) */
      animation: scroll 30s linear infinite;
      gap: 20px;
      height: 100%;
    }

    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(-25%); } /* 1/4 of total width since we have 4 copies */
    }

    .token-item {
      width: 120px;
      flex-shrink: 0;
      background: var(--analyzing-tokens-preview-bg);
      border: 1px solid var(--analyzing-tokens-border-color);
      border-radius: 8px;
      padding: 0.75rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      height: fit-content;
    }

    .color-swatch-preview {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      align-items: center;
    }

    .color-box-preview {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      border: 1px solid var(--analyzing-tokens-border-color);
      flex-shrink: 0;
    }

    .token-details-preview {
      text-align: center;
      min-width: 0;
    }

    .token-name-preview {
      font-size: 0.75rem;
      font-weight: 500;
      color: var(--analyzing-tokens-text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 0.25rem;
    }

    .token-value-preview {
      font-size: 0.65rem;
      color: var(--analyzing-tokens-text-secondary);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    .tokens-container {
      text-align: center;
      padding: 2rem;
    }

    .tokens-summary {
      margin-top: 1rem;
      color: var(--analyzing-tokens-text-secondary);
      font-size: 0.9rem;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .analyzing-design-tokens-container {
        padding: 1rem;
        min-height: 250px;
      }

      .analysis-header h3 {
        font-size: 1.25rem;
      }

      .tokens-carousel {
        height: 100px;
      }

      .token-item {
        width: 100px;
      }

      .color-box-preview {
        width: 32px;
        height: 32px;
      }
    }

    @media (max-width: 480px) {
      .analysis-header {
        flex-direction: column;
        gap: 0.75rem;
      }

      .token-item {
        width: 90px;
      }
    }
  `]
})
export class AnalyzingDesignTokensAnimationComponent implements OnInit, OnChanges {
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() showAnalyzing: boolean = true;

  private readonly logger = createLogger('AnalyzingDesignTokensAnimationComponent');

  readonly isAnalyzing = signal<boolean>(true);
  readonly animationDuration = signal<number>(30);
  readonly duplicatedTokens = signal<ColorTokenData[]>([]);

  readonly sampleTokens: ColorTokenData[] = [
    { id: 'primary', name: 'Primary', value: '#436ee5', category: 'Colors' },
    { id: 'secondary', name: 'Secondary', value: '#f1f5f9', category: 'Colors' },
    { id: 'background', name: 'Background', value: '#f8fafc', category: 'Colors' },
    { id: 'foreground', name: 'Foreground', value: '#243752', category: 'Colors' },
    { id: 'border', name: 'Border', value: '#e0e5eb', category: 'Colors' },
    { id: 'accent', name: 'Accent', value: '#f1f5f9', category: 'Colors' },
    { id: 'destructive', name: 'Destructive', value: '#ef4444', category: 'Colors' },
    { id: 'muted', name: 'Muted', value: '#677589', category: 'Colors' }
  ];

  ngOnInit(): void {
    this.logger.info('Component initialized', {
      theme: this.theme,
      showAnalyzing: this.showAnalyzing
    });

    // Create duplicated tokens for smooth infinite scroll
    const duplicated = [...this.sampleTokens, ...this.sampleTokens, ...this.sampleTokens, ...this.sampleTokens];
    this.duplicatedTokens.set(duplicated);
    this.isAnalyzing.set(this.showAnalyzing);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['showAnalyzing']) {
      this.isAnalyzing.set(this.showAnalyzing);
    }
  }

  trackByTokenId(index: number, token: ColorTokenData): string {
    return `${token.id}-${index}`;
  }

  startAnalyzing(): void {
    this.isAnalyzing.set(true);
    this.logger.info('Started analyzing design tokens animation');
  }

  stopAnalyzing(): void {
    this.isAnalyzing.set(false);
    this.logger.info('Stopped analyzing design tokens animation');
  }
}

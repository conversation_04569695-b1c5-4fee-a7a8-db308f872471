import { Component, ElementRef, Input, OnInit, OnDestroy, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef, DestroyRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ThemeService } from '../../services/theme-service/theme.service';
import { MonacoEditorService } from '../../services/monaco-editor/monaco-editor.service';
import { FileTreePersistenceService } from '../../services/file-tree-persistence.service';
import { MonacoStateManagementService } from '../../services/monaco-state-management.service';
import { FileContentCacheService } from '../../services/cache/file-content-cache.service';
import { CacheMetricsService } from '../../services/cache/cache-metrics.service';
import { MonacoLoaderComponent } from '../monaco-loader/monaco-loader.component';
import { createLogger } from '../../utils/logger';
import { FileOpeningService, FileOpenRequest } from '../../services/file-opening.service';

export interface FileModel {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileModel[];
  expanded?: boolean;
  fileName?: string;
}

// Interface for file input format
export interface FileInput {
  fileName: string;
  content: string;
}

@Component({
  selector: 'app-code-viewer',
  standalone: true,
  imports: [CommonModule, FormsModule, MonacoLoaderComponent],
  templateUrl: './code-viewer.component.html',
  styleUrl: './code-viewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeViewerComponent implements OnInit, OnDestroy {
  @ViewChild('editorContainer', { static: true }) editorContainer!: ElementRef;
  @Input() showFileExplorer = true;

  // private destroyRef = inject(DestroyRef);
  currentTheme: 'light' | 'dark';

  // Updated to handle multiple active files
  activeFiles: FileModel[] = [];
  activeFile: FileModel | null = null;
  selectedFilePath: string | null = null; // Track the selected file path for hover effect
  isLoading: boolean = true;
  isMonacoLoading: boolean = false;
  monacoLoadingState: 'idle' | 'loading' | 'loaded' | 'error' = 'idle';
  monacoError: string | null = null;
  searchQuery: string = '';
  showWelcomeMessage: boolean = true;
  private maxTabsVisible = 10;

  // Updated setter to handle the new file format
  // ENHANCED: Preserves existing Monaco editor content during tab switches
  @Input() set files(value: FileModel[] | FileInput[]) {
    this.logger.info('🔄 Files setter called with new value');

    this.isLoading = true;
    this.showWelcomeMessage = !value || (Array.isArray(value) && value.length === 0);

    if (!value || !Array.isArray(value) || value.length === 0) {
      this.logger.info('📂 No files provided, clearing file tree');
      this._originalFiles = [];
      this._files = [];
      this.isLoading = false;
      return;
    }

    // Check if the input follows FileModel[] format, FileInput[] format, or FileData format
    const firstItem = value[0];
    let fileModels: FileModel[] = [];

    if ('type' in firstItem && firstItem.type === 'file') {
      // Already FileModel format
      fileModels = value as FileModel[];
    } else if ('fileName' in firstItem && 'content' in firstItem) {
      // FileInput format
      fileModels = this.transformFilesToFileModels(value as FileInput[]);
    } else if ('path' in firstItem && 'code' in firstItem) {
      // FileData format from polling service
      fileModels = this.transformFileDataToFileModels(value as any[]);
    } else {
      // Try to handle as FileInput format as fallback
      fileModels = this.transformFilesToFileModels(value as FileInput[]);
    }

    // ENHANCED: Preserve existing Monaco editor content
    const preservedFileModels = this.preserveExistingContent(fileModels);

    this._originalFiles = this.buildFileTree(preservedFileModels);
    this._files = [...this._originalFiles];

    // Warm cache with new files for better performance
    this.warmCacheWithFiles(preservedFileModels);

    // Try to select the first file, but only if the editor is ready
    this.selectFirstFileIfAvailable();

    this.isLoading = false;
    this.logger.info(`✅ Files setter completed: ${this._files.length} files in tree`);
  }

  get files(): FileModel[] {
    return this._files;
  }

  @Input() theme: 'light' | 'dark' = 'dark'; // The theme input property
  private editor: any = null;
  private editorModelMap: Map<string, any> = new Map();
  private _files: FileModel[] = [];
  private _originalFiles: FileModel[] = [];
  private logger = createLogger('CodeViewerComponent');

  // Inject cache services using Angular 19+ inject() pattern
  private readonly fileContentCache = inject(FileContentCacheService);
  private readonly cacheMetrics = inject(CacheMetricsService);
  private readonly fileOpeningService = inject(FileOpeningService);
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private themeService: ThemeService,
    private monacoEditorService: MonacoEditorService,
    private fileTreePersistenceService: FileTreePersistenceService,
    private monacoStateManagementService: MonacoStateManagementService,
    private cdr: ChangeDetectorRef
  ) {
    this.currentTheme = this.themeService.getCurrentTheme();
    this.initializeCacheWarmup();
    this.setupFileOpeningSubscription();
  }

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(theme => {
        this.currentTheme = theme;
        this.applyTheme();
        this.cdr.markForCheck();
      });

      this.monacoEditorService.loadingState$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(state => {
        this.monacoLoadingState = state;
        this.cdr.markForCheck();
      });

    this.monacoEditorService.isLoading$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isLoading => {
        this.isMonacoLoading = isLoading;
        this.cdr.markForCheck();
      });

    this.monacoEditorService.error$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(error => {
        this.monacoError = error;
        this.cdr.markForCheck();
      });

    this.initializeEditor();
  }

  /**
   * Initialize cache warming for better performance
   */
  private initializeCacheWarmup(): void {
    // Warm file content cache when files are loaded
    this.fileContentCache.getFilesCacheStats().pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(stats => {
      this.logger.debug(`File cache stats: ${stats.totalFiles} files cached`);
    });

    // Monitor cache metrics for performance insights
    this.cacheMetrics.getMetrics().pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(metrics => {
      if (metrics.overallHitRatio > 0) {
        this.logger.debug(`Cache hit ratio: ${(metrics.overallHitRatio * 100).toFixed(1)}%`);
      }
    });
  }

  /**
   * Setup subscription to file opening requests from accordion
   */
  private setupFileOpeningSubscription(): void {
    this.fileOpeningService.fileOpenRequests.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(request => {
      this.logger.info(`📂 Received file open request: ${request.fileName} (${request.filePath})`);
      this.openFileByPath(request.filePath);
    });
  }

  /**
   * Warm cache with current files for better performance
   */
  private warmCacheWithFiles(files: FileModel[]): void {
    const cacheFiles = files
      .filter(file => file.type === 'file' && file.content)
      .map(file => ({
        filePath: file.name || file.fileName || '',
        content: typeof file.content === 'string' ? file.content : JSON.stringify(file.content, null, 2),
        lastModified: Date.now(),
        metadata: {
          language: this.getFileLanguage(file.name || file.fileName || ''),
          type: file.type
        }
      }));

    if (cacheFiles.length > 0) {
      this.fileContentCache.batchCacheFiles(cacheFiles);

      // Also preload related files for better performance
      if (cacheFiles.length > 0) {
        this.fileContentCache.preloadRelatedFiles(cacheFiles[0].filePath, cacheFiles);
      }

      this.logger.info(`Warmed cache with ${cacheFiles.length} files`);
    }
  }

  /**
   * Selects the first file in the file list if both the editor is ready and files are available
   */
  private selectFirstFileIfAvailable(): void {
    // Only proceed if both the editor is initialized and we have files
    if (this.editor && this._files.length > 0) {
      // Find the first file (not folder) in the file list
      const firstFile = this.findFirstFile(this._files);
      if (firstFile) {
        this.selectFile(firstFile);
        this.showWelcomeMessage = false;
      }
    }
  }

  /**
   * Recursively finds the first file (not folder) in the file tree
   */
  private findFirstFile(files: FileModel[]): FileModel | null {
    if (!files || files.length === 0) return null;

    // Look for the first file in the current level
    const firstFile = files.find(f => f.type === 'file');
    if (firstFile) return firstFile;

    // If no file at this level, recursively check folders
    for (const folder of files.filter(
      f => f.type === 'folder' && f.children && f.children.length > 0
    )) {
      const fileInFolder = this.findFirstFile(folder.children || []);
      if (fileInFolder) return fileInFolder;
    }

    return null;
  }

  ngOnDestroy(): void {
    // Save all current content before destroying
    this.saveAllCurrentContent();

    // Note: Subscriptions are automatically cleaned up by takeUntilDestroyed

    // Dispose editor and models using the service
    this.monacoEditorService.disposeEditor('code-viewer');

    // Clean up any models we created
    this.editorModelMap.forEach((_, key) => {
      this.monacoEditorService.disposeModel(key);
    });
    this.editorModelMap.clear();
  }

  ngOnChanges(): void {
    this.applyTheme(); // Reapply theme when it changes
  }

  public initializeEditor(): void {
    if (!this.editorContainer) {
      return;
    }

    // Set loading state
    this.isLoading = true;

    // Create editor with the MonacoEditorService
    this.monacoEditorService
      .createEditor(
        this.editorContainer.nativeElement,
        {
          theme: this.currentTheme === 'dark' ? 'dark-theme' : 'light-theme',
          language: 'plaintext',
          automaticLayout: true,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          readOnly: true,
          fontSize: 14,
          tabSize: 2,
          lineHeight: 22,
          stickyScroll: { enabled: false },
        },
        'code-viewer' // Unique ID for this editor instance
      )
     .subscribe({
        next: editor => {
        this.editor = editor;
        this.isLoading = false;
        this.applyTheme();

        // ENHANCED: Setup Monaco Editor event handlers for state management
        this.setupMonacoEventHandlers(editor);

        // Select the first file when the editor is ready
        this.selectFirstFileIfAvailable();
         this.cdr.markForCheck();
        },
        error: error => {
          this.isLoading = false;
          this.monacoError = `Failed to initialize Monaco Editor: ${error.message}`;
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Setup Monaco Editor event handlers for state management
   * ENHANCED: Comprehensive state preservation and user edit tracking
   */
  private setupMonacoEventHandlers(editor: any): void {
    if (!editor) return;

    // Track content changes
    editor.onDidChangeModelContent((event: any) => {
      if (!this.activeFile) return;

      const currentContent = editor.getValue();
      const filePath = this.activeFile.name || this.activeFile.fileName || '';

      // Get original content for comparison
      const originalContent = this.fileTreePersistenceService.getCurrentFileContent(filePath);

      // Record content change in state management service
      this.monacoStateManagementService.recordContentChange(
        filePath,
        currentContent,
        originalContent || this.activeFile.content
      );

      // Update file content in local state
      this.activeFile.content = currentContent;

      this.logger.info(`📝 Content changed for: ${filePath} (${currentContent.length} chars)`);
    });

    // Track cursor position changes
    editor.onDidChangeCursorPosition((event: any) => {
      if (!this.activeFile) return;

      const filePath = this.activeFile.name || this.activeFile.fileName || '';
      this.monacoStateManagementService.recordEditorState(filePath, {
        cursorPosition: {
          lineNumber: event.position.lineNumber,
          column: event.position.column
        }
      });
    });

    // Track scroll position changes
    editor.onDidScrollChange((event: any) => {
      if (!this.activeFile) return;

      const filePath = this.activeFile.name || this.activeFile.fileName || '';
      this.monacoStateManagementService.recordEditorState(filePath, {
        scrollPosition: {
          scrollTop: event.scrollTop,
          scrollLeft: event.scrollLeft
        }
      });
    });

    // Track selection changes
    editor.onDidChangeCursorSelection((event: any) => {
      if (!this.activeFile) return;

      const filePath = this.activeFile.name || this.activeFile.fileName || '';
      this.monacoStateManagementService.recordEditorState(filePath, {
        selectionRange: {
          startLineNumber: event.selection.startLineNumber,
          startColumn: event.selection.startColumn,
          endLineNumber: event.selection.endLineNumber,
          endColumn: event.selection.endColumn
        }
      });
    });

    this.logger.info('✅ Monaco Editor event handlers setup for state management');
  }

  private applyTheme(): void {
    if (this.editor) {
      // Set the theme using the service
      this.monacoEditorService.setTheme(
        this.currentTheme === 'light' ? 'light-theme' : 'dark-theme'
      );
    }
  }

  private getFileLanguage(fileName: string): string {
    // Use the service to get the language for the file
    return this.monacoEditorService.getLanguageForFile(fileName);
  }

  // private getFileLanguage(fileName: string): string {
  //   const ext = fileName.split('.').pop()?.toLowerCase(); // Get the file extension

  //   // Ensure Monaco is initialized and the language configurations are loaded
  //   const languages = monaco.languages.getLanguages();

  //   // Check if any language has the current file extension
  //   const language = languages.find(lang => lang.extensions?.includes(`.${ext}`));

  //   // Return language id if found, or default to 'plaintext'
  //   return language ? language.id : 'plaintext';
  // }

  getFileIcon(fileName: string): string {
    if (!fileName) return 'awe_document';

    // Special case for tailwind.config.ts
    if (fileName === 'tailwind.config.ts' || fileName.endsWith('/tailwind.config.ts')) {
      return 'awe_react_ts';
    }

    // Handle special config files with multi-level extensions
    // if (fileName.endsWith('.config.ts') || fileName.endsWith('.config.js')) {
    //   return 'awe_config';
    // }

    if (fileName.endsWith('.d.ts')) {
      return 'awe_react_ic';
    }

    // React component files and patterns
    if (
      fileName.endsWith('.component.tsx') ||
      fileName.endsWith('.component.jsx') ||
      fileName.match(/\.[A-Z][a-zA-Z]*\.tsx$/) || // Matches patterns like Button.tsx, App.tsx
      fileName.match(/\.[A-Z][a-zA-Z]*\.jsx$/) || // Matches patterns like Button.jsx, App.jsx
      fileName.includes('React') ||
      fileName.includes('react')
    ) {
      return 'awe_react_ic';
    }

    // Angular component files
    if (fileName.endsWith('.component.ts')) {
      return 'awe_angular';
    }

    if (fileName.endsWith('.component.html')) {
      return 'awe_html';
    }

    if ( fileName.endsWith('.component.css')) {
      return 'awe_css';
    }
    if ( fileName.endsWith('.component.scss')) {
      return 'awe_scss';
    }

    if (fileName.endsWith('.module.ts')) {
      return 'awe_angular';
    }

    if (fileName.endsWith('.service.ts')) {
      return 'awe_react_ic';
    }
    if (fileName.endsWith('.config.ts') && !fileName.endsWith('tailwind.config.ts')) {
      return 'awe_react_ic';
    }

    // For regular files, get the extension
    const ext = fileName.split('.').pop()?.toLowerCase();

    // Always use awe_react_ic icon for .jsx files
    if (ext === 'jsx') {
      return 'awe_react_ic';
    }

    // Map common extensions to their icons
    switch (ext) {
      case 'ts':
        return 'awe_react_ic';
      case 'tsx':
        return 'awe_react_ic';
      case 'js':
        return 'awe_js';
      case 'jsx':
        return 'awe_react_ic';
      case 'html':
        return 'awe_html';
      case 'css':
        return 'awe_css';
      case 'scss':
      case 'sass':
        return 'awe_scss';
      case 'json':
        return 'awe_json';
      case 'md':
        return 'awe_md';
      case 'svg':
        return 'awe_svg';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
        return 'awe_image';
      case 'pdf':
        return 'awe_pdf';
      case 'gitignore':
      case 'gitattributes':
        return 'awe_git';
      case 'yml':
      case 'yaml':
        return 'awe_yaml';
      case 'xml':
        return 'awe_xml';
      default:
        // Try to use the extension as the icon name if available
        return ext ? `awe_${ext}` : 'awe_document';
    }
  }

  //  /**
  //  * Get the path to the file icon image
  //  * @param fileName The name of the file
  //  * @returns The path to the icon image
  //  */
  // getFileIconPath(fileName: string): string {
  //   const iconName = this.getFileIcon(fileName);
  //   return `/assets/icons/${iconName}.svg`;

  /**
   * Open a file by its path (called from accordion clicks)
   */
  openFileByPath(filePath: string): void {
    if (!filePath) {
      this.logger.warn('⚠️ No file path provided for opening');
      return;
    }

    // Normalize the file path
    const normalizedPath = this.fileOpeningService.normalizeFilePath(filePath);

    // Find the file in the current file tree
    const file = this.findFileByPath(this._files, normalizedPath);

    if (file) {
      this.logger.info(`✅ Found file in tree: ${file.name}`);
      this.selectFile(file);
    } else {
      this.logger.warn(`⚠️ File not found in current tree: ${normalizedPath}`);
      // Try to find by file name only as fallback
      const fileName = this.fileOpeningService.extractFileName(normalizedPath);
      const fileByName = this.findFileByName(this._files, fileName);

      if (fileByName) {
        this.logger.info(`✅ Found file by name: ${fileByName.name}`);
        this.selectFile(fileByName);
      } else {
        this.logger.error(`❌ File not found: ${filePath}`);
      }
    }
  }

  selectFile(file: FileModel): void {
    if (!this.editor) {
      return;
    }

    // Set the selected file path for hover effect
    this.selectedFilePath = file.name;

    // Check if file is already open
    const existingFileIndex = this.activeFiles.findIndex(f => f.name === file.name);
    if (existingFileIndex !== -1) {
      // If file is already open, just make it active
      this.setActiveFile(this.activeFiles[existingFileIndex]);
      return;
    }

    // Save current file content before switching
    if (this.activeFile && this.activeFile.type === 'file' && this.editor) {
      const currentContent = this.editor.getValue();
      this.activeFile.content = currentContent;

      // Also update the corresponding file in the tree structure
      this.updateFileContentInTree(this.activeFile.name || this.activeFile.fileName || '', currentContent);

      this.logger.info(`💾 Saved content for: ${this.activeFile.name} (${currentContent.length} chars)`);
    }

    // Add new file to active files
    this.activeFiles.push(file);
    this.setActiveFile(file);
    this.showWelcomeMessage = false;

    // Get or create model for the file
    let model = this.editorModelMap.get(file.name);
    if (!model && file.type === 'file') {
      const language = this.getFileLanguage(file.name);
      const content =
        typeof file.content === 'string' ? file.content : JSON.stringify(file.content, null, 2);
      this.monacoEditorService
        .createOrGetModel(content, language, `file:///${file.name}`)
        .subscribe(newModel => {
          model = newModel;
          this.editorModelMap.set(file.name, model);
          this.editor.setModel(model);
          this.cdr.markForCheck();
        });
    } else if (model) {
      this.editor.setModel(model);
    }

    this.cdr.markForCheck();
  }

  setActiveFile(file: FileModel): void {
    this.activeFile = file;
    const model = this.editorModelMap.get(file.name);
    if (model) {
      this.editor.setModel(model);

      // ENHANCED: Restore editor state (cursor position, scroll, etc.)
      const filePath = file.name || file.fileName || '';
      this.monacoStateManagementService.restoreEditorState(filePath, this.editor);

      this.logger.info(`🔄 Set active file: ${filePath} with state restoration`);
    }
    this.cdr.markForCheck();
  }

  closeTab(file: FileModel): void {
    const index = this.activeFiles.findIndex(f => f.name === file.name);
    if (index === -1) return;

    // Save file content before closing
    if (file.type === 'file' && this.activeFile === file && this.editor) {
      const currentContent = this.editor.getValue() || '';
      file.content = currentContent;

      // Also update the corresponding file in the tree structure
      this.updateFileContentInTree(file.name || file.fileName || '', currentContent);

      this.logger.info(`💾 Saved content before closing tab: ${file.name} (${currentContent.length} chars)`);
    }

    // Remove the file from active files
    this.activeFiles.splice(index, 1);

    // Remove the model
    const modelUri = `file:///${file.name}`;
    this.monacoEditorService.disposeModel(modelUri);
    this.editorModelMap.delete(file.name);

    // If we're closing the active file, activate another file
    if (this.activeFile === file) {
      if (this.activeFiles.length > 0) {
        // Activate the previous file, or the next one if we closed the first file
        const newIndex = Math.min(index, this.activeFiles.length - 1);
        this.setActiveFile(this.activeFiles[newIndex]);
      } else {
        this.activeFile = null;
        this.editor?.setModel(null);
        this.showWelcomeMessage = true;
        this.cdr.markForCheck();
      }
    }

    this.cdr.markForCheck();
  }

  toggleFolder(folder: FileModel): void {
    folder.expanded = !folder.expanded;
    this.cdr.markForCheck();
  }

  /**
   * Update an existing file's content or add a new file
   * ENHANCED: Uses file tree persistence service for robust comparison and state management
   * This method is called when edit responses are received
   */
  updateOrAddFile(fileName: string, content: string): void {
    this.logger.info(`🔄 Updating or adding file: ${fileName}`);

    // Use file tree persistence service for robust comparison
    const comparisonResult = this.fileTreePersistenceService.compareAgainstBaseline([{ fileName, content }]);

    // Find existing file in the file tree using normalized path comparison
    const existingFile = this.findFileByName(this._files, fileName);

    if (existingFile) {
      // Update existing file content
      existingFile.content = content;
      existingFile.fileName = fileName; // Ensure fileName is updated

      // Update the Monaco model if it exists
      const modelKey = existingFile.name || existingFile.fileName;
      const model = this.editorModelMap.get(modelKey);
      if (model) {
        model.setValue(content);
      }

      // If this is the currently active file, refresh the editor
      if (this.activeFile) {
        const activeNormalized = this.normalizeFilePath(this.activeFile.name || this.activeFile.fileName || '');
        const existingNormalized = this.normalizeFilePath(existingFile.name || existingFile.fileName || '');

        if (activeNormalized === existingNormalized) {
          this.editor?.setModel(model);
        }
      }

      // Update active files if the file is open
      const activeFileIndex = this.activeFiles.findIndex(f => {
        const activeNormalized = this.normalizeFilePath(f.name || f.fileName || '');
        const existingNormalized = this.normalizeFilePath(existingFile.name || existingFile.fileName || '');
        return activeNormalized === existingNormalized;
      });

      if (activeFileIndex !== -1) {
        this.activeFiles[activeFileIndex].content = content;
        this.activeFiles[activeFileIndex].fileName = fileName;
      }

      this.logger.info(`🔄 Updated existing file: ${fileName}`);
    } else {
      // Add new file with proper file tree structure
      const newFile: FileModel = {
        name: fileName,
        type: 'file',
        content: content,
        fileName: fileName
      };

      // Get all current files and add the new one
      const allCurrentFiles = this.getAllFilesFromTree(this._files);
      allCurrentFiles.push(newFile);

      // Rebuild the file tree to maintain proper structure
      this._files = this.buildFileTree(allCurrentFiles);
      this._originalFiles = [...this._files];

      this.logger.info(`➕ Added new file: ${fileName}`);
    }

    this.cdr.markForCheck();
  }

  /**
   * Find a file by name in the file tree with normalized path comparison
   */
  private findFileByName(files: FileModel[], fileName: string): FileModel | null {
    const normalizedSearchPath = this.normalizeFilePath(fileName);

    for (const file of files) {
      if (file.type === 'file') {
        const normalizedFilePath = this.normalizeFilePath(file.name || file.fileName || '');
        const normalizedFileNamePath = this.normalizeFilePath(file.fileName || file.name || '');

        if (normalizedFilePath === normalizedSearchPath || normalizedFileNamePath === normalizedSearchPath) {
          return file;
        }
      }
      if (file.type === 'folder' && file.children) {
        const found = this.findFileByName(file.children, fileName);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * Normalize file path for comparison (handle different path separators and formats)
   */
  private normalizeFilePath(filePath: string): string {
    if (!filePath) return '';

    // Convert backslashes to forward slashes and remove leading/trailing slashes
    return filePath
      .replace(/\\/g, '/')
      .replace(/^\/+/, '')
      .replace(/\/+$/, '')
      .toLowerCase();
  }

  /**
   * Batch update multiple files efficiently
   * ENHANCED: Uses file tree persistence service for robust comparison and state management
   */
  updateMultipleFiles(editedFiles: { fileName: string; content: string }[]): void {
    if (!editedFiles || editedFiles.length === 0) {
      this.logger.warn('⚠️ No files to update');
      return;
    }

    this.logger.info(`🔄 Enhanced batch updating ${editedFiles.length} files`);

    // Use file tree persistence service for robust comparison
    const comparisonResult = this.fileTreePersistenceService.compareAgainstBaseline(editedFiles);
    this.logger.info(`📊 Comparison result: ${comparisonResult.updated.length} updated, ${comparisonResult.added.length} added, ${comparisonResult.unchanged.length} unchanged`);

    // Get current files from persistence service
    const currentFiles = this.fileTreePersistenceService.getCurrentFiles();

    // Create merged file list
    const mergedFiles = this.createMergedFileList(currentFiles, comparisonResult);

    // Update persistence service with new current state
    this.fileTreePersistenceService.updateCurrentState(mergedFiles);

    // Update local file tree
    this._files = this.buildFileTree(mergedFiles);
    this._originalFiles = [...this._files];

    // Update Monaco editor models for changed files
    this.updateMonacoModelsForChangedFiles(editedFiles);

    // Update active files
    this.updateActiveFilesContent();

    // Refresh current editor view
    this.refreshCurrentEditor();

    // Promote to baseline for future comparisons
    this.fileTreePersistenceService.promoteToBaseline();

    this.cdr.markForCheck();
    this.logger.info(`✅ Enhanced batch update completed: ${editedFiles.length} files processed`);
  }

  /**
   * Create merged file list from current files and comparison result
   */
  private createMergedFileList(currentFiles: FileModel[], comparisonResult: any): FileModel[] {
    const mergedFiles: FileModel[] = [...currentFiles];

    // Apply updates to existing files
    for (const updatedFile of comparisonResult.updated) {
      const existingIndex = mergedFiles.findIndex(file => {
        const currentPath = this.normalizeFilePath(file.name || file.fileName || '');
        const updatedPath = this.normalizeFilePath(updatedFile.name || updatedFile.fileName || '');
        return currentPath === updatedPath;
      });

      if (existingIndex !== -1) {
        mergedFiles[existingIndex] = { ...mergedFiles[existingIndex], ...updatedFile };
        this.logger.info(`🔄 Applied update to: ${updatedFile.name || updatedFile.fileName}`);
      }
    }

    // Add new files
    for (const newFile of comparisonResult.added) {
      mergedFiles.push(newFile);
      this.logger.info(`➕ Added new file: ${newFile.name || newFile.fileName}`);
    }

    return mergedFiles;
  }

  /**
   * Update Monaco editor models for changed files
   */
  private updateMonacoModelsForChangedFiles(editedFiles: { fileName: string; content: string }[]): void {
    for (const editedFile of editedFiles) {
      const modelKey = editedFile.fileName;
      let model = this.editorModelMap.get(modelKey);

      // If not found by exact key, try to find by normalized path
      if (!model) {
        const normalizedEditedPath = this.normalizeFilePath(editedFile.fileName);
        for (const [key, existingModel] of this.editorModelMap.entries()) {
          const normalizedKey = this.normalizeFilePath(key);
          if (normalizedKey === normalizedEditedPath) {
            model = existingModel;
            break;
          }
        }
      }

      if (model) {
        model.setValue(editedFile.content);
        this.logger.info(`🔄 Updated Monaco model for: ${editedFile.fileName}`);
      } else {
        this.logger.warn(`⚠️ No Monaco model found for: ${editedFile.fileName}`);
      }
    }
  }

  /**
   * Update content of currently active files
   */
  private updateActiveFilesContent(): void {
    for (let i = 0; i < this.activeFiles.length; i++) {
      const activeFile = this.activeFiles[i];
      const treeFile = this.findFileByName(this._files, activeFile.name || activeFile.fileName || '');

      if (treeFile && treeFile.content !== activeFile.content) {
        this.activeFiles[i] = { ...treeFile };
      }
    }
  }

  /**
   * Refresh the current editor view
   */
  private refreshCurrentEditor(): void {
    if (this.activeFile && this.editor) {
      const modelKey = this.activeFile.name || this.activeFile.fileName || '';
      const model = this.editorModelMap.get(modelKey);
      if (model) {
        this.editor.setModel(model);
      }
    }
  }

  /**
   * Add a new file to the file tree
   */
  private addFileToTree(newFile: FileModel): void {
    // For now, add to root level
    // In a more sophisticated implementation, this could parse the path and add to appropriate folder
    this._files.push(newFile);
  }

  /**
   * Get all files from the tree structure (flattened)
   */
  private getAllFilesFromTree(files: FileModel[]): FileModel[] {
    const allFiles: FileModel[] = [];

    for (const file of files) {
      if (file.type === 'file') {
        allFiles.push(file);
      } else if (file.type === 'folder' && file.children) {
        allFiles.push(...this.getAllFilesFromTree(file.children));
      }
    }

    return allFiles;
  }

  /**
   * Save all current content from Monaco editor models to file tree
   * This is called before component destruction or major state changes
   */
  private saveAllCurrentContent(): void {
    if (!this.editorModelMap || this.editorModelMap.size === 0) {
      return;
    }

    this.logger.info('💾 Saving all current content from Monaco editor models');

    // Save content from all Monaco models to the file tree
    this.editorModelMap.forEach((model, fileName) => {
      const currentContent = model.getValue();
      this.updateFileContentInTree(fileName, currentContent);
    });

    // Also save the currently active file
    if (this.activeFile && this.editor) {
      const currentContent = this.editor.getValue();
      this.activeFile.content = currentContent;
      this.updateFileContentInTree(this.activeFile.name || this.activeFile.fileName || '', currentContent);
    }

    this.logger.info('✅ All current content saved');
  }

  /**
   * Update file content in the tree structure
   * This ensures the tree structure stays in sync with Monaco editor content
   */
  private updateFileContentInTree(fileName: string, content: string): void {
    const file = this.findFileByName(this._files, fileName);
    if (file) {
      file.content = content;
      this.logger.info(`🔄 Updated file content in tree: ${fileName}`);
    } else {
      this.logger.warn(`⚠️ File not found in tree for content update: ${fileName}`);
    }
  }

  /**
   * Preserve existing Monaco editor content when files are updated
   * This prevents losing updated content during tab switches
   */
  private preserveExistingContent(newFileModels: FileModel[]): FileModel[] {
    if (!this.editorModelMap || this.editorModelMap.size === 0) {
      this.logger.info('📂 No existing Monaco models to preserve');
      return newFileModels;
    }

    this.logger.info('🔄 Preserving existing Monaco editor content');

    const preservedFiles = newFileModels.map(newFile => {
      const filePath = newFile.name || newFile.fileName || '';
      const normalizedPath = this.normalizeFilePath(filePath);

      // Check if we have an existing Monaco model for this file
      let existingModel = this.editorModelMap.get(filePath);

      // If not found by exact path, try to find by normalized path
      if (!existingModel) {
        for (const [modelKey, model] of this.editorModelMap.entries()) {
          const normalizedModelKey = this.normalizeFilePath(modelKey);
          if (normalizedModelKey === normalizedPath) {
            existingModel = model;
            break;
          }
        }
      }

      if (existingModel) {
        // Get the current content from the Monaco model
        const currentContent = existingModel.getValue();

        // Only preserve if the content is different from what we're about to set
        if (currentContent !== newFile.content) {
          this.logger.info(`🔄 Preserving updated content for: ${filePath}`);
          this.logger.info(`   Original: ${(newFile.content || '').length} chars`);
          this.logger.info(`   Current:  ${currentContent.length} chars`);

          return {
            ...newFile,
            content: currentContent
          };
        }
      }

      return newFile;
    });

    this.logger.info(`✅ Content preservation completed for ${preservedFiles.length} files`);
    return preservedFiles;
  }

  /**
   * Public method to save current content before tab switches
   * This is called by the code-window component when switching tabs
   */
  public saveCurrentContent(): void {
    this.saveAllCurrentContent();
  }

  /**
   * Public method to get all current files (flattened from tree structure)
   * This is used by the code-window component for file comparison
   */
  public getAllCurrentFiles(): FileModel[] {
    // Save current content before returning files
    this.saveAllCurrentContent();

    const flattenedFiles = this.getAllFilesFromTree(this._files);
    this.logger.info(`📂 Code-viewer returning ${flattenedFiles.length} flattened files`);

    flattenedFiles.forEach((file, index) => {
      const filePath = file.name || file.fileName || 'unknown';
      const contentLength = (file.content || '').length;
      this.logger.info(`  ${index + 1}. "${filePath}" (${contentLength} chars)`);
    });

    return flattenedFiles;
  }

  /**
   * Refresh all open files with updated content
   * This method can be called when multiple files are updated
   */
  refreshOpenFiles(): void {
    // Update all active files with their current content from the file tree
    for (const activeFile of this.activeFiles) {
      const treeFile = this.findFileByName(this._files, activeFile.name);
      if (treeFile && treeFile.content !== activeFile.content) {
        activeFile.content = treeFile.content;

        // Update Monaco model
        const model = this.editorModelMap.get(activeFile.name);
        if (model) {
          model.setValue(treeFile.content || '');
        }
      }
    }

    // Refresh the current editor view
    if (this.activeFile) {
      const model = this.editorModelMap.get(this.activeFile.name);
      if (model) {
        this.editor?.setModel(model);
      }
    }

    this.cdr.markForCheck();
  }

  /**
   * Replace all files with new files (for regeneration)
   * ENHANCED: Complete file replacement for regeneration workflow
   * This method completely replaces all files instead of merging
   */
  replaceAllFiles(newFiles: FileModel[] | FileInput[]): void {
    this.logger.info('🔄 Replacing all files for regeneration:', {
      newFileCount: newFiles?.length || 0,
      currentFileCount: this._files?.length || 0
    });

    // Clear all existing state
    this.clearAllState();

    // Set new files using the existing setter logic
    this.files = newFiles;

    this.logger.info('✅ All files replaced for regeneration');
  }

  /**
   * Clear all existing state for complete replacement
   * ENHANCED: Comprehensive state cleanup for regeneration
   */
  private clearAllState(): void {
    this.logger.info('🧹 Clearing all code-viewer state for regeneration');

    // Clear file arrays
    this._originalFiles = [];
    this._files = [];

    // Clear active files and close all tabs
    this.activeFiles = [];
    this.activeFile = null;

    // Clear Monaco editor models
    this.editorModelMap.clear();

    // Clear editor content
    if (this.editor) {
      this.editor.setModel(null);
    }

    // Reset file tree persistence service for new baseline
    this.fileTreePersistenceService.reset();

    // Reset Monaco state management
    this.monacoStateManagementService.reset();

    // Reset UI state
    this.showWelcomeMessage = true;
    this.isLoading = false;

    this.logger.info('✅ All code-viewer state cleared for regeneration');
  }

  // Updated method to transform your input format to FileModel array
  private transformFilesToFileModels(files: FileInput[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.fileName, // Use fileName as the name
      type: 'file',
      content: file.content,
    }));
  }

  // Transform FileData format (from polling service) to FileModel array
  private transformFileDataToFileModels(files: any[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.path || file.fileName || 'Unknown file', // Use path as the name
      type: 'file',
      content: file.code || file.content || '',
      fileName: file.path || file.fileName
    }));
  }

  private buildFileTree(files: FileModel[]): FileModel[] {
    const root: FileModel = { name: 'root', type: 'folder', children: [], expanded: true };

    if (!files || !Array.isArray(files)) {
      return [];
    }

    files.forEach(file => {
      // Skip files without a name property
      if (!file || typeof file.name !== 'string') {
        return;
      }

      // Normalize path separators to forward slashes
      const normalizedName = file.name.replace(/\\/g, '/');
      const pathParts = normalizedName.split('/');
      let currentFolder = root;

      pathParts.forEach((part, index) => {
        if (index === pathParts.length - 1) {
          // Pass full filename and content to file object
          currentFolder.children?.push({
            name: part,
            type: 'file',
            content: file.content,
            fileName: file.fileName || file.name,
          });
        } else {
          let nextFolder = currentFolder.children?.find(child => child.name === part);
          if (!nextFolder) {
            nextFolder = { name: part, type: 'folder', children: [], expanded: true }; // Set expanded to true by default
            currentFolder.children?.push(nextFolder);
          }
          currentFolder = nextFolder;
        }
      });
    });

    return root.children || [];
  }

  public filterFiles(): void {
    if (!this.searchQuery) {
      this._files = [...this._originalFiles];
      this.cdr.markForCheck();
      return;
    }

    const searchLower = this.searchQuery.toLowerCase();
    this._files = this.filterFileTree(this._originalFiles, searchLower);
    this.cdr.markForCheck();
  }

  private filterFileTree(files: FileModel[], searchQuery: string): FileModel[] {
    return files.filter(file => {
      if (file.type === 'folder' && file.children) {
        const matchingChildren = this.filterFileTree(file.children, searchQuery);
        if (matchingChildren.length > 0) {
          return true;
        }
      }
      return file.name.toLowerCase().includes(searchQuery);
    });
  }

  /**
   * Get the appropriate icon color based on file type
   * @param fileName The name of the file
   * @returns The icon color to use
   */
  getFileIconColor(fileName: string): string {
    if (!fileName) return 'neutralIcon';

    // Get file extension
    const ext = fileName.split('.').pop()?.toLowerCase();

    // Assign colors based on file types
    switch (ext) {
      case 'jsx':
      case 'tsx':
        return 'lightblue';

      case 'ts':
      case 'js':
        return 'action'; // TypeScript/JavaScript files in action color

      case 'css':
      case 'scss':
      case 'sass':
        return 'success'; // Styling files in success/green

      case 'json':
      case 'yml':
      case 'yaml':
      case 'xml':
        return 'warning'; // Config files in warning/yellow

      case 'md':
        return 'neutralIcon'; // Documentation in neutral

      case 'gitignore':
      case 'gitattributes':
        return 'danger'; // Git files in danger/red

      default:
        // Default color based on theme
        return this.theme === 'dark' ? 'whiteIcon' : 'neutralIcon';
    }
  }

  /**
   * Get the path to the icon SVG file based on the file name
   * @param fileName The name of the file
   * @returns The path to the icon SVG file
   */
  getFileIconPath(fileName: string): string {
    const iconName = this.getFileIcon(fileName);

    // Special case for tailwind.config.ts
    if (iconName === 'tailwind_config_ts') {
      return '/assets/icons/awe_react_ts.svg';
    }

    return `/assets/icons/${iconName}.svg`;
  }

  /**
   * Find a file by its path in the file tree (recursive search)
   */
  private findFileByPath(files: FileModel[], filePath: string): FileModel | null {
    if (!files || !filePath) return null;

    for (const file of files) {
      // Check if this is the file we're looking for by exact path match
      if (file.type === 'file' && (file.name === filePath || file.fileName === filePath)) {
        return file;
      }

      // Also check if the file name matches the end of the path
      if (file.type === 'file' && filePath.endsWith(file.name)) {
        return file;
      }

      // If it's a folder, search recursively
      if (file.type === 'folder' && file.children) {
        const found = this.findFileByPath(file.children, filePath);
        if (found) return found;
      }
    }

    return null;
  }

}

import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';

import { ThemeService } from '../../services/theme-service/theme.service';
import { UserProfile } from '../../models/user-profile.model';
import { AppConstants } from '../../appConstants';
import { ObserverManager } from '../../utils/subscription-management.util';
import { createThemeAssetResolver } from '../../utils/theme-manager.util';

@Component({
  selector: 'app-nav-header',
  imports: [HeaderComponent, CommonModule],
  standalone: true,
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NavHeaderComponent implements OnInit, OnDestroy {
  @Output() logout = new EventEmitter<void>();
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';

  private observerManager = new ObserverManager();
  private themeAssetResolver = createThemeAssetResolver();

  constructor(private themeService: ThemeService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.updateThemeAssets();
    this.observerManager.createMutationObserver(
      document.body,
      () => this.updateThemeAssets(),
      {
        attributes: true,
        attributeFilter: ['class'],
      }
    );
  }

  ngOnDestroy(): void {
    this.observerManager.cleanup();
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  // DISABLED: Profile menu functionality removed as per requirements
  // toggleProfileMenu(): void {
  //   this.showProfileMenu = !this.showProfileMenu;
  //   this.cdr.markForCheck();
  // }

  onLogout(): void {
    this.logout.emit();
    this.showProfileMenu = false;
    this.cdr.markForCheck();
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `${AppConstants.AssetsPath}/user-avatar.svg`;
  }

  getDisplayName(): string {
    return this.userProfile?.displayName || 'User';
  }

  getEmail(): string {
    return this.userProfile?.mail || this.userProfile?.userPrincipalName || '';
  }

  private updateThemeAssets(): void {
    // Use the theme asset resolver for consistent asset management
    const assets = this.themeAssetResolver.getAssets({
      logo: `${AppConstants.AssetsPath}/ascendion-logo`,
      themeToggle: `${AppConstants.AssetsPath}/theme-toggle`,
      menu: `${AppConstants.AssetsPath}/menu`
    });

    this.logoSrc = assets['logo'];
    this.themeToggleIcon = assets['themeToggle'];
    this.themeMenuIcon = assets['menu'];
    this.cdr.markForCheck();
  }
}

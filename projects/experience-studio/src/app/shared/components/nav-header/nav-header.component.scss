.profile-icon {
  height: 2.5rem;

  &.profile-disabled {
    cursor: default;
    opacity: 0.7;
    filter: grayscale(20%);
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
      transform: none;
    }
  }
}

.profile-flyout {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  background: #fff;
  border-radius: 1rem;
  z-index: 4000;
  color: #222;
}

.gradient-button {
  ::ng-deep button {
    width: 100% !important;
    background: linear-gradient(90deg, #a259c6 0%, #f76b6a 100%);
    font-weight: 600;
    &:hover {
      background: linear-gradient(90deg, #f76b6a 0%, #a259c6 100%);
    }
  }
}

.profile-avatar {
  border-radius: 50%;
  object-fit: cover;
}

::ng-deep .profile-name .heading.s2 {
  font-size: 1.1rem !important;
}

::ng-deep .profile-email .heading.s2 {
  font-size: 1rem !important;
  color: #888;
}

::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}

// Mobile Frame Component Styles
.mobile-frame-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--code-viewer-bg);
  border: 1px solid var(--code-viewer-border);
  border-radius: 0 0 12px 12px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  // Theme variables (kept for compatibility)
  &.light-theme {
    --placeholder-bg: #f9fafb;
    --placeholder-text: #6b7280;
  }

  &.dark-theme {
    --placeholder-bg: #1f2937;
    --placeholder-text: #9ca3af;
  }
}

// Device Frame Container
.device-frame-container {
  height:70vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Action buttons container - positioned at top-right
.action-buttons {

  display: flex;
  gap: 8px;
  z-index: 20;
}

// Action button base styling
.action-button {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 1);
    color: #333;
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

// Download button specific styling
.download-button {
  &:hover:not(:disabled) {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
  }
}

// Fullscreen button specific styling
.fullscreen-button {
  &:hover:not(:disabled) {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }
}

// Smartphone Frame (iPhone style) with realistic drop shadow
.smartphone {
  width: 50vh !important;
  height: 100vh !important;
  background: #000;
  border-radius: 40px;
  padding: 2%;
  position: relative;
  /* Realistic iPhone drop shadow */
  box-shadow:
  30px 45px 80px rgba(0, 0, 0, 0.5),
  15px 20px 35px rgba(0, 0, 0, 0.3),
  5px 8px 20px rgba(0, 0, 0, 0.15),
  0px 2px 8px rgba(0, 0, 0, 0.1);
    transform: scale(0.7) !important;
}

// Screen
.screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 32px;
  position: relative;
  overflow: hidden;
}

.notch {
  position: absolute;
  top: 0;
  left: 30%;
  transform: translateX(-50%);
  width: 40%;
  height: 1%;
  background: #000;
  border-radius: 0 0 15px 15px;
  z-index: 10;
}

.content-area {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
}

// Side buttons
.side-button {
  position: absolute;
  background: #000;
  border-radius: 2px;
}

.volume-up {
  left: -3px;
  top: 120px;
  width: 6px;
  height: 30px;
}

.volume-down {
  left: -3px;
  top: 160px;
  width: 6px;
  height: 30px;
}

.power-button {
  right: -3px;
  top: 140px;
  width: 6px;
  height: 50px;
}

// Mobile iframe styling
.mobile-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  border-radius: 32px;
  overflow: hidden;

  // Hide scrollbars but allow scrolling
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */

  // Ensure proper HTML rendering
  &[srcdoc] {
    background: white;
  }
}

// No Content Placeholder
.no-content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--placeholder-bg);
  color: var(--placeholder-text);
  border-radius: 32px;
  text-align: center;
  padding: 2rem;

  .placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
  }

  .placeholder-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
  }

  .placeholder-description {
    font-size: 0.875rem;
    opacity: 0.8;
    max-width: 200px;
    line-height: 1.4;
  }
}

// No Pages Placeholder
.no-pages-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-secondary);
  text-align: center;
  min-height: 100px;

  .placeholder-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.6;
  }

  .placeholder-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
  }

  .placeholder-description {
    font-size: 0.875rem;
    opacity: 0.8;
    max-width: 300px;
    line-height: 1.4;
  }
}

// Pagination Container
.pagination-container {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 400px;
  flex-shrink: 0;
}

.navigation-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-button {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  font-size: 18px;
  font-weight: bold;

  &:hover:not(:disabled) {
    background: rgba(0, 0, 0, 0.05);
    color: #333;
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.pill-container {
  background: white;
  border: 1px dashed #ddd;
  border-radius: 25px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.home-icon {
  width: 20px;
  height: 20px;
  color: #666;
  flex-shrink: 0;
}

.pill-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

// Responsive Design
@media (max-width: 768px) {
  .smartphone {
    width: 280px;
    height: 560px;
  }

  .mobile-iframe {
    border-radius: 32px;
  }

  .navigation-container {
    gap: 12px;
  }

  .nav-button {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .pill-container {
    padding: 6px 12px;
    min-width: 100px;
  }

  .pill-text {
    font-size: 12px;
    max-width: 100px;
  }

  .home-icon {
    width: 18px;
    height: 18px;
  }
}

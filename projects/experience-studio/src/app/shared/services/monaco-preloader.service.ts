import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, tap, switchMap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';

/**
 * Service to preload Monaco Editor resources in production
 * Handles chunk loading issues and provides fallback mechanisms
 */
@Injectable({
  providedIn: 'root'
})
export class MonacoPreloaderService {
  private logger = createLogger('MonacoPreloaderService');
  private preloadStatus$ = new BehaviorSubject<'idle' | 'loading' | 'loaded' | 'error'>('idle');
  private preloadError$ = new BehaviorSubject<string | null>(null);

  /**
   * Get preload status
   */
  get status$(): Observable<'idle' | 'loading' | 'loaded' | 'error'> {
    return this.preloadStatus$.asObservable();
  }

  /**
   * Get preload error
   */
  get error$(): Observable<string | null> {
    return this.preloadError$.asObservable();
  }

  /**
   * Preload Monaco Editor resources
   */
  preloadMonaco(): Observable<boolean> {
    if (this.preloadStatus$.value === 'loaded') {
      return of(true);
    }

    if (this.preloadStatus$.value === 'loading') {
      return this.status$.pipe(
        switchMap(status => status === 'loaded' ? of(true) : of(false))
      );
    }

    this.preloadStatus$.next('loading');
    this.preloadError$.next(null);
    this.logger.info('🚀 Starting Monaco Editor preload...');

    return this.loadMonacoChunks().pipe(
      tap(() => {
        this.preloadStatus$.next('loaded');
        this.logger.info('✅ Monaco Editor preload completed');
      }),
      catchError(error => {
        const errorMessage = `Monaco Editor preload failed: ${error.message}`;
        this.logger.error('❌ Monaco Editor preload failed:', error);
        this.preloadStatus$.next('error');
        this.preloadError$.next(errorMessage);
        return of(false);
      })
    );
  }

  /**
   * Load Monaco Editor chunks with retry mechanism
   */
  private loadMonacoChunks(): Observable<boolean> {
    return new Observable(observer => {
      // Try to preload the main Monaco chunks
      const chunkPromises = [
        this.loadChunk('monaco-core'),
        this.loadChunk('monaco-languages'),
        this.loadChunk('monaco-editor')
      ];

      Promise.all(chunkPromises)
        .then(() => {
          observer.next(true);
          observer.complete();
        })
        .catch(error => {
          // If chunk loading fails, try alternative approach
          this.logger.warn('Chunk loading failed, trying alternative approach:', error);
          this.loadMonacoAlternative()
            .then(() => {
              observer.next(true);
              observer.complete();
            })
            .catch(altError => {
              observer.error(altError);
            });
        });
    });
  }

  /**
   * Load individual chunk with timeout
   */
  private loadChunk(chunkName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Timeout loading chunk: ${chunkName}`));
      }, 15000);

      // Try to load the chunk by creating a script tag
      const script = document.createElement('script');
      script.async = true;
      script.onload = () => {
        clearTimeout(timeout);
        resolve();
      };
      script.onerror = () => {
        clearTimeout(timeout);
        reject(new Error(`Failed to load chunk: ${chunkName}`));
      };

      // In production, chunks might have different names
      const chunkPath = this.getChunkPath(chunkName);

      // Only append if we can determine the path
      if (chunkPath) {
        script.src = chunkPath;
        document.head.appendChild(script);
      } else {
        clearTimeout(timeout);
        resolve(); // Skip if we can't determine the path
      }
    });
  }

  /**
   * Get chunk path based on environment
   */
  private getChunkPath(chunkName: string): string | null {
    // In production, we need to find the actual chunk files
    // This is a simplified approach - in reality, you might need to
    // read from a manifest file or use webpack's __webpack_require__
    const isProduction = window.location.hostname !== 'localhost';

    if (!isProduction) {
      // In development, chunks are loaded automatically
      return null;
    }

    // In production, try to find the chunk in the document
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const chunkScript = scripts.find(script =>
      script.getAttribute('src')?.includes(chunkName)
    );

    return chunkScript?.getAttribute('src') || null;
  }

  /**
   * Alternative loading approach when chunk loading fails
   */
  private loadMonacoAlternative(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Try to load Monaco directly
      import('monaco-editor')
        .then(() => {
          this.logger.info('✅ Monaco loaded via alternative method');
          resolve();
        })
        .catch(error => {
          this.logger.error('❌ Alternative Monaco loading failed:', error);
          reject(error);
        });
    });
  }

  /**
   * Check if Monaco is available
   */
  isMonacoAvailable(): boolean {
    return !!(window as any).monaco || this.preloadStatus$.value === 'loaded';
  }

  /**
   * Reset preloader state
   */
  reset(): void {
    this.preloadStatus$.next('idle');
    this.preloadError$.next(null);
  }
}

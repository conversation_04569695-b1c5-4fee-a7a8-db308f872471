// import { Injectable, inject, signal, computed, DestroyRef } from '@angular/core';
// import { Observable, forkJoin, merge, of, EMPTY, timer } from 'rxjs';
// import { map, catchError, tap, startWith, takeUntil, finalize, switchMap } from 'rxjs/operators';
// import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

// import { createLogger } from '../utils/logger';
// import { CodeGenerationIntroService } from './code-generation-intro.service';
// import { UIDesignIntroService } from './ui-design-intro.service';

// // Regeneration-specific logger utility
// const createRegenerationLogger = (fileName: string, functionName: string) => ({
//   info: (...args: any[]) => console.log(`[REGEN][${fileName}.${functionName}]`, ...args),
//   error: (...args: any[]) => console.error(`[REGEN][${fileName}.${functionName}]`, ...args),
//   debug: (...args: any[]) => console.debug(`[REGEN][${fileName}.${functionName}]`, ...args),
//   warn: (...args: any[]) => console.warn(`[REGEN][${fileName}.${functionName}]`, ...args)
// });

// /**
//  * Parallel Regeneration Service
//  *
//  * MODIFIED: Implements FULLY SEQUENTIAL execution for code generation regeneration:
//  * - Intro API executes first (sequential)
//  * - Regenerate/code API executes second (sequential, after intro completes)
//  * - SSE monitoring executes third (sequential, after regenerate/code completes)
//  * - Ensures strict sequential order for regeneration process
//  * - Uses Angular 19+ patterns with inject(), Signals, and takeUntilDestroyed()
//  * - Maintains 100% backward compatibility with existing interfaces
//  */

// interface ParallelRegenerationConfig {
//   enableIntroAPI: boolean;
//   enableSSE: boolean;
//   timeoutMs: number;
//   retryAttempts: number;
// }

// interface ParallelRegenerationResult {
//   introResult: {
//     success: boolean;
//     text: string;
//     error?: any;
//   };
//   regenerationResult: {
//     success: boolean;
//     data: any;
//     error?: any;
//   };
//   sseResult: {
//     success: boolean;
//     connected: boolean;
//     error?: any;
//   };
//   overallSuccess: boolean;
//   executionTimeMs: number;
// }

// interface ParallelRegenerationState {
//   isExecuting: boolean;
//   introCompleted: boolean;
//   regenerationCompleted: boolean;
//   sseConnected: boolean;
//   hasErrors: boolean;
//   startTime: number;
// }

// @Injectable({
//   providedIn: 'root'
// })
// export class ParallelRegenerationService {
//   private readonly logger = createLogger('ParallelRegenerationService');
//   private readonly destroyRef = inject(DestroyRef);
//   private readonly codeGenerationIntroService = inject(CodeGenerationIntroService);
//   private readonly uiDesignIntroService = inject(UIDesignIntroService);

//   // Angular 19+ Signals for reactive state management
//   private readonly executionState = signal<ParallelRegenerationState>({
//     isExecuting: false,
//     introCompleted: false,
//     regenerationCompleted: false,
//     sseConnected: false,
//     hasErrors: false,
//     startTime: 0
//   });

//   // Computed signals for reactive UI updates
//   readonly isExecuting = computed(() => this.executionState().isExecuting);
//   readonly executionProgress = computed(() => {
//     const state = this.executionState();
//     const completed = [state.introCompleted, state.regenerationCompleted, state.sseConnected]
//       .filter(Boolean).length;
//     return Math.round((completed / 3) * 100);
//   });

//   // Default configuration optimized for code generation
//   private readonly defaultConfig: ParallelRegenerationConfig = {
//     enableIntroAPI: true,
//     enableSSE: true,
//     timeoutMs: 45000, // Increased timeout for better reliability
//     retryAttempts: 3   // Increased retries for better resilience
//   };

//   /**
//    * Execute parallel regeneration for code generation
//    *
//    * @param userRequest - User's regeneration request
//    * @param codeFiles - Current code files
//    * @param regenerationAPICall - Observable for the main regeneration API
//    * @param sseStartCallback - Callback to start SSE monitoring
//    * @param targetMessageId - Optional message ID for text replacement
//    * @param config - Optional configuration overrides
//    * @returns Observable<ParallelRegenerationResult>
//    */
//   executeParallelCodeRegeneration(
//     userRequest: string,
//     codeFiles: any[],
//     regenerationAPICall: Observable<any>,
//     sseStartCallback: () => void,
//     targetMessageId?: string,
//     config?: Partial<ParallelRegenerationConfig>
//   ): Observable<ParallelRegenerationResult> {
//     const finalConfig = { ...this.defaultConfig, ...config };
//     const regenLogger = createRegenerationLogger('ParallelRegenerationService', 'executeParallelCodeRegeneration');

//     regenLogger.info('🚀 Starting FULLY SEQUENTIAL code regeneration execution', {
//       userRequest: userRequest.substring(0, 100),
//       codeFilesCount: codeFiles.length,
//       enableIntroAPI: finalConfig.enableIntroAPI,
//       enableSSE: finalConfig.enableSSE,
//       targetMessageId,
//       timestamp: new Date().toISOString(),
//       executionMode: 'FULLY_SEQUENTIAL'
//     });
//     this.logger.info('🚀 Starting FULLY SEQUENTIAL code regeneration execution', {
//       userRequest: userRequest.substring(0, 100),
//       codeFilesCount: codeFiles.length,
//       enableIntroAPI: finalConfig.enableIntroAPI,
//       enableSSE: finalConfig.enableSSE,
//       targetMessageId,
//       timestamp: new Date().toISOString(),
//       executionMode: 'FULLY_SEQUENTIAL'
//     });

//     // Initialize execution state
//     this.updateExecutionState({
//       isExecuting: true,
//       introCompleted: false,
//       regenerationCompleted: false,
//       sseConnected: false,
//       hasErrors: false,
//       startTime: Date.now()
//     });

//     // Create parallel observables
//     const parallelOperations = this.createParallelCodeOperations(
//       userRequest,
//       codeFiles,
//       regenerationAPICall,
//       sseStartCallback,
//       targetMessageId,
//       finalConfig
//     );

//     // Execute operations with FULLY SEQUENTIAL approach for code regeneration
//     return this.executeSequentialOperations(parallelOperations, finalConfig);
//   }

//   /**
//    * Execute parallel regeneration for UI Design
//    *
//    * @param userRequest - User's regeneration request
//    * @param selectedNodes - Selected UI Design nodes
//    * @param regenerationAPICall - Observable for the main regeneration API
//    * @param sseStartCallback - Callback to start SSE monitoring
//    * @param targetMessageId - Optional message ID for text replacement
//    * @param config - Optional configuration overrides
//    * @returns Observable<ParallelRegenerationResult>
//    */
//   executeParallelUIDesignRegeneration(
//     userRequest: string,
//     selectedNodes: any[],
//     regenerationAPICall: Observable<any>,
//     sseStartCallback: () => void,
//     targetMessageId?: string,
//     config?: Partial<ParallelRegenerationConfig>
//   ): Observable<ParallelRegenerationResult> {
//     const finalConfig = { ...this.defaultConfig, ...config };

//     this.logger.info('🚀 Starting SEQUENTIAL intro + PARALLEL UI Design regeneration execution', {
//       userRequest: userRequest.substring(0, 100),
//       selectedNodesCount: selectedNodes.length,
//       enableIntroAPI: finalConfig.enableIntroAPI,
//       enableSSE: finalConfig.enableSSE,
//       targetMessageId,
//       executionMode: 'SEQUENTIAL_INTRO_THEN_PARALLEL'
//     });

//     // Initialize execution state
//     this.updateExecutionState({
//       isExecuting: true,
//       introCompleted: false,
//       regenerationCompleted: false,
//       sseConnected: false,
//       hasErrors: false,
//       startTime: Date.now()
//     });

//     // Create parallel observables for UI Design
//     const parallelOperations = this.createParallelUIDesignOperations(
//       userRequest,
//       selectedNodes,
//       regenerationAPICall,
//       sseStartCallback,
//       targetMessageId,
//       finalConfig
//     );

//     // Execute operations with parallel approach for UI Design (unchanged)
//     return this.executeUIDesignParallelOperations(parallelOperations, finalConfig);
//   }

//   /**
//    * Create parallel operations for code regeneration
//    */
//   private createParallelCodeOperations(
//     userRequest: string,
//     codeFiles: any[],
//     regenerationAPICall: Observable<any>,
//     sseStartCallback: () => void,
//     targetMessageId?: string,
//     config?: ParallelRegenerationConfig
//   ) {
//     const operations: { [key: string]: Observable<any> } = {};

//     // 1. Intro API Operation (if enabled)
//     if (config?.enableIntroAPI) {
//       // Use public method to convert code files to intro items
//       const codeItems = codeFiles.map(file => ({
//         fileName: file.fileName || file.path || 'unknown',
//         content: file.content || file.code || '',
//         language: this.detectLanguageFromFileName(file.fileName || file.path || '')
//       }));

//       operations['intro'] = this.codeGenerationIntroService
//         .generateCodeIntroMessage(userRequest, codeItems)
//         .pipe(
//           map(text => ({ success: true, text })),
//           catchError(error => {
//             this.logger.warn('⚠️ Intro API failed, using fallback', error);
//             return of({
//               success: false,
//               text: 'Processing your request...',
//               error
//             });
//           }),
//           tap(() => this.updateExecutionState({ introCompleted: true })),
//           takeUntilDestroyed(this.destroyRef)
//         );
//     } else {
//       operations['intro'] = of({ success: true, text: 'Processing your request...' });
//     }

//     // 2. Regeneration API Operation
//     operations['regeneration'] = regenerationAPICall.pipe(
//       map(data => ({ success: true, data })),
//       catchError(error => {
//         this.logger.error('❌ Regeneration API failed', error);
//         return of({ success: false, data: null, error });
//       }),
//       // CRITICAL FIX: Do NOT mark regeneration as completed here - only SSE events should complete it
//       tap(() => {
//         this.logger.info('✅ Regeneration API completed - waiting for SSE DEPLOY+COMPLETED event');
//         // Do NOT call updateExecutionState({ regenerationCompleted: true }) here
//         // Only SSE events should mark regeneration as completed
//       }),
//       takeUntilDestroyed(this.destroyRef)
//     );

//     // 3. SSE Connection Operation (if enabled)
//     if (config?.enableSSE) {
//       operations['sse'] = timer(0).pipe(
//         tap(() => {
//           this.logger.info('🔌 Starting SSE connection immediately');
//           sseStartCallback();
//         }),
//         map(() => ({ success: true, connected: true })),
//         catchError(error => {
//           this.logger.warn('⚠️ SSE connection failed', error);
//           return of({ success: false, connected: false, error });
//         }),
//         tap(() => this.updateExecutionState({ sseConnected: true })),
//         takeUntilDestroyed(this.destroyRef)
//       );
//     } else {
//       operations['sse'] = of({ success: true, connected: false });
//     }

//     return operations;
//   }

//   /**
//    * Detect programming language from file name
//    */
//   private detectLanguageFromFileName(fileName: string): string {
//     const extension = fileName.split('.').pop()?.toLowerCase();
//     switch (extension) {
//       case 'ts': return 'typescript';
//       case 'js': return 'javascript';
//       case 'html': return 'html';
//       case 'css': return 'css';
//       case 'scss': return 'scss';
//       case 'json': return 'json';
//       default: return 'text';
//     }
//   }

//   /**
//    * Create parallel operations for UI Design regeneration
//    */
//   private createParallelUIDesignOperations(
//     userRequest: string,
//     selectedNodes: any[],
//     regenerationAPICall: Observable<any>,
//     sseStartCallback: () => void,
//     targetMessageId?: string,
//     config?: ParallelRegenerationConfig
//   ) {
//     const operations: { [key: string]: Observable<any> } = {};

//     // 1. Intro API Operation (if enabled)
//     if (config?.enableIntroAPI) {
//       // For UI Design, we'll use a simple fallback since the intro service doesn't expose a direct method
//       operations['intro'] = of('Preparing your UI design regeneration...').pipe(
//         map(text => ({ success: true, text })),
//         tap(() => {
//           this.logger.info('✅ UI Design intro message generated (fallback)');
//           this.updateExecutionState({ introCompleted: true });
//         }),
//         takeUntilDestroyed(this.destroyRef)
//       );
//     } else {
//       operations['intro'] = of({ success: true, text: 'Regenerating your UI design...' });
//     }

//     // 2. Regeneration API Operation
//     operations['regeneration'] = regenerationAPICall.pipe(
//       map(data => ({ success: true, data })),
//       catchError(error => {
//         this.logger.error('❌ UI Design Regeneration API failed', error);
//         return of({ success: false, data: null, error });
//       }),
//       tap(() => this.updateExecutionState({ regenerationCompleted: true })),
//       takeUntilDestroyed(this.destroyRef)
//     );

//     // 3. SSE Connection Operation (if enabled)
//     if (config?.enableSSE) {
//       operations['sse'] = timer(0).pipe(
//         tap(() => {
//           this.logger.info('🔌 Starting UI Design SSE connection immediately');
//           sseStartCallback();
//         }),
//         map(() => ({ success: true, connected: true })),
//         catchError(error => {
//           this.logger.warn('⚠️ UI Design SSE connection failed', error);
//           return of({ success: false, connected: false, error });
//         }),
//         tap(() => this.updateExecutionState({ sseConnected: true })),
//         takeUntilDestroyed(this.destroyRef)
//       );
//     } else {
//       operations['sse'] = of({ success: true, connected: false });
//     }

//     return operations;
//   }

//   /**
//    * COMMENTED OUT: Old parallel execution approach
//    * Execute operations with sequential intro + parallel regeneration/SSE
//    * MODIFIED: Changed from full parallel to sequential intro, then parallel execution
//    *
//    * OLD APPROACH (commented out):
//    * return forkJoin(operations).pipe(...) // All three operations in parallel
//    *
//    * PREVIOUS APPROACH:
//    * 1. Execute intro API sequentially
//    * 2. After intro completes, execute regeneration + SSE in parallel
//    */
//   /*
//   private executeParallelOperations(
//     operations: { [key: string]: Observable<any> },
//     config: ParallelRegenerationConfig
//   ): Observable<ParallelRegenerationResult> {
//     const startTime = Date.now();

//     this.logger.info('🔄 Starting SEQUENTIAL intro + PARALLEL regeneration execution', {
//       enableIntroAPI: config.enableIntroAPI,
//       enableSSE: config.enableSSE,
//       executionMode: 'SEQUENTIAL_INTRO_THEN_PARALLEL'
//     });

//     // PHASE 1: Execute intro API sequentially (if enabled)
//     const introExecution = operations['intro'].pipe(
//       tap(introResult => {
//         this.logger.info('✅ Intro API completed sequentially:', {
//           success: introResult.success,
//           textLength: introResult.text?.length || 0
//         });
//         this.updateExecutionState({ introCompleted: true });
//       }),
//       catchError(error => {
//         this.logger.warn('⚠️ Intro API failed in sequential execution, continuing with fallback', error);
//         return of({
//           success: false,
//           text: 'Preparing your regeneration...',
//           error
//         });
//       })
//     );

//     // PHASE 2: After intro completes, execute regeneration and SSE in parallel
//     return introExecution.pipe(
//       switchMap(introResult => {
//         this.logger.info('🚀 Intro completed, starting PARALLEL regeneration + SSE execution');

//         // Create parallel operations for regeneration and SSE
//         const parallelOperations = {
//           regeneration: operations['regeneration'],
//           sse: operations['sse']
//         };

//         return forkJoin(parallelOperations).pipe(
//           map(parallelResults => {
//             const executionTime = Date.now() - startTime;
//             const overallSuccess = introResult.success &&
//                                  parallelResults['regeneration'].success &&
//                                  parallelResults['sse'].success;

//             this.logger.info('✅ Sequential intro + parallel regeneration execution completed', {
//               executionTimeMs: executionTime,
//               overallSuccess,
//               introSuccess: introResult.success,
//               regenerationSuccess: parallelResults['regeneration'].success,
//               sseSuccess: parallelResults['sse'].success,
//               executionMode: 'SEQUENTIAL_INTRO_THEN_PARALLEL'
//             });

//             return {
//               introResult: introResult,
//               regenerationResult: parallelResults['regeneration'],
//               sseResult: parallelResults['sse'],
//               overallSuccess,
//               executionTimeMs: executionTime
//             };
//           })
//         );
//       }),
//       catchError(error => {
//         const executionTime = Date.now() - startTime;
//         this.logger.error('❌ Sequential intro + parallel regeneration execution failed', error);

//         return of({
//           introResult: { success: false, text: 'Failed to load intro', error },
//           regenerationResult: { success: false, data: null, error },
//           sseResult: { success: false, connected: false, error },
//           overallSuccess: false,
//           executionTimeMs: executionTime
//         });
//       }),
//       finalize(() => {
//         this.updateExecutionState({ isExecuting: false });
//       }),
//       takeUntilDestroyed(this.destroyRef)
//     );
//   }
//   */

//   /**
//    * Execute operations with FULLY SEQUENTIAL approach for code regeneration
//    * NEW APPROACH: All three operations execute in strict sequential order:
//    * 1. Execute intro API first (sequential)
//    * 2. Execute regenerate/code API second (sequential, after intro completes)
//    * 3. Execute SSE monitoring third (sequential, after regenerate/code completes)
//    */
//   private executeSequentialOperations(
//     operations: { [key: string]: Observable<any> },
//     config: ParallelRegenerationConfig
//   ): Observable<ParallelRegenerationResult> {
//     const startTime = Date.now();

//     this.logger.info('🔄 Starting FULLY SEQUENTIAL execution for code regeneration', {
//       enableIntroAPI: config.enableIntroAPI,
//       enableSSE: config.enableSSE,
//       executionMode: 'FULLY_SEQUENTIAL'
//     });

//     // PHASE 1: Execute intro API first (sequential)
//     const introExecution = operations['intro'].pipe(
//       tap(introResult => {
//         this.logger.info('✅ PHASE 1: Intro API completed sequentially:', {
//           success: introResult.success,
//           textLength: introResult.text?.length || 0
//         });
//         this.updateExecutionState({ introCompleted: true });
//       }),
//       catchError(error => {
//         this.logger.warn('⚠️ PHASE 1: Intro API failed, continuing with fallback', error);
//         return of({
//           success: false,
//           text: 'Preparing your regeneration...',
//           error
//         });
//       })
//     );

//     // PHASE 2: After intro completes, execute regenerate/code API (sequential)
//     return introExecution.pipe(
//       switchMap(introResult => {
//         this.logger.info('🚀 PHASE 2: Starting regenerate/code API execution (sequential)');

//         return operations['regeneration'].pipe(
//           tap(regenerationResult => {
//             this.logger.info('✅ PHASE 2: Regenerate/code API completed sequentially - waiting for SSE DEPLOY+COMPLETED:', {
//               success: regenerationResult.success
//             });
//             // CRITICAL FIX: Do NOT mark regeneration as completed here - only SSE events should complete it
//             // this.updateExecutionState({ regenerationCompleted: true });
//           }),
//           catchError(error => {
//             this.logger.error('❌ PHASE 2: Regenerate/code API failed', error);
//             return of({ success: false, data: null, error });
//           }),
//           // PHASE 3: After regenerate/code completes, execute SSE monitoring (sequential)
//           switchMap(regenerationResult => {
//             this.logger.info('🚀 PHASE 3: Starting SSE monitoring (sequential)');

//             return operations['sse'].pipe(
//               tap(sseResult => {
//                 this.logger.info('✅ PHASE 3: SSE monitoring completed sequentially:', {
//                   success: sseResult.success,
//                   connected: sseResult.connected
//                 });
//                 this.updateExecutionState({ sseConnected: sseResult.connected });
//               }),
//               catchError(error => {
//                 this.logger.error('❌ PHASE 3: SSE monitoring failed', error);
//                 return of({ success: false, connected: false, error });
//               }),
//               map(sseResult => {
//                 const executionTime = Date.now() - startTime;
//                 const overallSuccess = introResult.success &&
//                                      regenerationResult.success &&
//                                      sseResult.success;

//                 this.logger.info('✅ FULLY SEQUENTIAL execution completed', {
//                   executionTimeMs: executionTime,
//                   overallSuccess,
//                   introSuccess: introResult.success,
//                   regenerationSuccess: regenerationResult.success,
//                   sseSuccess: sseResult.success,
//                   executionMode: 'FULLY_SEQUENTIAL'
//                 });

//                 return {
//                   introResult: introResult,
//                   regenerationResult: regenerationResult,
//                   sseResult: sseResult,
//                   overallSuccess,
//                   executionTimeMs: executionTime
//                 };
//               })
//             );
//           })
//         );
//       }),
//       catchError(error => {
//         const executionTime = Date.now() - startTime;
//         this.logger.error('❌ FULLY SEQUENTIAL execution failed', error);

//         return of({
//           introResult: { success: false, text: 'Failed to load intro', error },
//           regenerationResult: { success: false, data: null, error },
//           sseResult: { success: false, connected: false, error },
//           overallSuccess: false,
//           executionTimeMs: executionTime
//         });
//       }),
//       finalize(() => {
//         this.updateExecutionState({ isExecuting: false });
//       }),
//       takeUntilDestroyed(this.destroyRef)
//     );
//   }

//   /**
//    * Execute operations with parallel approach for UI Design regeneration
//    * UI Design keeps the original parallel execution approach
//    */
//   private executeUIDesignParallelOperations(
//     operations: { [key: string]: Observable<any> },
//     config: ParallelRegenerationConfig
//   ): Observable<ParallelRegenerationResult> {
//     const startTime = Date.now();

//     this.logger.info('🔄 Starting PARALLEL execution for UI Design regeneration', {
//       enableIntroAPI: config.enableIntroAPI,
//       enableSSE: config.enableSSE,
//       executionMode: 'PARALLEL_UI_DESIGN'
//     });

//     // UI Design uses original parallel approach (all operations concurrent)
//     return forkJoin(operations).pipe(
//       map(results => {
//         const executionTime = Date.now() - startTime;
//         const overallSuccess = results['intro'].success && results['regeneration'].success && results['sse'].success;

//         this.logger.info('✅ PARALLEL UI Design regeneration execution completed', {
//           executionTimeMs: executionTime,
//           overallSuccess,
//           introSuccess: results['intro'].success,
//           regenerationSuccess: results['regeneration'].success,
//           sseSuccess: results['sse'].success,
//           executionMode: 'PARALLEL_UI_DESIGN'
//         });

//         return {
//           introResult: results['intro'],
//           regenerationResult: results['regeneration'],
//           sseResult: results['sse'],
//           overallSuccess,
//           executionTimeMs: executionTime
//         };
//       }),
//       catchError(error => {
//         const executionTime = Date.now() - startTime;
//         this.logger.error('❌ PARALLEL UI Design regeneration execution failed', error);

//         return of({
//           introResult: { success: false, text: 'Failed to load intro', error },
//           regenerationResult: { success: false, data: null, error },
//           sseResult: { success: false, connected: false, error },
//           overallSuccess: false,
//           executionTimeMs: executionTime
//         });
//       }),
//       finalize(() => {
//         this.updateExecutionState({ isExecuting: false });
//       }),
//       takeUntilDestroyed(this.destroyRef)
//     );
//   }

//   /**
//    * Update execution state using Angular Signals
//    */
//   private updateExecutionState(updates: Partial<ParallelRegenerationState>): void {
//     this.executionState.update(current => ({ ...current, ...updates }));
//   }

//   /**
//    * Get current execution state
//    */
//   getExecutionState(): ParallelRegenerationState {
//     return this.executionState();
//   }

//   /**
//    * Reset execution state
//    */
//   resetExecutionState(): void {
//     this.updateExecutionState({
//       isExecuting: false,
//       introCompleted: false,
//       regenerationCompleted: false,
//       sseConnected: false,
//       hasErrors: false,
//       startTime: 0
//     });
//   }
// }

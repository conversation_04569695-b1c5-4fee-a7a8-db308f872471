import { Injectable, inject, DestroyRef, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, Subject, BehaviorSubject, EMPTY, timer, Subscription } from 'rxjs';
import { switchMap, catchError, retry, share, takeUntil, tap, filter } from 'rxjs/operators';
import { SSEService, SSEEvent, SSEOptions } from './sse.service';
import { SSEDataProcessorService } from './sse-data-processor.service';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';
import { createLogger } from '../utils/logger';
import { environment } from '../../../environments/environment';


/**
 * Enhanced SSE Service for project status monitoring
 *
 * This service extends the base SSE service with specific functionality for
 * project status monitoring, including:
 * - Integration with SSE data processor
 * - Fallback mechanisms to polling
 * - Enhanced error handling and reconnection
 * - Compatibility with existing polling interface
 *
 * Features:
 * - Uses Angular 19+ patterns (inject(), takeUntilDestroyed())
 * - Implements exponential backoff for reconnection
 * - Provides fallback to polling on SSE failure
 * - Maintains same interface as polling service
 * - Comprehensive error handling and logging
 */
@Injectable({
  providedIn: 'root'
})
export class EnhancedSSEService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly sseService = inject(SSEService);
  private readonly sseDataProcessor = inject(SSEDataProcessorService);
  private readonly pollingProcessor = inject(NewPollingResponseProcessorService);
  private readonly logger = createLogger('EnhancedSSEService');

  // Connection state management
  private isConnectedSubject = new BehaviorSubject<boolean>(false);
  private connectionErrorSubject = new Subject<any>();
  private fallbackTriggeredSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Current connection details
  private currentJobId: string | null = null;
  private currentProjectId: string | null = null;
  private connectionAttempts = 0;
  private maxConnectionAttempts = 5;
  private fallbackCallback: (() => void) | null = null;

  // ENHANCED: Retry event filtering support
  private retryTimestamp: number | null = null;
  private isRetryFilteringEnabled = false;

  // ENHANCED: Connection management to prevent duplicates
  private activeConnections = new Map<string, Observable<any>>();

  // CRITICAL FIX: Track pending connections to prevent race conditions
  private pendingConnections = new Set<string>();

  // ENHANCED: Track active subscriptions for proper cleanup
  private activeSubscriptions = new Map<string, Subscription>();
  private connectionTimers = new Map<string, number>();

  // ENHANCED: Track UI states associated with connections for cleanup
  private connectionUIStates = new Map<string, {
    isLoading: boolean;
    progressDescription: string;
    lastActivity: number;
  }>();

  // Event ID caching for regeneration checkpointing (Angular 19+ pattern)
  private readonly regenerationSessionEventIds = signal<Map<string, string>>(new Map());

  // Public observables
  public readonly isConnected$ = this.isConnectedSubject.asObservable();
  public readonly connectionError$ = this.connectionErrorSubject.asObservable();
  public readonly fallbackTriggered$ = this.fallbackTriggeredSubject.asObservable();

  // CRITICAL: Expose the same observables as PollingService for 100% compatibility
  // These observables are consumed by components and must match exactly

  // Primary observables from SSE data processor
  public readonly status$ = this.sseDataProcessor.status$;
  public readonly progress$ = this.sseDataProcessor.progress$;
  public readonly progressDescription$ = this.sseDataProcessor.progressDescription$;
  public readonly logs$ = this.sseDataProcessor.logs$;
  public readonly isProcessing$ = this.sseDataProcessor.isProcessing$;
  public readonly error$ = this.sseDataProcessor.error$;

  // Additional observables from the polling processor for complete compatibility
  public readonly currentProgress$ = this.pollingProcessor.currentProgress$;
  public readonly currentStatus$ = this.pollingProcessor.currentStatus$;
  public readonly artifactData$ = this.pollingProcessor.artifactData$;
  public readonly fileList$ = this.pollingProcessor.fileList$;
  public readonly codeFiles$ = this.pollingProcessor.codeFiles$;
  public readonly previewUrl$ = this.pollingProcessor.previewUrl$;
  public readonly projectInfo$ = this.pollingProcessor.projectInfo$;

  constructor() {
    this.logger.info('🔧 Enhanced SSE Service initialized');

    // Setup automatic cleanup
    this.destroyRef.onDestroy(() => {
      this.cleanup();
    });

    // Monitor base SSE service connection state
    this.setupSSEServiceMonitoring();

    // ENHANCED: Log initial header support status
    const polyfillInfo = this.sseService.getPolyfillInfo();
    this.logger.info('🚀 Enhanced SSE Service - Header Support:', polyfillInfo);

    // Expose testing method to window for debugging (development only)
    if (!environment.production) {
      (window as any).testSSEHeaders = () => this.testSSEHeaders();
      (window as any).getSSEStatus = () => this.getServiceStatus();
      (window as any).testFinalEvent = (isFinal: boolean = true) => this.testFinalEventHandling(isFinal);
      (window as any).getSSEConnections = () => this.getConnectionStatistics();
      this.logger.info('🧪 SSE testing methods exposed to window: testSSEHeaders(), getSSEStatus(), testFinalEvent(), getSSEConnections()');
    }
  }

  /**
   * Start SSE monitoring for project status
   * @param projectId Project ID to monitor
   * @param jobId Job ID to monitor
   * @param options SSE configuration options
   * @param fallbackCallback Callback to trigger fallback to polling
   * @param generationType Optional generation type context for optimization
   */
  startMonitoring(
    projectId: string,
    jobId: string,
    options?: Partial<SSEOptions>,
    fallbackCallback?: () => void,
    generationType?: 'initial-code-gen' | 'code-regen' | 'unknown'
  ): Observable<any> {
    if (!projectId || !jobId) {
      this.logger.error('Cannot start SSE monitoring: missing project ID or job ID');
      return EMPTY;
    }

    // ENHANCED: Comprehensive connection session validation with deduplication
    const connectionKey = `${projectId}-${jobId}`;

    // ENHANCED: Use comprehensive session validation
    const validation = this.validateConnectionSession(projectId, jobId, generationType);

    if (!validation.isValid) {
      if (validation.shouldCleanup) {
        this.logger.info('🧹 Cleaning up stale/orphaned resources before creating new connection:', {
          connectionKey,
          reason: validation.reason
        });
        this.cleanupConnection(connectionKey);
      } else if (validation.existingConnection) {
        this.logger.info('🔄 Reusing existing valid connection:', {
          connectionKey,
          reason: validation.reason
        });
        return validation.existingConnection;
      } else {
        this.logger.warn('🚫 Connection validation failed without existing connection:', {
          connectionKey,
          reason: validation.reason
        });
        return EMPTY;
      }
    }

    // CRITICAL FIX: Mark connection as pending to prevent race conditions
    this.pendingConnections.add(connectionKey);
    this.logger.info(`🔒 Marked connection as pending: ${connectionKey}`);

    // ENHANCED: Log connection creation lifecycle event
    this.logConnectionLifecycleEvent('created', connectionKey, {
      projectId,
      jobId,
      generationType,
      options: options || {},
      validationPassed: true
    });

    // ENHANCED: Additional check for same job monitoring with cleanup
    if (this.currentJobId === jobId && this.currentProjectId === projectId && this.isMonitoring()) {
      this.logger.info(`🧹 Already monitoring same job - cleaning up before new connection: ${projectId}, job: ${jobId}`);
      this.stopMonitoring();
    }

    this.logger.info(`🚀 Starting NEW SSE monitoring for project: ${projectId}, job: ${jobId}`, {
      connectionKey,
      activeConnectionsCount: this.activeConnections.size,
      pendingConnectionsCount: this.pendingConnections.size,
      baseSSEConnected: this.sseService.isConnected(),
      timestamp: new Date().toISOString()
    });

    // ENHANCED: Log connection status and header support before starting
    this.logConnectionStatus('Before Starting SSE Monitoring');

    // Log header support status
    const polyfillInfo = this.sseService.getPolyfillInfo();
    this.logger.info('🔧 SSE Header Support Status:', {
      method: polyfillInfo.headerMethod,
      isSSECompliant: polyfillInfo.isSSECompliant,
      polyfillAvailable: polyfillInfo.polyfillAvailable,
      willUseHeaders: polyfillInfo.supportsHeaders && polyfillInfo.defaultUseHeaders
    });

    // Store connection details
    this.currentProjectId = projectId;
    this.currentJobId = jobId;
    this.fallbackCallback = fallbackCallback || null;
    this.connectionAttempts = 0;

    // Reset data processor state
    this.sseDataProcessor.reset();

    // ENHANCED: Detect generation type for optimization
    const detectedGenerationType = this.detectGenerationType(generationType, options);

    // Configure SSE options with enhanced settings and event ID checkpointing
    // ENHANCED: Now includes header-based options for EventSource polyfill and query parameters
    // CRITICAL FIX: Increased timeouts to prevent SSE connection errors
    const enhancedOptions: Partial<SSEOptions> = {
      reconnectInterval: 5000, // INCREASED: 5 seconds between reconnects
      maxReconnectAttempts: this.maxConnectionAttempts,
      enableExponentialBackoff: true,
      backoffFactor: 1.5,
      maxBackoffInterval: 60000, // INCREASED: 1 minute max backoff
      enableHeartbeat: true,
      heartbeatInterval: 60000, // INCREASED: 1 minute heartbeat interval
      useHeadersForEventId: true, // NEW: Enable header-based last-event-id
      customHeaders: {
        'Cache-Control': 'no-cache',
        'Accept': 'text/event-stream'
      },
      // OPTIMIZATION: Add generation type context and since parameter
      generationType: detectedGenerationType,
      enableSinceParameter: true, // Enable since=0 optimization
      ...options
    };

    // ENHANCED: Add cached event ID for checkpointing (supports cross-session compatibility)
    const sessionKey = `${projectId}-${jobId}`;
    const cachedEventId = this.getCachedEventId(sessionKey);
    if (cachedEventId) {
      enhancedOptions.lastEventId = cachedEventId;
      this.logger.info('🔄 Using cached event ID for generation/regeneration checkpointing:', {
        sessionKey,
        cachedEventId,
        projectId,
        jobId,
        note: 'May be from initial generation or previous regeneration'
      });
    } else {
      this.logger.info('🆕 First generation connection - no cached event ID available:', {
        sessionKey,
        projectId,
        jobId,
        note: 'This is likely the initial generation'
      });
    }

    this.logger.info('🔧 About to start SSE connection with enhanced options:', {
      projectId,
      jobId,
      explicitGenerationType: generationType,
      detectedGenerationType,
      enableSinceParameter: enhancedOptions.enableSinceParameter,
      optimizationEnabled: enhancedOptions.enableSinceParameter && detectedGenerationType === 'initial-code-gen',
      shouldAddSinceParam: detectedGenerationType === 'initial-code-gen',
      enhancedOptions: {
        generationType: enhancedOptions.generationType,
        enableSinceParameter: enhancedOptions.enableSinceParameter,
        reconnectInterval: enhancedOptions.reconnectInterval
      }
    });
    this.logger.info('🔧 SSE Service status before connect:', this.sseService.getConnectionState());

    // Start SSE connection
    const sseObservable = this.sseService.connect(jobId, enhancedOptions).pipe(
      tap((sseEvent) => {
        this.connectionAttempts++;
        this.isConnectedSubject.next(true);
        this.logger.info('✅ SSE connection established, received event:', {
          type: sseEvent.event,
          id: sseEvent.id,
          dataLength: sseEvent.data?.length
        });

        // ENHANCED: Handle automatic connection closure for initial-code-gen final events
        this.handlePotentialFinalEvent(sseEvent, connectionKey);
      }),
      switchMap((sseEvent: SSEEvent) => {
        this.logger.info('🔄 Processing SSE event through data processor with session context:', {
          type: sseEvent.event,
          id: sseEvent.id,
          sessionKey: `${projectId}-${jobId}`,
          checkpointEnabled: !!sseEvent.id
        });
        // ENHANCED: Process each SSE event through the data processor with session key for checkpoint tracking
        const sessionKey = `${projectId}-${jobId}`;
        return this.sseDataProcessor.processSSEEvent(sseEvent, sessionKey);
      }),
      catchError((error) => {
        this.logger.error('❌ SSE connection error:', error);
        this.logger.error('❌ SSE Service status on error:', this.sseService.getConnectionState());
        this.handleConnectionError(error);
        // ENHANCED: Clean up connection on error
        this.cleanupConnection(connectionKey);
        // CRITICAL FIX: Also remove from pending connections on error
        this.pendingConnections.delete(connectionKey);
        return EMPTY;
      }),
      retry({
        count: this.maxConnectionAttempts,
        delay: (_error, retryCount) => {
          const delay = Math.min(3000 * Math.pow(1.5, retryCount - 1), 30000);
          this.logger.info(`🔄 Retrying SSE connection in ${delay}ms (attempt ${retryCount})`);
          return timer(delay);
        }
      }),
      share()
    );

    // ENHANCED: Store the connection to prevent duplicates
    this.activeConnections.set(connectionKey, sseObservable);

    // CRITICAL FIX: Remove from pending connections now that it's active
    this.pendingConnections.delete(connectionKey);

    // ENHANCED: Initialize UI state tracking for this connection
    this.connectionUIStates.set(connectionKey, {
      isLoading: true,
      progressDescription: 'Connecting...',
      lastActivity: Date.now()
    });

    this.logger.info('🚀 SSE Observable created and stored, returning to subscriber', {
      connectionKey,
      activeConnectionsCount: this.activeConnections.size,
      pendingConnectionsCount: this.pendingConnections.size,
      uiStateInitialized: this.connectionUIStates.has(connectionKey)
    });

    // ENHANCED: Log connection status after creation
    this.logConnectionStatus('After Creating SSE Connection');

    return sseObservable;
  }

  /**
   * ENHANCED: Track subscription for proper cleanup
   * This method should be called by consumers when they subscribe to SSE observables
   */
  trackSubscription(connectionKey: string, subscription: Subscription): void {
    this.logger.info('📝 Tracking subscription for connection:', {
      connectionKey,
      subscriptionClosed: subscription.closed,
      totalTrackedSubscriptions: this.activeSubscriptions.size
    });

    // Store the subscription for cleanup
    this.activeSubscriptions.set(connectionKey, subscription);

    // Update UI state to reflect active subscription
    if (this.connectionUIStates.has(connectionKey)) {
      const uiState = this.connectionUIStates.get(connectionKey)!;
      uiState.isLoading = true;
      uiState.progressDescription = 'Connected';
      uiState.lastActivity = Date.now();
    }
  }

  /**
   * ENHANCED: Update UI state for a connection
   */
  updateConnectionUIState(connectionKey: string, updates: Partial<{
    isLoading: boolean;
    progressDescription: string;
  }>): void {
    if (this.connectionUIStates.has(connectionKey)) {
      const uiState = this.connectionUIStates.get(connectionKey)!;
      Object.assign(uiState, updates, { lastActivity: Date.now() });

      this.logger.debug('🎛️ Updated UI state for connection:', {
        connectionKey,
        updates,
        currentState: uiState
      });
    }
  }

  /**
   * ENHANCED: Reset all UI states for clean regeneration start
   * This method coordinates with UI components to reset their loading states
   */
  resetAllUIStates(): void {
    this.logger.info('🧹 Resetting all UI states for clean regeneration start');

    try {
      // 1. Clear all connection UI states
      this.connectionUIStates.clear();

      // 2. Reset connection state observables
      this.isConnectedSubject.next(false);

      // 3. Clear any pending error states
      this.connectionErrorSubject = new Subject<any>();
      this.fallbackTriggeredSubject = new Subject<string>();

      // 4. ENHANCED: Coordinate with UI services for comprehensive state reset
      this.coordinateUIServiceReset();

      this.logger.info('✅ All UI states reset successfully');

    } catch (error) {
      this.logger.error('❌ Error resetting UI states:', {
        error: error instanceof Error ? error.message : String(error),
        fallbackAction: 'Continuing with partial reset'
      });
    }
  }

  /**
   * ENHANCED: Coordinate with UI services for comprehensive state reset
   * This method ensures all related UI services are properly reset
   */
  private coordinateUIServiceReset(): void {
    try {
      // Note: We don't directly inject UI services here to avoid circular dependencies
      // Instead, we emit events that UI components can listen to for state reset

      // Emit UI reset event for components to listen to
      this.connectionErrorSubject.next({
        type: 'ui_reset_requested',
        timestamp: Date.now(),
        reason: 'sse_connection_cleanup'
      });

      this.logger.info('🔄 UI service reset coordination completed');

    } catch (error) {
      this.logger.error('❌ Error coordinating UI service reset:', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * ENHANCED: Get current UI state for a connection
   */
  getConnectionUIState(connectionKey: string): {
    isLoading: boolean;
    progressDescription: string;
    lastActivity: number;
  } | null {
    return this.connectionUIStates.get(connectionKey) || null;
  }

  /**
   * ENHANCED: Check if any connections are currently loading
   */
  hasActiveLoadingStates(): boolean {
    for (const [key, state] of this.connectionUIStates.entries()) {
      if (state.isLoading) {
        this.logger.debug('🔍 Found active loading state:', { connectionKey: key, state });
        return true;
      }
    }
    return false;
  }

  /**
   * ENHANCED: Validate connection session to prevent duplicates
   * Implements comprehensive session-based deduplication with validation
   */
  private validateConnectionSession(projectId: string, jobId: string, generationType?: string): {
    isValid: boolean;
    reason: string;
    existingConnection?: Observable<any>;
    shouldCleanup: boolean;
  } {
    const connectionKey = `${projectId}-${jobId}`;
    const sessionKey = `${projectId}-${jobId}`;

    this.logger.info('🔍 Validating connection session:', {
      connectionKey,
      sessionKey,
      generationType,
      hasActiveConnection: this.activeConnections.has(connectionKey),
      hasPendingConnection: this.pendingConnections.has(connectionKey),
      hasActiveSubscription: this.activeSubscriptions.has(connectionKey),
      hasUIState: this.connectionUIStates.has(connectionKey)
    });

    // 1. Check for active connections
    if (this.activeConnections.has(connectionKey)) {
      const existingConnection = this.activeConnections.get(connectionKey);
      const uiState = this.connectionUIStates.get(connectionKey);

      // Check if the existing connection is stale (no activity for 5 minutes)
      const isStale = uiState && (Date.now() - uiState.lastActivity) > 300000; // 5 minutes

      if (isStale) {
        this.logger.warn('🧹 Found stale connection - will cleanup and create new:', {
          connectionKey,
          lastActivity: uiState?.lastActivity,
          staleDuration: Date.now() - (uiState?.lastActivity || 0)
        });
        return {
          isValid: false,
          reason: 'stale_connection',
          shouldCleanup: true
        };
      }

      this.logger.info('🚫 Active connection exists - reusing existing connection:', connectionKey);
      return {
        isValid: false,
        reason: 'active_connection_exists',
        existingConnection,
        shouldCleanup: false
      };
    }

    // 2. Check for pending connections
    if (this.pendingConnections.has(connectionKey)) {
      const existingConnection = this.activeConnections.get(connectionKey);

      this.logger.warn('⏳ Pending connection exists - checking for race condition:', connectionKey);

      if (existingConnection) {
        return {
          isValid: false,
          reason: 'pending_connection_with_observable',
          existingConnection,
          shouldCleanup: false
        };
      } else {
        // Pending connection without observable - possible race condition
        this.logger.warn('🧹 Pending connection without observable - cleaning up:', connectionKey);
        return {
          isValid: false,
          reason: 'orphaned_pending_connection',
          shouldCleanup: true
        };
      }
    }

    // 3. Check for orphaned subscriptions or UI states
    const hasOrphanedSubscription = this.activeSubscriptions.has(connectionKey);
    const hasOrphanedUIState = this.connectionUIStates.has(connectionKey);

    if (hasOrphanedSubscription || hasOrphanedUIState) {
      this.logger.warn('🧹 Found orphaned resources - will cleanup before creating new connection:', {
        connectionKey,
        hasOrphanedSubscription,
        hasOrphanedUIState
      });
      return {
        isValid: false,
        reason: 'orphaned_resources',
        shouldCleanup: true
      };
    }

    // 4. Validate generation type consistency for regeneration
    if (generationType === 'code-regen') {
      // For regeneration, ensure no initial-code-gen connections are active
      const initialGenConnections = Array.from(this.activeConnections.keys())
        .filter(key => key.includes('initial-code-gen') || key === connectionKey);

      if (initialGenConnections.length > 0) {
        this.logger.info('🧹 Found initial generation connections during regeneration - will cleanup:', {
          connectionKey,
          initialGenConnections
        });
        return {
          isValid: false,
          reason: 'conflicting_generation_type',
          shouldCleanup: true
        };
      }
    }

    this.logger.info('✅ Connection session validation passed - safe to create new connection:', {
      connectionKey,
      sessionKey,
      generationType
    });

    return {
      isValid: true,
      reason: 'validation_passed',
      shouldCleanup: false
    };
  }

  /**
   * Stop SSE monitoring
   * ENHANCED: Includes connection cleanup to prevent duplicates
   */
  stopMonitoring(): void {
    this.logger.info('🛑 Stopping SSE monitoring');

    this.destroy$.next();
    this.sseService.disconnect();
    this.isConnectedSubject.next(false);

    // ENHANCED: Clean up all active connections
    this.cleanupAllConnections();

    // Reset state
    // this.currentProjectId = null;
    // this.currentJobId = null;
    // this.connectionAttempts = 0;
    // this.fallbackCallback = null;
  }



  /**
   * Detect generation type for SSE optimization
   * ENHANCED: Determines generation type from context and options for since=0 parameter
   */
  private detectGenerationType(
    explicitType?: 'initial-code-gen' | 'code-regen' | 'unknown',
    options?: Partial<SSEOptions>
  ): 'initial-code-gen' | 'code-regen' | 'unknown' {
    // Use explicit type if provided
    if (explicitType) {
      this.logger.info('🎯 Using explicit generation type:', explicitType);
      return explicitType;
    }

    // Check if type is specified in options
    if (options?.generationType) {
      this.logger.info('🎯 Using generation type from options:', options.generationType);
      return options.generationType;
    }

    // HEURISTIC: Detect based on current state and context
    // If this is the first connection for a project, assume initial generation
    const isFirstConnection = !this.currentProjectId;

    // Check if regeneration is active (from stepper state service if available)
    const isRegenerationActive = this.isRegenerationContext();

    let detectedType: 'initial-code-gen' | 'code-regen' | 'unknown';

    if (isRegenerationActive) {
      detectedType = 'code-regen';
    } else{
      detectedType = 'initial-code-gen';
    }

    this.logger.info('🔍 Auto-detected generation type:', {
      detectedType,
      isFirstConnection,
      isRegenerationActive,
      currentProjectId: this.currentProjectId,
      reasoning: isRegenerationActive ? 'Regeneration context detected' :
                 isFirstConnection ? 'First connection for project' : 'Unknown context'
    });

    return detectedType;
  }

  /**
   * Check if we're in a regeneration context
   * ENHANCED: Attempts to detect regeneration state from available services
   */
  private isRegenerationContext(): boolean {
    // This is a simple heuristic - in a real implementation, you might
    // inject a service that tracks regeneration state
    // For now, we'll assume unknown context means potential initial generation
    return false;
  }

  /**
   * Handle potential final event for initial-code-gen events
   * ENHANCED: Manages connection cleanup when isFinal flag is detected
   * @param sseEvent The SSE event to check
   * @param connectionKey The connection key for cleanup
   */
  private handlePotentialFinalEvent(sseEvent: SSEEvent, connectionKey: string): void {
    // Only handle initial-code-gen events
    if (sseEvent.event !== 'initial-code-gen') {
      return;
    }

    try {
      // Parse event data to check for isFinal flag (support both isFinal and is_final)
      const eventData = JSON.parse(sseEvent.data);
      const status = eventData.status;
      const progress = eventData.progress;
      const isFinal = eventData.isFinal === true || eventData.is_final === true;

      // Check if this is a final event
      const isFinalEvent = isFinal && (
        // Both status and progress are FAILED
        (status === 'FAILED' && progress === 'FAILED') ||
        // Progress is DEPLOY and status is COMPLETED
        (progress === 'DEPLOY' && status === 'COMPLETED')
      );

      if (isFinalEvent) {
        this.logger.info('🏁 Final initial-code-gen event detected in Enhanced SSE - preparing cleanup:', {
          eventId: sseEvent.id,
          status,
          progress,
          isFinal,
          connectionKey,
          reason: status === 'FAILED' ? 'Generation failed' : 'Generation completed successfully'
        });

        // Schedule cleanup after event processing
        setTimeout(() => {
          this.logger.info('🧹 Cleaning up initial-code-gen connection due to isFinal flag');
          this.cleanupConnection(connectionKey);
          this.isConnectedSubject.next(false);

          // Reset current job tracking for initial generation
          if (sseEvent.event === 'initial-code-gen') {
            this.currentProjectId = null;
            this.currentJobId = null;
          }
        }, 150); // Slightly longer delay to ensure data processor completes
      }

    } catch (error) {
      // If we can't parse the data, log but don't clean up
      this.logger.debug('📨 Could not parse initial-code-gen event data for isFinal check in Enhanced SSE:', {
        eventId: sseEvent.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * ENHANCED: Clean up a specific connection with comprehensive cleanup
   * Includes subscription disposal, timer cleanup, and UI state reset
   */
  private cleanupConnection(connectionKey: string): void {
    // ENHANCED: Log cleanup lifecycle event
    this.logConnectionLifecycleEvent('cleanup', connectionKey, {
      hasActiveConnection: this.activeConnections.has(connectionKey),
      hasPendingConnection: this.pendingConnections.has(connectionKey),
      hasActiveSubscription: this.activeSubscriptions.has(connectionKey),
      hasTimer: this.connectionTimers.has(connectionKey),
      hasUIState: this.connectionUIStates.has(connectionKey)
    });

    this.logger.info('🧹 Starting comprehensive cleanup for connection:', {
      connectionKey,
      hasActiveConnection: this.activeConnections.has(connectionKey),
      hasPendingConnection: this.pendingConnections.has(connectionKey),
      hasActiveSubscription: this.activeSubscriptions.has(connectionKey),
      hasTimer: this.connectionTimers.has(connectionKey),
      hasUIState: this.connectionUIStates.has(connectionKey)
    });

    try {
      // 1. Clean up active subscription
      if (this.activeSubscriptions.has(connectionKey)) {
        const subscription = this.activeSubscriptions.get(connectionKey);
        if (subscription && !subscription.closed) {
          this.logger.info('🧹 Unsubscribing active subscription:', connectionKey);
          subscription.unsubscribe();
        }
        this.activeSubscriptions.delete(connectionKey);
      }

      // 2. Clear any connection timers
      if (this.connectionTimers.has(connectionKey)) {
        const timerId = this.connectionTimers.get(connectionKey);
        if (timerId) {
          this.logger.info('🧹 Clearing connection timer:', connectionKey);
          clearTimeout(timerId);
        }
        this.connectionTimers.delete(connectionKey);
      }

      // 3. Reset UI state
      if (this.connectionUIStates.has(connectionKey)) {
        this.logger.info('🧹 Resetting UI state for connection:', connectionKey);
        this.connectionUIStates.delete(connectionKey);
      }

      // 4. Clean up active connection observable
      if (this.activeConnections.has(connectionKey)) {
        this.logger.info('🧹 Cleaning up SSE connection observable:', connectionKey);
        this.activeConnections.delete(connectionKey);

        // ENHANCED: Ensure base SSE service is also cleaned up if this was the active connection
        const [projectId, jobId] = connectionKey.split('-');
        if (this.currentProjectId === projectId && this.currentJobId === jobId) {
          this.logger.info('🧹 Cleaning up base SSE service for active connection:', connectionKey);
          if (this.sseService.isConnected()) {
            this.sseService.disconnect();
          }
        }
      }

      // 5. Clean up pending connections
      if (this.pendingConnections.has(connectionKey)) {
        this.logger.info('🧹 Cleaning up pending SSE connection:', connectionKey);
        this.pendingConnections.delete(connectionKey);
      }

      this.logger.info('✅ Comprehensive cleanup completed for connection:', {
        connectionKey,
        remainingActiveConnections: this.activeConnections.size,
        remainingPendingConnections: this.pendingConnections.size,
        remainingSubscriptions: this.activeSubscriptions.size,
        remainingTimers: this.connectionTimers.size,
        remainingUIStates: this.connectionUIStates.size
      });

    } catch (error) {
      this.logger.error('❌ Error during connection cleanup:', {
        connectionKey,
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.name : 'Unknown',
        stack: error instanceof Error ? error.stack : undefined,
        fallbackAction: 'Attempting graceful degradation with detailed tracking'
      });

      // ENHANCED: Graceful degradation with detailed error tracking and recovery
      const fallbackResults = {
        activeConnection: false,
        pendingConnection: false,
        subscription: false,
        timer: false,
        uiState: false
      };

      // Try each cleanup operation individually with error isolation
      try {
        this.activeConnections.delete(connectionKey);
        fallbackResults.activeConnection = true;
      } catch (e) {
        this.logger.error('❌ Failed to cleanup active connection:', {
          connectionKey,
          error: e instanceof Error ? e.message : String(e)
        });
      }

      try {
        this.pendingConnections.delete(connectionKey);
        fallbackResults.pendingConnection = true;
      } catch (e) {
        this.logger.error('❌ Failed to cleanup pending connection:', {
          connectionKey,
          error: e instanceof Error ? e.message : String(e)
        });
      }

      try {
        this.activeSubscriptions.delete(connectionKey);
        fallbackResults.subscription = true;
      } catch (e) {
        this.logger.error('❌ Failed to cleanup subscription:', {
          connectionKey,
          error: e instanceof Error ? e.message : String(e)
        });
      }

      try {
        this.connectionTimers.delete(connectionKey);
        fallbackResults.timer = true;
      } catch (e) {
        this.logger.error('❌ Failed to cleanup timer:', {
          connectionKey,
          error: e instanceof Error ? e.message : String(e)
        });
      }

      try {
        this.connectionUIStates.delete(connectionKey);
        fallbackResults.uiState = true;
      } catch (e) {
        this.logger.error('❌ Failed to cleanup UI state:', {
          connectionKey,
          error: e instanceof Error ? e.message : String(e)
        });
      }

      const successfulCleanups = Object.values(fallbackResults).filter(Boolean).length;
      const totalCleanups = Object.keys(fallbackResults).length;

      this.logger.info('🔧 Fallback cleanup completed with detailed results:', {
        connectionKey,
        fallbackResults,
        successfulCleanups,
        totalCleanups,
        successRate: `${successfulCleanups}/${totalCleanups}`,
        isPartialSuccess: successfulCleanups > 0,
        isCompleteFailure: successfulCleanups === 0
      });

      // ENHANCED: Attempt recovery if partial cleanup succeeded
      if (successfulCleanups > 0 && successfulCleanups < totalCleanups) {
        const failedCleanups = Object.entries(fallbackResults)
          .filter(([_, success]) => !success)
          .map(([type, _]) => type);

        this.logger.warn('⚠️ Partial cleanup detected - may cause resource leaks:', {
          connectionKey,
          failedCleanups
        });

        // ENHANCED: Log error recovery attempt
        this.logErrorRecovery(
          connectionKey,
          'partial_cleanup_failure',
          'resource_leak_warning',
          false,
          { failedCleanups, successfulCleanups, totalCleanups }
        );
      } else if (successfulCleanups === totalCleanups) {
        // ENHANCED: Log successful recovery
        this.logErrorRecovery(
          connectionKey,
          'cleanup_error',
          'fallback_cleanup',
          true,
          { successfulCleanups, totalCleanups }
        );
      }
    }
  }

  /**
   * ENHANCED: Clean up all active connections with comprehensive cleanup
   * Includes all subscriptions, timers, and UI states
   */
  private cleanupAllConnections(): void {
    this.logger.info('🧹 Starting comprehensive cleanup of all SSE connections:', {
      activeConnections: this.activeConnections.size,
      pendingConnections: this.pendingConnections.size,
      activeSubscriptions: this.activeSubscriptions.size,
      connectionTimers: this.connectionTimers.size,
      connectionUIStates: this.connectionUIStates.size
    });

    try {
      // 1. Clean up all active subscriptions
      this.activeSubscriptions.forEach((subscription, connectionKey) => {
        if (subscription && !subscription.closed) {
          this.logger.info('🧹 Unsubscribing active subscription:', connectionKey);
          subscription.unsubscribe();
        }
      });
      this.activeSubscriptions.clear();

      // 2. Clear all connection timers
      this.connectionTimers.forEach((timerId, connectionKey) => {
        if (timerId) {
          this.logger.info('🧹 Clearing connection timer:', connectionKey);
          clearTimeout(timerId);
        }
      });
      this.connectionTimers.clear();

      // 3. Clear all UI states
      this.connectionUIStates.clear();

      // 4. Clear connection maps
      this.activeConnections.clear();
      this.pendingConnections.clear();

      // 5. Disconnect base SSE service if connected
      if (this.sseService.isConnected()) {
        this.logger.info('🧹 Disconnecting base SSE service during comprehensive cleanup');
        this.sseService.disconnect();
      }

      // 6. Reset connection state
      this.isConnectedSubject.next(false);
      this.currentProjectId = null;
      this.currentJobId = null;

      this.logger.info('✅ Comprehensive cleanup of all connections completed successfully');

    } catch (error) {
      this.logger.error('❌ Error during comprehensive cleanup of all connections:', {
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.name : 'Unknown',
        stack: error instanceof Error ? error.stack : undefined,
        fallbackAction: 'Attempting graceful degradation with detailed tracking'
      });

      // ENHANCED: Graceful degradation with detailed error tracking
      const fallbackResults = {
        activeConnections: false,
        pendingConnections: false,
        activeSubscriptions: false,
        connectionTimers: false,
        connectionUIStates: false,
        connectionState: false,
        baseSSEDisconnect: false
      };

      // Try each cleanup operation individually
      try {
        this.activeConnections.clear();
        fallbackResults.activeConnections = true;
      } catch (e) {
        this.logger.error('❌ Failed to clear active connections:', e);
      }

      try {
        this.pendingConnections.clear();
        fallbackResults.pendingConnections = true;
      } catch (e) {
        this.logger.error('❌ Failed to clear pending connections:', e);
      }

      try {
        this.activeSubscriptions.clear();
        fallbackResults.activeSubscriptions = true;
      } catch (e) {
        this.logger.error('❌ Failed to clear active subscriptions:', e);
      }

      try {
        this.connectionTimers.clear();
        fallbackResults.connectionTimers = true;
      } catch (e) {
        this.logger.error('❌ Failed to clear connection timers:', e);
      }

      try {
        this.connectionUIStates.clear();
        fallbackResults.connectionUIStates = true;
      } catch (e) {
        this.logger.error('❌ Failed to clear connection UI states:', e);
      }

      try {
        this.isConnectedSubject.next(false);
        fallbackResults.connectionState = true;
      } catch (e) {
        this.logger.error('❌ Failed to reset connection state:', e);
      }

      try {
        if (this.sseService.isConnected()) {
          this.sseService.disconnect();
        }
        fallbackResults.baseSSEDisconnect = true;
      } catch (e) {
        this.logger.error('❌ Failed to disconnect base SSE service:', e);
      }

      const successfulCleanups = Object.values(fallbackResults).filter(Boolean).length;
      const totalCleanups = Object.keys(fallbackResults).length;

      this.logger.info('🔧 Fallback cleanup of all connections completed with results:', {
        fallbackResults,
        successfulCleanups,
        totalCleanups,
        successRate: `${successfulCleanups}/${totalCleanups}`,
        isPartialSuccess: successfulCleanups > 0,
        isCompleteFailure: successfulCleanups === 0
      });

      // ENHANCED: Log critical failures that may cause memory leaks
      if (successfulCleanups < totalCleanups) {
        const failedOperations = Object.entries(fallbackResults)
          .filter(([_, success]) => !success)
          .map(([operation, _]) => operation);

        this.logger.error('⚠️ Critical cleanup failures detected - potential memory leaks:', {
          failedOperations,
          recommendation: 'Consider application restart if issues persist'
        });
      }
    }
  }

  /**
   * Check if currently monitoring
   */
  isMonitoring(): boolean {
    return this.isConnectedSubject.value && this.currentJobId !== null;
  }

  /**
   * ENHANCED: Log connection lifecycle events with structured data
   * Provides comprehensive logging for debugging and monitoring
   */
  private logConnectionLifecycleEvent(
    event: 'created' | 'connected' | 'error' | 'disconnected' | 'cleanup' | 'validation',
    connectionKey: string,
    details?: any
  ): void {
    const timestamp = new Date().toISOString();
    const connectionStats = this.getConnectionStatistics();

    const logData = {
      event,
      connectionKey,
      timestamp,
      details: details || {},
      connectionStats: {
        activeConnections: connectionStats.activeConnections,
        pendingConnections: connectionStats.pendingConnections,
        activeSubscriptions: connectionStats.activeSubscriptions,
        connectionTimers: connectionStats.connectionTimers,
        connectionUIStates: connectionStats.connectionUIStates
      },
      serviceState: {
        isConnected: this.isConnectedSubject.value,
        isMonitoring: this.isMonitoring(),
        currentProjectId: this.currentProjectId,
        currentJobId: this.currentJobId,
        baseSSEConnected: this.sseService.isConnected()
      }
    };

    switch (event) {
      case 'created':
        this.logger.info('🆕 SSE Connection Created:', logData);
        break;
      case 'connected':
        this.logger.info('🔗 SSE Connection Established:', logData);
        break;
      case 'error':
        this.logger.error('❌ SSE Connection Error:', logData);
        break;
      case 'disconnected':
        this.logger.info('🔌 SSE Connection Disconnected:', logData);
        break;
      case 'cleanup':
        this.logger.info('🧹 SSE Connection Cleanup:', logData);
        break;
      case 'validation':
        this.logger.info('🔍 SSE Connection Validation:', logData);
        break;
      default:
        this.logger.info('📝 SSE Connection Event:', logData);
    }
  }

  /**
   * ENHANCED: Log error recovery attempts with detailed context
   */
  private logErrorRecovery(
    connectionKey: string,
    errorType: string,
    recoveryAction: string,
    success: boolean,
    details?: any
  ): void {
    const logData = {
      connectionKey,
      errorType,
      recoveryAction,
      success,
      timestamp: new Date().toISOString(),
      details: details || {},
      connectionStats: this.getConnectionStatistics()
    };

    if (success) {
      this.logger.info('✅ SSE Error Recovery Successful:', logData);
    } else {
      this.logger.error('❌ SSE Error Recovery Failed:', logData);
    }
  }

  /**
   * ENHANCED: Check if a specific connection exists
   */
  hasActiveConnection(projectId: string, jobId: string): boolean {
    const connectionKey = `${projectId}-${jobId}`;
    return this.activeConnections.has(connectionKey);
  }

  /**
   * CRITICAL FIX: Check if a connection is pending
   */
  hasPendingConnection(projectId: string, jobId: string): boolean {
    const connectionKey = `${projectId}-${jobId}`;
    return this.pendingConnections.has(connectionKey);
  }

  /**
   * CRITICAL FIX: Check if a connection exists or is pending
   */
  hasConnectionOrPending(projectId: string, jobId: string): boolean {
    return this.hasActiveConnection(projectId, jobId) || this.hasPendingConnection(projectId, jobId);
  }

  /**
   * ENHANCED: Get active connection count for debugging
   */
  getActiveConnectionCount(): number {
    return this.activeConnections.size;
  }

  /**
   * ENHANCED: Get comprehensive connection statistics for debugging
   * Includes subscription tracking, timers, and UI states
   */
  getConnectionStatistics(): {
    activeConnections: number;
    pendingConnections: number;
    activeSubscriptions: number;
    connectionTimers: number;
    connectionUIStates: number;
    activeKeys: string[];
    pendingKeys: string[];
    subscriptionKeys: string[];
    timerKeys: string[];
    uiStateKeys: string[];
    totalConnections: number;
    subscriptionStates: { [key: string]: boolean };
    uiStates: { [key: string]: any };
  } {
    const subscriptionStates: { [key: string]: boolean } = {};
    this.activeSubscriptions.forEach((subscription, key) => {
      subscriptionStates[key] = !subscription.closed;
    });

    const uiStates: { [key: string]: any } = {};
    this.connectionUIStates.forEach((state, key) => {
      uiStates[key] = { ...state };
    });

    return {
      activeConnections: this.activeConnections.size,
      pendingConnections: this.pendingConnections.size,
      activeSubscriptions: this.activeSubscriptions.size,
      connectionTimers: this.connectionTimers.size,
      connectionUIStates: this.connectionUIStates.size,
      activeKeys: Array.from(this.activeConnections.keys()),
      pendingKeys: Array.from(this.pendingConnections),
      subscriptionKeys: Array.from(this.activeSubscriptions.keys()),
      timerKeys: Array.from(this.connectionTimers.keys()),
      uiStateKeys: Array.from(this.connectionUIStates.keys()),
      totalConnections: this.activeConnections.size + this.pendingConnections.size,
      subscriptionStates,
      uiStates
    };
  }

  /**
   * Get current monitoring details
   */
  getCurrentMonitoringDetails() {
    return {
      projectId: this.currentProjectId,
      jobId: this.currentJobId,
      isConnected: this.isConnectedSubject.value,
      connectionAttempts: this.connectionAttempts,
      sseConnectionState: this.sseService.getConnectionState(),
      dataProcessorState: this.sseDataProcessor.getCurrentState()
    };
  }

  /**
   * Force fallback to polling
   */
  triggerFallback(reason: string = 'Manual trigger'): void {
    this.logger.warn(`🔄 Triggering fallback to polling: ${reason}`);

    this.stopMonitoring();
    this.fallbackTriggeredSubject.next(reason);

    if (this.fallbackCallback) {
      this.fallbackCallback();
    }
  }

  /**
   * Setup enhanced monitoring of base SSE service
   */
  private setupSSEServiceMonitoring(): void {
    // Monitor connection errors with enhanced logging
    this.sseService.connectionError$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(error => {
      this.logger.warn('🔗 Base SSE service reported connection error');
      this.handleConnectionError(error);
    });

    // Monitor connection state changes with detailed logging
    this.sseService.isConnected$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(isConnected => {
      this.isConnectedSubject.next(isConnected);

      if (isConnected) {
        this.logger.info('✅ SSE connection established successfully for first-cut generation');
        // Reset connection attempts on successful connection
        this.connectionAttempts = 0;
      } else if (this.currentJobId) {
        this.logger.warn('⚠️ SSE connection lost during first-cut generation - will attempt reconnection');
        // Enhanced logging for first-cut generation debugging
        const connectionState = this.sseService.getConnectionState();
        this.logger.debug('🔍 First-cut generation connection state:', {
          ...connectionState,
          jobId: this.currentJobId,
          projectId: this.currentProjectId,
          connectionAttempts: this.connectionAttempts
        });

        // ENHANCED: Prevent duplicate connections during manual reconnection
        if (this.connectionAttempts < this.maxConnectionAttempts) {
          this.logger.info('🔄 Attempting immediate reconnection for first-cut generation');
          setTimeout(() => {
            // CRITICAL FIX: Check for existing connections before manual reconnection
            const currentConnectionKey = this.currentProjectId && this.currentJobId
              ? `${this.currentProjectId}-${this.currentJobId}`
              : null;

            if (!this.isConnectedSubject.value && this.currentJobId && currentConnectionKey) {
              // Ensure no duplicate connections exist
              if (this.activeConnections.has(currentConnectionKey)) {
                this.logger.info('🚫 Skipping manual reconnection - connection already exists:', currentConnectionKey);
                return;
              }

              if (this.sseService.isConnected()) {
                this.logger.info('🚫 Skipping manual reconnection - base SSE already connected');
                return;
              }

              this.logger.info('🔌 Triggering manual reconnection for first-cut generation');
              this.sseService.connect(this.currentJobId, {
                reconnectInterval: 1000, // Faster reconnection for first-cut
                maxReconnectAttempts: 15
              }).pipe(
                takeUntilDestroyed(this.destroyRef)
              ).subscribe();
            }
          }, 1000);
        }
      }
    });

    // Note: connectionState$ is a signal, not an observable, so we don't subscribe to it
    // Connection state debugging is handled through the isConnected$ observable above
  }

  /**
   * Handle connection errors with enhanced recovery logic
   */
  private handleConnectionError(error: any): void {
    const errorDetails = {
      message: error?.message || 'Unknown error',
      type: error?.type || 'connection_error',
      code: error?.code || 'UNKNOWN',
      attempts: this.connectionAttempts,
      timestamp: new Date().toISOString()
    };

    this.logger.error('❌ Enhanced SSE connection error:', errorDetails);

    this.connectionErrorSubject.next(error);
    this.isConnectedSubject.next(false);

    // Enhanced error recovery with better categorization
    if (this.shouldTriggerFallback(error)) {
      const reason = `Connection failed after ${this.connectionAttempts} attempts: ${errorDetails.message}`;
      this.logger.warn('🔄 Triggering fallback due to persistent connection issues');
      this.triggerFallback(reason);
    } else {
      // Log that we're continuing to retry
      this.logger.info(`🔄 Connection error handled, will continue retrying (${this.connectionAttempts}/${this.maxConnectionAttempts})`);
    }
  }

  /**
   * Determine if fallback should be triggered
   */
  private shouldTriggerFallback(error: any): boolean {
    // Trigger fallback if:
    // 1. Max connection attempts reached
    // 2. Specific error types that indicate SSE won't work
    // 3. Network errors that persist

    if (this.connectionAttempts >= this.maxConnectionAttempts) {
      this.logger.warn('🔄 Max connection attempts reached, triggering fallback');
      return true;
    }

    // Check for specific error types that indicate SSE incompatibility
    const errorMessage = error?.message?.toLowerCase() || '';
    const fallbackTriggerErrors = [
      'eventsource not supported',
      'cors error',
      'network error',
      'connection refused',
      'timeout'
    ];

    if (fallbackTriggerErrors.some(trigger => errorMessage.includes(trigger))) {
      this.logger.warn(`🔄 Error type indicates SSE incompatibility: ${errorMessage}`);
      return true;
    }

    return false;
  }

  /**
   * Cache event ID for regeneration checkpointing
   * @param sessionKey Session key (projectId-jobId)
   * @param eventId Event ID to cache
   */
  cacheEventId(sessionKey: string, eventId: string): void {
    const currentMap = this.regenerationSessionEventIds();
    const newMap = new Map(currentMap);
    newMap.set(sessionKey, eventId);
    this.regenerationSessionEventIds.set(newMap);

    this.logger.info('💾 Cached event ID for regeneration session:', {
      sessionKey,
      eventId,
      timestamp: new Date().toISOString()
    });

    // Also cache in the base SSE service
    this.sseService.cacheEventId(eventId, sessionKey);
  }

  /**
   * Get cached event ID for regeneration checkpointing
   * ENHANCED: Uses base SSE service for cross-session compatibility
   * @param sessionKey Session key (projectId-jobId)
   * @returns Cached event ID or null
   */
  getCachedEventId(sessionKey: string): string | null {
    // Use base SSE service for enhanced caching logic (session-specific, latest, global)
    const cachedId = this.sseService.getCachedEventId(sessionKey);

    this.logger.debug('🔍 Retrieved cached event ID from base SSE service:', {
      sessionKey,
      cachedId,
      hasCachedId: !!cachedId,
      note: 'Supports cross-session compatibility (initial-gen -> first-regen)'
    });

    return cachedId;
  }

  /**
   * Clear cached event ID for session
   * @param sessionKey Session key to clear
   */
  clearCachedEventId(sessionKey: string): void {
    const currentMap = this.regenerationSessionEventIds();
    const newMap = new Map(currentMap);
    newMap.delete(sessionKey);
    this.regenerationSessionEventIds.set(newMap);

    this.logger.info('🧹 Cleared cached event ID for session:', sessionKey);

    // Also clear from base SSE service
    this.sseService.clearCachedEventId(sessionKey);
  }

  /**
   * Cleanup resources
   * ENHANCED: Includes connection cleanup to prevent duplicates
   */
  private cleanup(): void {
    this.logger.info('🧹 Cleaning up Enhanced SSE Service');

    // ENHANCED: Clean up all connections first
    this.cleanupAllConnections();

    // Clear all cached event IDs
    this.regenerationSessionEventIds.set(new Map());

    this.destroy$.next();
    this.destroy$.complete();
    this.stopMonitoring();
  }

  /**
   * ENHANCED: Prepare for regeneration by cleaning up existing connections
   * This ensures no duplicate connections during regeneration processes
   * Now includes comprehensive cleanup of all regeneration-related connections
   */
  prepareForRegeneration(projectId: string, jobId: string): void {
    const connectionKey = `${projectId}-${jobId}`;

    this.logger.info('🧹 Preparing for regeneration - comprehensive connection cleanup', {
      connectionKey,
      hasActiveConnection: this.activeConnections.has(connectionKey),
      activeConnectionsCount: this.activeConnections.size,
      pendingConnectionsCount: this.pendingConnections.size,
      activeSubscriptionsCount: this.activeSubscriptions.size,
      connectionTimersCount: this.connectionTimers.size,
      connectionUIStatesCount: this.connectionUIStates.size,
      baseSSEConnected: this.sseService.isConnected(),
      currentlyMonitoring: this.isMonitoring()
    });

    try {
      // ENHANCED: Clean up ALL regeneration-related connections, not just the specific one
      // This prevents any lingering connections from interfering with the new regeneration
      const regenerationConnections = Array.from(this.activeConnections.keys())
        .filter(key => key.includes('code-regen') || key === connectionKey);

      if (regenerationConnections.length > 0) {
        this.logger.info('🧹 Cleaning up regeneration-related connections:', regenerationConnections);
        regenerationConnections.forEach(key => this.cleanupConnection(key));
      }

      // Clean up the specific connection if it exists
      if (this.activeConnections.has(connectionKey)) {
        this.logger.info('🧹 Cleaning up specific connection for regeneration:', connectionKey);
        this.cleanupConnection(connectionKey);
      }

      // ENHANCED: Clean up any orphaned regeneration subscriptions
      const regenerationSubscriptions = Array.from(this.activeSubscriptions.keys())
        .filter(key => key.includes('code-regen') || key === connectionKey);

      if (regenerationSubscriptions.length > 0) {
        this.logger.info('🧹 Cleaning up orphaned regeneration subscriptions:', regenerationSubscriptions);
        regenerationSubscriptions.forEach(key => {
          const subscription = this.activeSubscriptions.get(key);
          if (subscription && !subscription.closed) {
            subscription.unsubscribe();
          }
          this.activeSubscriptions.delete(key);
        });
      }

      // Stop current monitoring if active
      if (this.isMonitoring()) {
        this.logger.info('🧹 Stopping current SSE monitoring for regeneration');
        this.stopMonitoring();
      }

      // Ensure base SSE service is disconnected
      if (this.sseService.isConnected()) {
        this.logger.info('🧹 Disconnecting base SSE service for regeneration preparation');
        this.sseService.disconnect();
      }

      // ENHANCED: Reset connection state and UI states for clean regeneration start
      this.isConnectedSubject.next(false);
      this.resetAllUIStates();

      this.logger.info('✅ Comprehensive regeneration preparation complete:', {
        remainingActiveConnections: this.activeConnections.size,
        remainingPendingConnections: this.pendingConnections.size,
        remainingSubscriptions: this.activeSubscriptions.size,
        remainingTimers: this.connectionTimers.size,
        remainingUIStates: this.connectionUIStates.size,
        baseSSEConnected: this.sseService.isConnected(),
        isMonitoring: this.isMonitoring(),
        uiStatesReset: true
      });

    } catch (error) {
      this.logger.error('❌ Error during regeneration preparation:', {
        connectionKey,
        error: error instanceof Error ? error.message : String(error),
        fallbackAction: 'Attempting comprehensive cleanup'
      });

      // Fallback: comprehensive cleanup of all connections
      try {
        this.cleanupAllConnections();
        this.logger.info('✅ Fallback comprehensive cleanup completed for regeneration');
      } catch (fallbackError) {
        this.logger.error('❌ Fallback regeneration preparation also failed:', fallbackError);
      }
    }
  }

  /**
   * ENHANCED: Log comprehensive connection status for debugging
   * Useful for troubleshooting duplicate connections and cleanup issues
   */
  logConnectionStatus(context: string = 'Debug'): void {
    const status = this.getServiceStatus();
    this.logger.info(`🔍 ${context} - SSE Connection Status:`, {
      context,
      timestamp: new Date().toISOString(),
      enhancedSSE: {
        isMonitoring: status.enhancedSSE.isMonitoring,
        currentProjectId: status.enhancedSSE.currentProjectId,
        currentJobId: status.enhancedSSE.currentJobId,
        connectionAttempts: status.enhancedSSE.connectionAttempts,
        isConnected: status.enhancedSSE.isConnected,
        activeConnectionsCount: status.enhancedSSE.activeConnectionsCount,
        activeConnectionKeys: status.enhancedSSE.activeConnectionKeys,
      },
      baseSSE: {
        isConnected: status.baseSSE.isConnected,
        isConnecting: status.baseSSE.isConnecting,
        connectionId: status.baseSSE.connectionId,
        totalEvents: status.baseSSE.totalEvents,
        reconnectAttempts: status.baseSSE.reconnectAttempts
      }
    });
  }

  /**
   * Test final event handling for debugging
   * ENHANCED: Tests the isFinal flag detection and connection closure
   */
  testFinalEventHandling(isFinal: boolean = true): void {
    this.logger.info('🧪 Testing final event handling:', { isFinal });

    // Create a mock initial-code-gen event with isFinal flag (support both formats)
    const mockEvent = {
      id: `test-${Date.now()}`,
      event: 'initial-code-gen',
      data: JSON.stringify({
        status: 'COMPLETED',
        progress: 'DEPLOY',
        isFinal: isFinal,
        is_final: isFinal, // Support both formats
        message: 'Test final event'
      })
    };

    this.logger.info('🧪 Mock final event created:', mockEvent);

    // Test the final event handling logic
    const connectionKey = 'test-project-test-job';
    this.handlePotentialFinalEvent(mockEvent, connectionKey);

    this.logger.info('🧪 Final event test completed - check logs for cleanup behavior');
  }

  /**
   * Test SSE header functionality for debugging
   * ENHANCED: Tests if last-event-id headers are properly supported
   */
  async testSSEHeaders(): Promise<void> {
    this.logger.info('🧪 Testing SSE header functionality...');

    const polyfillInfo = this.sseService.getPolyfillInfo();
    this.logger.info('📊 Current SSE configuration:', polyfillInfo);

    try {
      const headerTest = await this.sseService.testHeaderFunctionality();
      this.logger.info('🧪 Header test result:', headerTest);

      if (headerTest.success) {
        this.logger.info('✅ SSE headers working correctly - last-event-id will be sent in headers');
      } else {
        this.logger.warn('⚠️ SSE headers not working - will fall back to URL parameters:', headerTest.error);
      }
    } catch (error) {
      this.logger.error('❌ Header test failed:', error);
    }
  }

  /**
   * Get comprehensive service status for debugging
   * ENHANCED: Includes connection management information and header status
   */
  getServiceStatus() {
    const polyfillInfo = this.sseService.getPolyfillInfo();

    return {
      enhancedSSE: {
        isMonitoring: this.isMonitoring(),
        currentProjectId: this.currentProjectId,
        currentJobId: this.currentJobId,
        connectionAttempts: this.connectionAttempts,
        isConnected: this.isConnectedSubject.value,
        // ENHANCED: Connection management status
        activeConnectionsCount: this.activeConnections.size,
        activeConnectionKeys: Array.from(this.activeConnections.keys()),
        pendingConnectionsCount: this.pendingConnections.size,
        pendingConnectionKeys: Array.from(this.pendingConnections),
      },
      baseSSE: this.sseService.getConnectionState(),
      dataProcessor: this.sseDataProcessor.getCurrentState(),
      // ENHANCED: Header support status
      headerSupport: {
        polyfillAvailable: polyfillInfo.polyfillAvailable,
        supportsHeaders: polyfillInfo.supportsHeaders,
        headerMethod: polyfillInfo.headerMethod,
        isSSECompliant: polyfillInfo.isSSECompliant
      }
    };
  }

  /**
   * ENHANCED: Enable retry event filtering for retry operations
   * Filters SSE events to process only those that occur after retry trigger
   * @param retryTimestamp Timestamp when retry was triggered
   */
  enableRetryEventFiltering(retryTimestamp: number): void {
    this.logger.info('🔄 Enabling retry event filtering:', {
      retryTimestamp,
      currentTime: Date.now(),
      filteringEnabled: true
    });

    this.retryTimestamp = retryTimestamp;
    this.isRetryFilteringEnabled = true;

    // Apply filtering to SSE data processor
    this.sseDataProcessor.enableRetryEventFiltering(retryTimestamp);

    this.logger.info('✅ Retry event filtering enabled successfully');
  }

  /**
   * ENHANCED: Disable retry event filtering
   * Removes event filtering to process all SSE events normally
   */
  disableRetryEventFiltering(): void {
    this.logger.info('🔄 Disabling retry event filtering');

    this.retryTimestamp = null;
    this.isRetryFilteringEnabled = false;

    // Remove filtering from SSE data processor
    this.sseDataProcessor.disableRetryEventFiltering();

    this.logger.info('✅ Retry event filtering disabled');
  }

  /**
   * ENHANCED: Check if retry event filtering is currently enabled
   * @returns Whether retry event filtering is active
   */
  isRetryEventFilteringEnabled(): boolean {
    return this.isRetryFilteringEnabled;
  }

  /**
   * ENHANCED: Get current retry timestamp for filtering
   * @returns Current retry timestamp or null if not set
   */
  getRetryTimestamp(): number | null {
    return this.retryTimestamp;
  }

  /**
   * ENHANCED: Get connection error observable
   * UI components can subscribe to this for error handling and state coordination
   */
  getConnectionError(): Observable<any> {
    return this.connectionErrorSubject.asObservable();
  }

  /**
   * ENHANCED: Get fallback triggered observable
   * UI components can subscribe to this to handle fallback scenarios
   */
  getFallbackTriggered(): Observable<string> {
    return this.fallbackTriggeredSubject.asObservable();
  }

  /**
   * ENHANCED: Get UI reset events observable
   * UI components can subscribe to this to reset their states when SSE connections are cleaned up
   */
  getUIResetEvents(): Observable<any> {
    return this.connectionErrorSubject.asObservable().pipe(
      filter((event: any) => event?.type === 'ui_reset_requested')
    );
  }

  /**
   * ENHANCED: Notify UI components to reset their loading states
   * This method can be called by UI components to trigger coordinated state reset
   */
  notifyUIReset(reason: string = 'manual_reset'): void {
    this.logger.info('🔄 Notifying UI components to reset states:', { reason });

    this.connectionErrorSubject.next({
      type: 'ui_reset_requested',
      timestamp: Date.now(),
      reason
    });
  }

  /**
   * ENHANCED: Check if any UI components have active loading states
   * This helps coordinate cleanup timing with UI state management
   */
  hasActiveUILoadingStates(): boolean {
    return this.hasActiveLoadingStates();
  }

  /**
   * ENHANCED: Force UI state synchronization
   * This method ensures all UI states are synchronized with connection states
   */
  synchronizeUIStates(): void {
    this.logger.info('🔄 Synchronizing UI states with connection states');

    try {
      // Check for inconsistent states
      const hasActiveConnections = this.activeConnections.size > 0;
      const hasActiveLoadingStates = this.hasActiveLoadingStates();

      if (!hasActiveConnections && hasActiveLoadingStates) {
        this.logger.warn('⚠️ Inconsistent state detected: no active connections but UI loading states exist');
        this.resetAllUIStates();
      }

      // Notify UI components of current state
      this.notifyUIReset('state_synchronization');

      this.logger.info('✅ UI state synchronization completed');

    } catch (error) {
      this.logger.error('❌ Error during UI state synchronization:', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

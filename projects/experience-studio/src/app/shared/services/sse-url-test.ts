/**
 * Quick test to verify SSE URL building with since=0 parameter
 * This can be run in the browser console to test the implementation
 */

// Mock environment for testing
const mockEnvironment = {
  apiUrl: 'https://api.example.com'
};

// Mock logger for testing
const mockLogger = {
  info: (...args: any[]) => console.log('INFO:', ...args),
  debug: (...args: any[]) => console.log('DEBUG:', ...args),
  warn: (...args: any[]) => console.warn('WARN:', ...args),
  error: (...args: any[]) => console.error('ERROR:', ...args)
};

// Test SSE Options interface
interface TestSSEOptions {
  generationType?: 'initial-code-gen' | 'code-regen' | 'unknown';
  enableSinceParameter?: boolean;
  queryParameters?: Record<string, string>;
}

// Test implementation of shouldAddSinceParameter
function testShouldAddSinceParameter(options?: TestSSEOptions): boolean {
  const sinceEnabled = options?.enableSinceParameter ?? true; // Default to true
  const isInitialGeneration = options?.generationType === 'initial-code-gen';
  const shouldAdd = sinceEnabled && isInitialGeneration;
  
  mockLogger.info('🔍 Testing since=0 parameter addition:', {
    sinceEnabled,
    generationType: options?.generationType,
    isInitialGeneration,
    shouldAdd
  });
  
  return shouldAdd;
}

// Test implementation of buildQueryParameters
function testBuildQueryParameters(options?: TestSSEOptions): string {
  const params: Record<string, string> = {};
  
  mockLogger.info('🔧 Testing query parameters build:', {
    hasOptions: !!options,
    generationType: options?.generationType,
    enableSinceParameter: options?.enableSinceParameter
  });
  
  // Add custom query parameters from options
  if (options?.queryParameters) {
    Object.assign(params, options.queryParameters);
  }
  
  // Add since=0 parameter for initial code generation
  if (testShouldAddSinceParameter(options)) {
    params['since'] = '0';
    mockLogger.info('🚀 Adding since=0 parameter for initial code generation');
  } else {
    mockLogger.info('❌ NOT adding since=0 parameter');
  }
  
  // Convert to query string
  const queryString = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  
  mockLogger.info('🔗 Final query string:', { queryString, params });
  
  return queryString;
}

// Test implementation of buildSSEUrl
function testBuildSSEUrl(jobId: string, options?: TestSSEOptions): string {
  const baseUrl = mockEnvironment.apiUrl;
  let url = `${baseUrl}/stream/project-status/${jobId}`;
  
  const queryParams = testBuildQueryParameters(options);
  if (queryParams) {
    url += `?${queryParams}`;
  }
  
  mockLogger.info('🔗 Built SSE URL:', {
    jobId,
    finalUrl: url,
    generationType: options?.generationType,
    hasQueryParams: !!queryParams
  });
  
  return url;
}

// Run tests
console.log('=== SSE URL Building Tests ===');

console.log('\n1. Testing initial-code-gen with optimization enabled:');
const url1 = testBuildSSEUrl('test-job-123', {
  generationType: 'initial-code-gen',
  enableSinceParameter: true
});
console.log('Expected: URL with ?since=0');
console.log('Actual:', url1);
console.log('✅ Should contain since=0:', url1.includes('since=0'));

console.log('\n2. Testing code-regen (should NOT have since=0):');
const url2 = testBuildSSEUrl('test-job-456', {
  generationType: 'code-regen',
  enableSinceParameter: true
});
console.log('Expected: URL without since=0');
console.log('Actual:', url2);
console.log('✅ Should NOT contain since=0:', !url2.includes('since=0'));

console.log('\n3. Testing initial-code-gen with optimization disabled:');
const url3 = testBuildSSEUrl('test-job-789', {
  generationType: 'initial-code-gen',
  enableSinceParameter: false
});
console.log('Expected: URL without since=0');
console.log('Actual:', url3);
console.log('✅ Should NOT contain since=0:', !url3.includes('since=0'));

console.log('\n4. Testing with custom query parameters:');
const url4 = testBuildSSEUrl('test-job-abc', {
  generationType: 'initial-code-gen',
  enableSinceParameter: true,
  queryParameters: {
    'custom': 'value',
    'test': 'param'
  }
});
console.log('Expected: URL with since=0 and custom params');
console.log('Actual:', url4);
console.log('✅ Should contain since=0:', url4.includes('since=0'));
console.log('✅ Should contain custom=value:', url4.includes('custom=value'));
console.log('✅ Should contain test=param:', url4.includes('test=param'));

console.log('\n5. Testing unknown generation type:');
const url5 = testBuildSSEUrl('test-job-xyz', {
  generationType: 'unknown',
  enableSinceParameter: true
});
console.log('Expected: URL without since=0');
console.log('Actual:', url5);
console.log('✅ Should NOT contain since=0:', !url5.includes('since=0'));

console.log('\n=== Test Summary ===');
const tests = [
  { name: 'Initial gen with optimization', pass: url1.includes('since=0') },
  { name: 'Code regen (no optimization)', pass: !url2.includes('since=0') },
  { name: 'Initial gen with optimization disabled', pass: !url3.includes('since=0') },
  { name: 'Custom params with optimization', pass: url4.includes('since=0') && url4.includes('custom=value') },
  { name: 'Unknown type (no optimization)', pass: !url5.includes('since=0') }
];

tests.forEach(test => {
  console.log(`${test.pass ? '✅' : '❌'} ${test.name}`);
});

const allPassed = tests.every(test => test.pass);
console.log(`\n${allPassed ? '🎉 All tests passed!' : '❌ Some tests failed!'}`);

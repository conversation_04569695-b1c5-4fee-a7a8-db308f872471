import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DesignToken, DesignTokenState, UIDesignAPIResponse } from '../models/design-tokens.model';

@Injectable({
  providedIn: 'root'
})
export class DesignTokenService {
  private designTokenStateSubject = new BehaviorSubject<DesignTokenState>({
    selected: DesignToken.Colors,
    data: {},
    loading: false
  });

  designTokenState$: Observable<DesignTokenState> = this.designTokenStateSubject.asObservable();

  updateTokenData(token: DesignToken, data: any): void {
    const currentState = this.designTokenStateSubject.value;
    this.designTokenStateSubject.next({
      ...currentState,
      data: {
        ...currentState.data,
        [token]: data
      }
    });
  }

  setSelectedToken(token: DesignToken): void {
    this.designTokenStateSubject.next({
      ...this.designTokenStateSubject.value,
      selected: token
    });
  }

  setLoading(loading: boolean): void {
    this.designTokenStateSubject.next({
      ...this.designTokenStateSubject.value,
      loading
    });
  }

  setError(error: string): void {
    this.designTokenStateSubject.next({
      ...this.designTokenStateSubject.value,
      error
    });
  }

  clearError(): void {
    this.designTokenStateSubject.next({
      ...this.designTokenStateSubject.value,
      error: undefined
    });
  }
}

import { Injectable, DestroyRef, inject, signal, computed } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, BehaviorSubject, Subject, EMPTY, of } from 'rxjs';
import { tap, finalize, shareReplay, catchError } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { SSEService, SSEEvent, SSEOptions } from './sse.service';

/**
 * Interface for tracking active SSE connections
 */
export interface ActiveSSEConnection {
  sessionKey: string;
  projectId: string;
  jobId: string;
  observable: Observable<SSEEvent>;
  createdAt: number;
  lastEventAt: number | null;
  eventCount: number;
  isActive: boolean;
  connectionId: string;
}

/**
 * Interface for connection statistics
 */
export interface SSEConnectionStats {
  totalActiveConnections: number;
  totalSessionKeys: string[];
  oldestConnectionAge: number | null;
  newestConnectionAge: number | null;
  totalEventsProcessed: number;
}

/**
 * SSE Connection Deduplication Service
 * 
 * Ensures only one SSE connection exists per regeneration session (projectId-jobId combination)
 * to prevent duplicate event processing and resource waste. Implements Angular 19+ patterns
 * with proper lifecycle management and comprehensive connection tracking.
 * 
 * Features:
 * - Single SSE connection per session guarantee
 * - Connection reuse logic for existing sessions
 * - Automatic connection cleanup on completion/failure
 * - Comprehensive connection state tracking
 * - Angular 19+ patterns: inject(), Signals, takeUntilDestroyed()
 * - Memory-efficient connection management
 * - Detailed logging and debugging support
 */
@Injectable({
  providedIn: 'root'
})
export class SSEConnectionDeduplicationService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('SSEConnectionDeduplicationService');
  private readonly sseService = inject(SSEService);

  // Angular 19+ Signals for reactive state management
  private readonly activeConnections = signal<Map<string, ActiveSSEConnection>>(new Map());
  private readonly connectionStats = signal<SSEConnectionStats>({
    totalActiveConnections: 0,
    totalSessionKeys: [],
    oldestConnectionAge: null,
    newestConnectionAge: null,
    totalEventsProcessed: 0
  });

  // Computed signals for derived state
  public readonly connectionCount = computed(() => this.activeConnections().size);
  public readonly stats = computed(() => this.connectionStats());
  public readonly hasActiveConnections = computed(() => this.connectionCount() > 0);

  // BehaviorSubjects for observable streams
  private readonly connectionCreated$ = new BehaviorSubject<ActiveSSEConnection | null>(null);
  private readonly connectionClosed$ = new BehaviorSubject<string | null>(null);
  private readonly connectionError$ = new Subject<{ sessionKey: string; error: any }>();

  // Connection ID generator
  private connectionIdCounter = 0;

  constructor() {
    this.initializeService();
    this.logger.info('✅ SSE Connection Deduplication Service initialized with Angular 19+ patterns');
  }

  /**
   * Initialize the service with proper lifecycle management
   */
  private initializeService(): void {
    // Set up automatic cleanup on component destruction
    this.destroyRef.onDestroy(() => {
      this.cleanup();
    });

    this.logger.info('🔧 SSE Connection Deduplication Service initialized');
  }

  /**
   * Get or create SSE connection for a regeneration session
   * Ensures only one connection exists per session key
   * @param projectId The project ID
   * @param jobId The job ID
   * @param options SSE connection options
   * @returns Observable of SSE events (existing or new connection)
   */
  public getOrCreateConnection(
    projectId: string,
    jobId: string,
    options?: Partial<SSEOptions>
  ): Observable<SSEEvent> {
    const sessionKey = `${projectId}-${jobId}`;
    
    // Check if connection already exists
    const existingConnection = this.getActiveConnection(sessionKey);
    if (existingConnection) {
      this.logger.info('🔄 Reusing existing SSE connection for session:', {
        sessionKey,
        projectId,
        jobId,
        connectionId: existingConnection.connectionId,
        eventCount: existingConnection.eventCount,
        ageMs: Date.now() - existingConnection.createdAt
      });
      
      return existingConnection.observable;
    }

    // Create new connection
    return this.createNewConnection(projectId, jobId, sessionKey, options);
  }

  /**
   * Check if a connection exists for the given session
   * @param sessionKey The session key to check
   * @returns True if connection exists and is active
   */
  public hasActiveConnection(sessionKey: string): boolean {
    const connection = this.activeConnections().get(sessionKey);
    return !!(connection && connection.isActive);
  }

  /**
   * Get active connection for a session
   * @param sessionKey The session key
   * @returns Active connection or null if not found
   */
  public getActiveConnection(sessionKey: string): ActiveSSEConnection | null {
    return this.activeConnections().get(sessionKey) || null;
  }

  /**
   * Close connection for a specific session
   * @param sessionKey The session key
   * @param reason Reason for closing the connection
   */
  public closeConnection(sessionKey: string, reason: string = 'Manual close'): void {
    const currentConnections = this.activeConnections();
    const connection = currentConnections.get(sessionKey);

    if (!connection) {
      this.logger.warn('⚠️ Cannot close non-existent connection:', { sessionKey, reason });
      return;
    }

    // Update connection state
    const updatedConnection: ActiveSSEConnection = {
      ...connection,
      isActive: false
    };

    const newConnections = new Map(currentConnections);
    newConnections.set(sessionKey, updatedConnection);
    this.activeConnections.set(newConnections);

    this.logger.info('🔌 SSE connection closed:', {
      sessionKey,
      reason,
      connectionId: connection.connectionId,
      eventCount: connection.eventCount,
      durationMs: Date.now() - connection.createdAt
    });

    // Update statistics
    this.updateConnectionStats();

    // Emit connection closed event
    this.connectionClosed$.next(sessionKey);

    // Clean up after a delay to allow for any final processing
    setTimeout(() => {
      this.removeConnection(sessionKey);
    }, 1000);
  }

  /**
   * Close all active connections
   * @param reason Reason for closing all connections
   */
  public closeAllConnections(reason: string = 'Service cleanup'): void {
    const currentConnections = this.activeConnections();
    const sessionKeys = Array.from(currentConnections.keys());

    this.logger.info('🧹 Closing all SSE connections:', {
      count: sessionKeys.length,
      reason,
      sessionKeys
    });

    sessionKeys.forEach(sessionKey => {
      this.closeConnection(sessionKey, reason);
    });
  }

  /**
   * Get observable for connection creation events
   */
  public getConnectionCreated(): Observable<ActiveSSEConnection | null> {
    return this.connectionCreated$.asObservable();
  }

  /**
   * Get observable for connection closure events
   */
  public getConnectionClosed(): Observable<string | null> {
    return this.connectionClosed$.asObservable();
  }

  /**
   * Get observable for connection errors
   */
  public getConnectionErrors(): Observable<{ sessionKey: string; error: any }> {
    return this.connectionError$.asObservable();
  }

  /**
   * Create a new SSE connection for the session
   */
  private createNewConnection(
    projectId: string,
    jobId: string,
    sessionKey: string,
    options?: Partial<SSEOptions>
  ): Observable<SSEEvent> {
    const connectionId = this.generateConnectionId();
    
    this.logger.info('🚀 Creating new SSE connection for session:', {
      sessionKey,
      projectId,
      jobId,
      connectionId,
      options: options ? Object.keys(options) : 'none'
    });

    // Create the SSE connection observable
    const sseObservable = this.sseService.connect(jobId, options).pipe(
      tap((event: SSEEvent) => {
        // Update connection with event information
        this.updateConnectionEventCount(sessionKey, event);
      }),
      catchError((error) => {
        this.logger.error('❌ SSE connection error:', {
          sessionKey,
          connectionId,
          error
        });
        
        this.connectionError$.next({ sessionKey, error });
        
        // Close the connection on error
        this.closeConnection(sessionKey, `Connection error: ${error.message || error}`);
        
        return EMPTY;
      }),
      finalize(() => {
        this.logger.info('🔌 SSE connection finalized:', {
          sessionKey,
          connectionId,
          reason: 'Observable completed'
        });
        
        // Mark connection as inactive
        this.closeConnection(sessionKey, 'Observable completed');
      }),
      shareReplay(1), // Share the connection and replay last event for new subscribers
      takeUntilDestroyed(this.destroyRef)
    );

    // Create connection record
    const connection: ActiveSSEConnection = {
      sessionKey,
      projectId,
      jobId,
      observable: sseObservable,
      createdAt: Date.now(),
      lastEventAt: null,
      eventCount: 0,
      isActive: true,
      connectionId
    };

    // Store the connection
    const currentConnections = this.activeConnections();
    const newConnections = new Map(currentConnections);
    newConnections.set(sessionKey, connection);
    this.activeConnections.set(newConnections);

    // Update statistics
    this.updateConnectionStats();

    // Emit connection created event
    this.connectionCreated$.next(connection);

    this.logger.info('✅ SSE connection created and stored:', {
      sessionKey,
      connectionId,
      totalConnections: newConnections.size
    });

    return sseObservable;
  }

  /**
   * Update connection event count and last event time
   */
  private updateConnectionEventCount(sessionKey: string, event: SSEEvent): void {
    const currentConnections = this.activeConnections();
    const connection = currentConnections.get(sessionKey);

    if (!connection) return;

    const updatedConnection: ActiveSSEConnection = {
      ...connection,
      eventCount: connection.eventCount + 1,
      lastEventAt: Date.now()
    };

    const newConnections = new Map(currentConnections);
    newConnections.set(sessionKey, updatedConnection);
    this.activeConnections.set(newConnections);

    // Update statistics
    this.updateConnectionStats();

    this.logger.debug('📊 Updated connection event count:', {
      sessionKey,
      eventId: event.id,
      eventType: event.event,
      eventCount: updatedConnection.eventCount
    });
  }

  /**
   * Remove connection from tracking
   */
  private removeConnection(sessionKey: string): void {
    const currentConnections = this.activeConnections();
    
    if (!currentConnections.has(sessionKey)) {
      return;
    }

    const newConnections = new Map(currentConnections);
    newConnections.delete(sessionKey);
    this.activeConnections.set(newConnections);

    // Update statistics
    this.updateConnectionStats();

    this.logger.info('🗑️ Connection removed from tracking:', {
      sessionKey,
      remainingConnections: newConnections.size
    });
  }

  /**
   * Update connection statistics
   */
  private updateConnectionStats(): void {
    const connections = this.activeConnections();
    const now = Date.now();
    
    let oldestAge: number | null = null;
    let newestAge: number | null = null;
    let totalEvents = 0;

    for (const connection of connections.values()) {
      const age = now - connection.createdAt;
      
      if (oldestAge === null || age > oldestAge) {
        oldestAge = age;
      }
      
      if (newestAge === null || age < newestAge) {
        newestAge = age;
      }
      
      totalEvents += connection.eventCount;
    }

    this.connectionStats.set({
      totalActiveConnections: connections.size,
      totalSessionKeys: Array.from(connections.keys()),
      oldestConnectionAge: oldestAge,
      newestConnectionAge: newestAge,
      totalEventsProcessed: totalEvents
    });
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `sse-conn-${++this.connectionIdCounter}-${Date.now()}`;
  }

  /**
   * Clean up all resources
   */
  private cleanup(): void {
    this.closeAllConnections('Service destruction');
    
    // Clear all state
    this.activeConnections.set(new Map());
    
    // Complete observables
    this.connectionCreated$.complete();
    this.connectionClosed$.complete();
    this.connectionError$.complete();

    this.logger.info('🧹 SSE Connection Deduplication Service cleaned up');
  }
}

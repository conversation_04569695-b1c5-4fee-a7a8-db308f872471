import { Injectable, signal } from '@angular/core';

/**
 * Image Optimization Service
 * 
 * Provides advanced image loading and optimization features:
 * - WebP format detection and support
 * - Progressive image loading
 * - Image preloading strategies
 * - Performance monitoring for image loads
 * - Responsive image selection
 * 
 * Uses Angular 19+ patterns with Signals for reactive state management
 */
@Injectable({
  providedIn: 'root'
})
export class ImageOptimizationService {
  // WebP support detection
  private readonly webpSupported = signal<boolean | null>(null);
  
  // Image cache for preloaded images
  private readonly imageCache = new Map<string, HTMLImageElement>();
  
  // Loading states for tracked images
  private readonly loadingStates = signal<Map<string, 'loading' | 'loaded' | 'error'>>(new Map());

  constructor() {
    this.detectWebPSupport();
  }

  /**
   * Check if WebP format is supported
   */
  isWebPSupported(): boolean {
    return this.webpSupported() ?? false;
  }

  /**
   * Get optimized image source with format selection
   */
  getOptimizedImageSrc(basePath: string, webpPath?: string): string {
    if (this.isWebPSupported() && webpPath) {
      return webpPath;
    }
    return basePath;
  }

  /**
   * Preload an image and cache it
   */
  async preloadImage(src: string, priority: 'high' | 'low' = 'low'): Promise<HTMLImageElement> {
    // Check if already cached
    if (this.imageCache.has(src)) {
      return this.imageCache.get(src)!;
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      
      // Set loading priority
      if ('fetchPriority' in img) {
        (img as any).fetchPriority = priority;
      }
      
      img.onload = () => {
        this.imageCache.set(src, img);
        this.updateLoadingState(src, 'loaded');
        resolve(img);
      };
      
      img.onerror = () => {
        this.updateLoadingState(src, 'error');
        reject(new Error(`Failed to load image: ${src}`));
      };
      
      this.updateLoadingState(src, 'loading');
      img.src = src;
    });
  }

  /**
   * Preload multiple images with priority handling
   */
  async preloadImages(images: Array<{ src: string; priority?: 'high' | 'low' }>): Promise<void> {
    // Sort by priority - high priority images first
    const sortedImages = images.sort((a, b) => {
      const priorityA = a.priority === 'high' ? 1 : 0;
      const priorityB = b.priority === 'high' ? 1 : 0;
      return priorityB - priorityA;
    });

    // Preload high priority images first, then low priority
    const highPriorityImages = sortedImages.filter(img => img.priority === 'high');
    const lowPriorityImages = sortedImages.filter(img => img.priority !== 'high');

    // Load high priority images immediately
    await Promise.allSettled(
      highPriorityImages.map(img => this.preloadImage(img.src, 'high'))
    );

    // Load low priority images with a slight delay to not block high priority
    setTimeout(() => {
      Promise.allSettled(
        lowPriorityImages.map(img => this.preloadImage(img.src, 'low'))
      );
    }, 100);
  }

  /**
   * Get loading state for an image
   */
  getLoadingState(src: string): 'loading' | 'loaded' | 'error' | 'unknown' {
    return this.loadingStates().get(src) || 'unknown';
  }

  /**
   * Check if image is loaded
   */
  isImageLoaded(src: string): boolean {
    return this.getLoadingState(src) === 'loaded';
  }

  /**
   * Check if image is loading
   */
  isImageLoading(src: string): boolean {
    return this.getLoadingState(src) === 'loading';
  }

  /**
   * Generate responsive image sources for different screen sizes
   */
  getResponsiveImageSources(basePath: string, sizes: number[] = [320, 640, 1024, 1920]): string[] {
    const extension = basePath.split('.').pop();
    const pathWithoutExtension = basePath.replace(`.${extension}`, '');
    
    return sizes.map(size => `${pathWithoutExtension}-${size}w.${extension}`);
  }

  /**
   * Create intersection observer for lazy loading
   */
  createLazyLoadObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver | null {
    if (!('IntersectionObserver' in window)) {
      return null;
    }

    const defaultOptions: IntersectionObserverInit = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    };

    return new IntersectionObserver(callback, defaultOptions);
  }

  /**
   * Clear image cache to free memory
   */
  clearCache(): void {
    this.imageCache.clear();
    this.loadingStates.set(new Map());
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; images: string[] } {
    return {
      size: this.imageCache.size,
      images: Array.from(this.imageCache.keys())
    };
  }

  /**
   * Detect WebP support using modern async approach
   */
  private async detectWebPSupport(): Promise<void> {
    if (this.webpSupported() !== null) {
      return; // Already detected
    }

    try {
      // Use a minimal WebP image for testing
      const webpData = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';
      const img = new Image();
      
      const isSupported = await new Promise<boolean>((resolve) => {
        img.onload = () => resolve(img.width === 1 && img.height === 1);
        img.onerror = () => resolve(false);
        img.src = webpData;
      });

      this.webpSupported.set(isSupported);
    } catch {
      this.webpSupported.set(false);
    }
  }

  /**
   * Update loading state for an image
   */
  private updateLoadingState(src: string, state: 'loading' | 'loaded' | 'error'): void {
    const currentStates = this.loadingStates();
    const newStates = new Map(currentStates);
    newStates.set(src, state);
    this.loadingStates.set(newStates);
  }
}

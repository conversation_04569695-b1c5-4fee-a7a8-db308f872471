import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
 * Interface for code regeneration progress states
 */
export interface CodeRegenerationProgress {
  event: 'code-regen';
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY';
  files?: any[];
  url?: string;
  metadata?: any;
}

/**
 * Interface for UI state management during code regeneration
 */
export interface CodeRegenerationUIState {
  isCodeGenerationLoading: boolean;
  isBuildInProgress: boolean;
  isDeployInProgress: boolean;
  shouldShowLoadingScreen: boolean;
  shouldSwitchToCodeTab: boolean;
  shouldSwitchToPreviewTab: boolean;
  shouldRefreshPreview: boolean;
  latestFiles: any[];
  currentVersion: number;
}

/**
 * Interface for accordion append events with replacement flag
 */
export interface AccordionAppendEvent {
  files: any[];
  version: number;
  replaceAll?: boolean; // Flag to indicate complete replacement vs append
}

/**
 * Service to manage UI states during code regeneration workflow
 * Handles specific SSE event sequences for code-regen events
 * CRITICAL: Processes files exactly like PAGES_GENERATED step for initial generation
 */
@Injectable({
  providedIn: 'root'
})
export class CodeRegenerationUIService {
  private readonly logger = createLogger('CodeRegenerationUIService');

  // State subjects
  private uiStateSubject = new BehaviorSubject<CodeRegenerationUIState>({
    isCodeGenerationLoading: false,
    isBuildInProgress: false,
    isDeployInProgress: false,
    shouldShowLoadingScreen: false,
    shouldSwitchToCodeTab: false,
    shouldSwitchToPreviewTab: false,
    shouldRefreshPreview: false,
    latestFiles: [],
    currentVersion: 1
  });

  // Event subjects for specific actions
  private showLoadingScreenSubject = new Subject<boolean>();
  private appendToAccordionSubject = new Subject<AccordionAppendEvent>();
  private appendToCodeViewerSubject = new Subject<any[]>();
  private switchToCodeTabSubject = new Subject<void>();
  private switchToPreviewTabSubject = new Subject<void>();
  private refreshPreviewSubject = new Subject<string>();

  // Public observables
  public readonly uiState$ = this.uiStateSubject.asObservable();
  public readonly showLoadingScreen$ = this.showLoadingScreenSubject.asObservable();
  public readonly appendToAccordion$ = this.appendToAccordionSubject.asObservable();
  public readonly appendToCodeViewer$ = this.appendToCodeViewerSubject.asObservable();
  public readonly switchToCodeTab$ = this.switchToCodeTabSubject.asObservable();
  public readonly switchToPreviewTab$ = this.switchToPreviewTabSubject.asObservable();
  public readonly refreshPreview$ = this.refreshPreviewSubject.asObservable();

  constructor() {
    this.logger.info('🎨 Code Regeneration UI Service initialized');
  }

  /**
   * Process code regeneration progress events and update UI accordingly
   * CRITICAL: Handles files exactly like PAGES_GENERATED step
   */
  processCodeRegenerationProgress(progress: CodeRegenerationProgress): void {
    this.logger.info('🔄 Processing code regeneration progress:', {
      event: progress.event,
      status: progress.status,
      progress: progress.progress,
      hasFiles: !!progress.files?.length,
      fileCount: progress.files?.length || 0
    });

    const currentState = this.uiStateSubject.value;

    switch (progress.progress) {
      case 'CODE_GENERATION':
        this.handleCodeGenerationProgress(progress, currentState);
        break;
      case 'BUILD':
        this.handleBuildProgress(progress, currentState);
        break;
      case 'DEPLOY':
        this.handleDeployProgress(progress, currentState);
        break;
    }
  }

  /**
   * Handle CODE_GENERATION progress events
   * CRITICAL: This handles the exact same file format as PAGES_GENERATED step
   */
  private handleCodeGenerationProgress(
    progress: CodeRegenerationProgress, 
    currentState: CodeRegenerationUIState
  ): void {
    if (progress.status === 'IN_PROGRESS') {
      this.logger.info('🔄 CODE_GENERATION IN_PROGRESS - showing loading screen on right side');
      
      // Show loading screen on right side
      this.showLoadingScreenSubject.next(true);
      
      // Update state
      this.updateUIState({
        ...currentState,
        isCodeGenerationLoading: true,
        shouldShowLoadingScreen: true
      });

    } else if (progress.status === 'COMPLETED') {
      this.logger.info('✅ CODE_GENERATION COMPLETED - processing files like PAGES_GENERATED step');
      
      // Hide loading screen
      this.showLoadingScreenSubject.next(false);
      
      // Increment version for new regeneration
      const newVersion = currentState.currentVersion + 1;
      
      if (progress.files && progress.files.length > 0) {
        this.logger.info('📁 Processing regeneration files:', {
          fileCount: progress.files.length,
          files: progress.files.map(f => f.fileName || f.name || 'Unknown')
        });

        // CRITICAL: Override previous codes and file models - complete replacement
        // This ensures regeneration completely replaces the previous generation
        this.appendToAccordionSubject.next({
          files: progress.files,
          version: newVersion,
          replaceAll: true // Flag to indicate complete replacement
        });
        
        // CRITICAL: Override all files in code-viewer
        this.appendToCodeViewerSubject.next(progress.files);
      }
      
      // Update state
      this.updateUIState({
        ...currentState,
        isCodeGenerationLoading: false,
        shouldShowLoadingScreen: false,
        latestFiles: progress.files || [],
        currentVersion: newVersion
      });
    }
  }

  /**
   * Handle BUILD progress events
   */
  private handleBuildProgress(
    progress: CodeRegenerationProgress, 
    currentState: CodeRegenerationUIState
  ): void {
    if (progress.status === 'IN_PROGRESS') {
      this.logger.info('🔨 BUILD IN_PROGRESS - staying on code tab with latest changes');
      
      // Switch to code tab to show latest changes
      this.switchToCodeTabSubject.next();
      
      // Update state
      this.updateUIState({
        ...currentState,
        isBuildInProgress: true,
        shouldSwitchToCodeTab: true
      });

    } else if (progress.status === 'COMPLETED') {
      this.logger.info('✅ BUILD COMPLETED - appending any new code to code-viewer/accordion');
      
      if (progress.files && progress.files.length > 0) {
        // Append any new files to code-viewer and accordion
        this.appendToCodeViewerSubject.next(progress.files);
        this.appendToAccordionSubject.next({
          files: progress.files,
          version: currentState.currentVersion,
          replaceAll: false // Append, don't replace
        });
      }
      
      // Update state
      this.updateUIState({
        ...currentState,
        isBuildInProgress: false,
        latestFiles: [...currentState.latestFiles, ...(progress.files || [])]
      });
    }
  }

  /**
   * Handle DEPLOY progress events
   */
  private handleDeployProgress(
    progress: CodeRegenerationProgress, 
    currentState: CodeRegenerationUIState
  ): void {
    if (progress.status === 'COMPLETED') {
      this.logger.info('🚀 DEPLOY COMPLETED - switching to preview and refreshing');
      
      // Switch to preview tab
      this.switchToPreviewTabSubject.next();
      
      // Refresh preview with new URL if available
      if (progress.url) {
        this.refreshPreviewSubject.next(progress.url);
      }
      
      // Update state
      this.updateUIState({
        ...currentState,
        isDeployInProgress: false,
        shouldSwitchToPreviewTab: true,
        shouldRefreshPreview: true
      });
      
      // Reset all loading states after deployment
      this.resetLoadingStates();
    }
  }

  /**
   * Update UI state
   */
  private updateUIState(newState: CodeRegenerationUIState): void {
    this.uiStateSubject.next(newState);
    this.logger.debug('🎨 UI state updated:', newState);
  }

  /**
   * Reset all loading states after regeneration completion
   */
  private resetLoadingStates(): void {
    const currentState = this.uiStateSubject.value;
    this.updateUIState({
      ...currentState,
      isCodeGenerationLoading: false,
      isBuildInProgress: false,
      isDeployInProgress: false,
      shouldShowLoadingScreen: false,
      shouldSwitchToCodeTab: false,
      shouldSwitchToPreviewTab: false,
      shouldRefreshPreview: false
    });
    this.logger.info('🧹 All loading states reset after regeneration completion');
  }

  /**
   * Get current UI state
   */
  getCurrentUIState(): CodeRegenerationUIState {
    return this.uiStateSubject.value;
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.updateUIState({
      isCodeGenerationLoading: false,
      isBuildInProgress: false,
      isDeployInProgress: false,
      shouldShowLoadingScreen: false,
      shouldSwitchToCodeTab: false,
      shouldSwitchToPreviewTab: false,
      shouldRefreshPreview: false,
      latestFiles: [],
      currentVersion: 1
    });
    this.logger.info('🔄 Code Regeneration UI Service reset');
  }
}

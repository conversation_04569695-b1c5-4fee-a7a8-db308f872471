import { Injectable, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Subject, Observable } from 'rxjs';
import { CodeGenerationService } from './code-generation.service';
import { CodeSharingService } from './code-sharing.service';
import { AppStateService } from './app-state.service';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';
import { PollingResponseLoggerService } from './polling-response-logger.service';
import { PollingResponseFileWriterService } from './polling-response-file-writer.service';
import { StepperState } from '../models/stepper-states.enum';
import { NewPollingResponse } from '../models/polling-response.interface';
import { createLogger } from '../utils/logger';

// Define response types for better type safety
export interface CodeGenerationStatusResponse {
  status?: string;
  status_code?: number;
  details?: {
    status: string;
    log: string;
    progress?: string;
    progress_description?: string;
    [key: string]: any; // Allow for additional properties
  };
}

// Define file model interface for consistency
export interface FileModel {
  fileName: string;
  content: string;
}

// Define polling configuration options
export interface PollingOptions {
  initialInterval?: number; // Initial polling interval in ms
  maxInterval?: number; // Maximum polling interval in ms
  backoffFactor?: number; // Factor to increase interval by
  maxRetries?: number; // Maximum number of consecutive error retries
  taskType?: string; // Type of task being polled (for logging)
  startFromStep?: number; // Step to start from when restarting polling
  enableAdaptivePolling?: boolean; // Enable intelligent adaptive polling
  jitterFactor?: number; // Random jitter factor (0-1) to prevent thundering herd
  circuitBreakerThreshold?: number; // Number of failures before circuit breaker opens
}

// Enhanced polling state interface
export interface PollingState {
  isActive: boolean;
  currentInterval: number;
  consecutiveErrors: number;
  consecutiveSuccesses: number;
  isCircuitBreakerOpen: boolean;
  lastSuccessTime: number;
  lastErrorTime: number;
  totalRequests: number;
  totalErrors: number;
  averageResponseTime: number;
  lastRequestStartTime: number;
}

@Injectable({
  providedIn: 'root',
})
export class PollingService {
  private destroy$ = new Subject<void>();
  private destroyRef = inject(DestroyRef);
  private isPollingSubject = new BehaviorSubject<boolean>(false);
  private statusSubject = new BehaviorSubject<string>('idle');
  private progressSubject = new BehaviorSubject<string>('');
  private progressDescriptionSubject = new BehaviorSubject<string>('');
  private logsSubject = new BehaviorSubject<string[]>([]);
  private artifactDataSubject = new BehaviorSubject<any>(null); // New subject for artifact data
  private lastProgressValue: string = '';
  private lastProgressDescription: string = '';
  private lastStatusValue: string = '';
  private lastStatusResponse: any = null; // Store the last status response
  private currentStep: number = 0; // Track the current step for restart
  private artifactDataMap: Map<string, any> = new Map(); // Store artifact data by progress state

  // Public observables for components to subscribe to
  isPolling$ = this.isPollingSubject.asObservable();
  status$ = this.statusSubject.asObservable();
  progress$ = this.progressSubject.asObservable();
  progressDescription$ = this.progressDescriptionSubject.asObservable();
  logs$ = this.logsSubject.asObservable();
  artifactData$ = this.artifactDataSubject.asObservable(); // New observable for artifact data

  // Enhanced default polling configuration
  private static readonly DEFAULT_INITIAL_INTERVAL = 1000; // Start with 1 second for responsiveness
  private static readonly DEFAULT_MAX_INTERVAL = 30000; // Cap at 30 seconds to prevent excessive delays
  private static readonly DEFAULT_BACKOFF_FACTOR = 1.5; // Exponential backoff factor
  private static readonly DEFAULT_MAX_RETRIES = 5; // Increased retries for better resilience
  private static readonly DEFAULT_JITTER_FACTOR = 0.1; // 10% jitter to prevent thundering herd
  private static readonly DEFAULT_CIRCUIT_BREAKER_THRESHOLD = 3; // Circuit breaker after 3 consecutive failures

  // Enhanced polling state
  private projectId: string | null = null;
  private jobId: string | null = null;
  private taskType: string = 'code-generation';
  private currentPollingInterval: number;
  private initialPollingInterval: number;
  private maxPollingInterval: number;
  private backoffFactor: number;
  private retryCount = 0;
  private maxRetries: number;

  // New adaptive polling properties
  private enableAdaptivePolling: boolean = true;
  private jitterFactor: number = PollingService.DEFAULT_JITTER_FACTOR;
  private circuitBreakerThreshold: number = PollingService.DEFAULT_CIRCUIT_BREAKER_THRESHOLD;
  private pollingState: PollingState = {
    isActive: false,
    currentInterval: PollingService.DEFAULT_INITIAL_INTERVAL,
    consecutiveErrors: 0,
    consecutiveSuccesses: 0,
    isCircuitBreakerOpen: false,
    lastSuccessTime: 0,
    lastErrorTime: 0,
    totalRequests: 0,
    totalErrors: 0,
    averageResponseTime: 0,
    lastRequestStartTime: 0
  };
  private pollingTimeoutId: number | null = null;
  private networkStatusListener: (() => void) | null = null;

  // Logger instance
  private logger = createLogger('PollingService');

  constructor(
    private codeGenerationService: CodeGenerationService,
    private codeSharingService: CodeSharingService,
    private appStateService: AppStateService,
    private newPollingResponseProcessor: NewPollingResponseProcessorService,
    private pollingResponseLogger: PollingResponseLoggerService,
    private pollingResponseFileWriter: PollingResponseFileWriterService
  ) {
    // Initialize with enhanced default values
    this.initialPollingInterval = PollingService.DEFAULT_INITIAL_INTERVAL;
    this.maxPollingInterval = PollingService.DEFAULT_MAX_INTERVAL;
    this.backoffFactor = PollingService.DEFAULT_BACKOFF_FACTOR;
    this.maxRetries = PollingService.DEFAULT_MAX_RETRIES;
    this.jitterFactor = PollingService.DEFAULT_JITTER_FACTOR;
    this.circuitBreakerThreshold = PollingService.DEFAULT_CIRCUIT_BREAKER_THRESHOLD;
    this.currentPollingInterval = this.initialPollingInterval;

    // Setup network status monitoring
    this.setupNetworkStatusMonitoring();
  }

  startPolling(projectId: string, jobId: string, options?: PollingOptions): void {
    if (!projectId || !jobId) {
      this.logger.error('Cannot start polling: missing project ID or job ID');
      return;
    }

    // ✅ FIX: Prevent duplicate polling sessions
    if (this.isPollingSubject.value && this.pollingState.isActive) {
      // Check if it's the same project/job combination
      if (this.projectId === projectId && this.jobId === jobId) {
        this.logger.warn('⚠️ Polling already active for the same project/job - ignoring duplicate start request');
        return;
      } else {
        // Different project/job - stop current polling first
        this.logger.info('🔄 Switching to new project/job - stopping current polling first');
        this.stopPolling();
      }
    }

    // Store the IDs
    this.projectId = projectId;
    this.jobId = jobId;

    // Apply custom options if provided with enhanced configuration
    if (options) {
      // Use provided initial interval or default (backward compatible)
      this.initialPollingInterval = options.initialInterval ?? PollingService.DEFAULT_INITIAL_INTERVAL;
      if (options.maxInterval) this.maxPollingInterval = options.maxInterval;
      if (options.backoffFactor) this.backoffFactor = options.backoffFactor;
      if (options.maxRetries) this.maxRetries = options.maxRetries;
      if (options.taskType) this.taskType = options.taskType;

      // New adaptive polling options
      if (options.enableAdaptivePolling !== undefined) this.enableAdaptivePolling = options.enableAdaptivePolling;
      if (options.jitterFactor !== undefined) this.jitterFactor = options.jitterFactor;
      if (options.circuitBreakerThreshold !== undefined) this.circuitBreakerThreshold = options.circuitBreakerThreshold;

      // If startFromStep is provided, use it to resume from a specific step
      if (options.startFromStep !== undefined) {
        this.currentStep = options.startFromStep;
        this.logger.info(`Resuming from step: ${this.currentStep}`);
      } else {
        // Reset current step if not resuming
        this.currentStep = 0;
      }
    } else {
      // If no options provided, still ensure initialInterval is 5 seconds
      this.initialPollingInterval = 3000;
      // Reset current step
      this.currentStep = 0;
    }

    // Reset polling parameters
    this.currentPollingInterval = this.initialPollingInterval;
    this.retryCount = 0;

    // this.logger.info(`\u2705 Started polling for ${this.taskType} status with:`);
    // this.logger.info('Project ID:', this.projectId);
    // this.logger.info('Job ID:', this.jobId);
    // this.logger.info('Initial polling interval:', this.currentPollingInterval, 'ms');
    // this.logger.info('Max polling interval:', this.maxPollingInterval, 'ms');
    // this.logger.info('Backoff factor:', this.backoffFactor);

    // Reset polling state for new session
    this.resetPollingState();

    // Set polling state
    this.isPollingSubject.next(true);
    this.statusSubject.next('polling');
    this.pollingState.isActive = true;

    // Stop any existing polling
    this.destroy$.next();

    // Start polling immediately
    this.checkCodeGenerationStatus();
  }

  private checkCodeGenerationStatus(): void {
    if (!this.projectId || !this.jobId) {
      this.logger.error('Cannot check status: missing project ID or job ID');
      this.stopPolling();
      return;
    }

    // Track request start time for response time calculation
    this.pollingState.lastRequestStartTime = Date.now();

    // this.logger.info(`Making API call to check ${this.taskType} status with:`);
    // this.logger.info('Project ID:', this.projectId);
    // this.logger.info('Job ID:', this.jobId);

    this.codeGenerationService.checkCodeGenerationStatus(this.projectId, this.jobId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: any) => {
          // Reset retry count on successful response
          this.retryCount = 0;

          // this.logger.info('\u2705 Status check response received:', response);
          // this.logger.info('Response type:', typeof response);

        // Handle different response formats
        let statusResponse = response;
        if (typeof response === 'string') {
          try {
            statusResponse = JSON.parse(response);
            // this.logger.info('Parsed status response:', statusResponse);
          } catch (e) {
            this.logger.error('Error parsing status response as JSON:', e);
          }
        }

        // Store the status response for later use
        this.lastStatusResponse = statusResponse;

        // Handle successful polling response
        this.handlePollingSuccess();

        // Log the raw response for analysis and debugging
        this.logRawPollingResponse(statusResponse);

        // Check if this is the new workflow polling response format (direct properties)
        const isNewWorkflow = this.isNewWorkflowPollingResponseFormat(statusResponse);

        if (isNewWorkflow) {
          this.logger.info('🚀 Detected new workflow polling response format, processing with new workflow processor');
          this.pollingResponseLogger.logEnhancedResponse(statusResponse as unknown as NewPollingResponse, {
            detectedFormat: 'new-workflow',
            timestamp: new Date().toISOString(),
            projectId: this.projectId,
            jobId: this.jobId
          });
          this.processNewWorkflowResponse(statusResponse);
          return; // Exit early, new workflow processor handles everything
        }

        // Check if this is the enhanced new polling response format (direct properties)
        if (this.isEnhancedPollingResponseFormat(statusResponse)) {
          this.logger.info('⚡ Detected enhanced polling response format, processing with enhanced processor');
          this.pollingResponseLogger.logEnhancedResponse(statusResponse as unknown as NewPollingResponse, {
            detectedFormat: 'enhanced',
            timestamp: new Date().toISOString(),
            projectId: this.projectId,
            jobId: this.jobId
          });
          this.processEnhancedResponse(statusResponse);
          return; // Exit early, enhanced processor handles everything
        }

        // Check if this is the new polling response format (nested in details)
        const isNewPolling = this.isNewPollingResponseFormat(statusResponse);

        if (isNewPolling) {
          this.logger.info('🔄 Detected new polling response format, processing with new processor');
          this.pollingResponseLogger.logResponse(statusResponse, 'new');
          this.processNewPollingResponse(statusResponse);
          return; // Exit early, new processor handles everything
        }

        // If no format detected, log what we got
        this.logger.debug('No specific format detected, using legacy processing', {
          hasProgress: !!(statusResponse as any)?.progress,
          hasStatus: !!(statusResponse as any)?.status,
          hasDetails: !!statusResponse?.details,
          responseKeys: Object.keys(statusResponse || {})
        });

        // Handle completed status
        if (this.isCompleted(statusResponse)) {
          // this.logger.info(`\u2705 ${this.taskType} completed successfully!`);
          this.statusSubject.next('completed');
          // this.progressSubject.next(`${this.taskType} completed successfully!`);

          // Add a completion log entry
          const timestamp = this.getFormattedTimestamp();
          const completionLogMessage = `${timestamp} - INFO - ${this.taskType} completed successfully!`;

          // Add completion log message if needed
          // this.logsSubject.next([...this.logsSubject.getValue(), completionLogMessage]);

          // this.logger.info('Added completion log message:', completionLogMessage);

          // Update the last status value to COMPLETED
          this.lastStatusValue = 'COMPLETED';
          this.stopPolling();

          // Process the generated code
          // this.logger.info('Processing generated code...');
          const logContent = this.getLogContent(statusResponse);
          // this.logger.info('Log content type:', typeof logContent);
          this.processGeneratedCode(logContent);
        }
        // Handle failed status
        else if (this.isFailed(statusResponse)) {
          this.logger.error(`\u274c ${this.taskType} failed:`, statusResponse);
          this.statusSubject.next('failed');
          // this.progressSubject.next(`${this.taskType} failed. Please try again.`);

          // Add a failure log entry
          const timestamp = this.getFormattedTimestamp();
          const failureLogMessage = `${timestamp} - ERROR - ${this.taskType} failed. Please try again.`;

          // Get current logs and append the failure log
          const currentLogs = this.logsSubject.getValue();
          this.logsSubject.next([...currentLogs, failureLogMessage]);

          this.logger.info('Added failure log message:', failureLogMessage);

          this.stopPolling();
        }
        // Handle in-progress status
        else {
          // Still in progress, continue polling with exponential backoff
          const progressMessage = this.getProgressMessage(statusResponse);
          const progressDescription = this.getProgressDescription(statusResponse);
          // this.logger.info(
          //   `\u23F3 ${this.taskType} in progress... Status:`,
          //   this.getStatusText(statusResponse)
          // );
          // this.logger.info('Progress message:', progressMessage);
          // this.logger.info('Progress description:', progressDescription);

          this.statusSubject.next('in-progress');
          this.progressSubject.next(progressMessage || 'Processing your request...');

          // Get the current status from the response
          const currentStatus = this.getStatusText(statusResponse);
          const statusChanged =
            currentStatus !== this.lastStatusValue && currentStatus.trim() !== '';
          this.lastStatusValue = currentStatus;

          // Add a log entry if the status has changed
          if (statusChanged) {
            const timestamp = this.getFormattedTimestamp();
            const statusLogMessage = `${timestamp} - INFO - Status changed to: ${currentStatus}`;

            // Get current logs and append new log
            const currentLogs = this.logsSubject.getValue();
            this.logsSubject.next([...currentLogs, statusLogMessage]);

            this.logger.info('Added status change log:', statusLogMessage);
          }

          // Process the response but don't add raw responses to logs
          if (statusResponse && statusResponse.details) {
            const timestamp = this.getFormattedTimestamp();
            const details = statusResponse.details as any;

            let debugLogMessage = `${timestamp} - DEBUG - Raw response: `;

            // Add status information
            if (statusResponse.details.status) {
              debugLogMessage += `Status: ${statusResponse.details.status}, `;
            }

            // Add log information if available
            if (statusResponse.details.log) {
              debugLogMessage += `Log: ${statusResponse.details.log}, `;
            }

            // Add progress information if available
            if (details.progress) {
              debugLogMessage += `Progress: ${details.progress}, `;
            }

            // Add progress description if available
            if (details.progress_description) {
              debugLogMessage += `Progress Description: ${details.progress_description}`;
            }

            this.logger.info('Raw response (debug only):', debugLogMessage);

            // Check if the Progress state has changed
            const currentProgress = details.progress || '';
            const progressStateChanged =
              currentProgress !== this.lastProgressValue && currentProgress.trim() !== '';

            // Only add the log field to the logs if status is not COMPLETED and Progress state has changed
            if (statusResponse.details.log &&
                statusResponse.details.status !== 'COMPLETED' &&
                progressStateChanged) {

              // this.logger.info('Progress state changed from:', this.lastProgressValue, 'to:', currentProgress);

              // Try to parse the log field as JSON first (it might be a code object)
              try {
                const logContent = statusResponse.details.log;
                let parsedLog;

                // Check if the log is a string that contains JSON
                if (typeof logContent === 'string' &&
                    (logContent.startsWith('{') || logContent.startsWith('['))) {
                  try {
                    parsedLog = JSON.parse(logContent);
                    // this.logger.info('Successfully parsed log as JSON:', typeof parsedLog);

                    // If it's a code object (key-value pairs where key is path and value is content)
                    if (typeof parsedLog === 'object' && parsedLog !== null) {
                      // Check if this is a layout data log with message and data fields
                      if (parsedLog.message && parsedLog.data) {
                        this.logger.info('Found message and data fields in parsed log - potential layout data');

                        // Create a log entry with the message and data to ensure it's properly displayed
                        // This format will be recognized by the code-window component for layout data
                        const layoutLogMessage = `${timestamp} - INFO - ${JSON.stringify(parsedLog)}`;
                        const currentLogs = this.logsSubject.getValue();
                        this.logsSubject.next([...currentLogs, layoutLogMessage]);
                        // this.logger.info('Added layout data log with message and data fields');

                        // If the data field contains layout information, also add it to the progress description
                        // This helps the code-window component detect layout changes
                        if (parsedLog.message.includes('Layout') ||
                            (typeof parsedLog.data === 'string' && this.isLayoutKey(parsedLog.data))) {
                          // this.logger.info('Detected layout information in data field:', parsedLog.data);
                          // Update progress description to include layout data
                          this.progressDescriptionSubject.next(JSON.stringify(parsedLog));
                        }
                      }
                      // If parsedLog has a data field that might contain code
                      else if (parsedLog.data) {
                        // this.logger.info('Found data field in parsed log:', typeof parsedLog.data);

                        // If data is a string that might be JSON, try to parse it
                        if (typeof parsedLog.data === 'string' &&
                            parsedLog.data.includes('{') &&
                            parsedLog.data.includes('}')) {
                          try {
                            // Try to parse the data string as JSON
                            const parsedData = JSON.parse(parsedLog.data);
                            // this.logger.info('Successfully parsed data field as JSON:', typeof parsedData);

                            // Create another log entry with the parsed data to ensure it's properly displayed
                            const dataLogMessage = `${timestamp} - INFO - Generated code: ${JSON.stringify(parsedData)}`;
                            this.logsSubject.next([...this.logsSubject.getValue(), dataLogMessage]);
                            this.logger.info('Added parsed data field to logs');
                          } catch (innerError) {
                            this.logger.error('Error parsing data field as JSON:', innerError);
                          }
                        } else {
                          // If data is not JSON, add the message part only
                          if (parsedLog.message) {
                            const messageLogEntry = `${timestamp} - INFO - ${parsedLog.message}`;
                            const currentLogs = this.logsSubject.getValue();
                            this.logsSubject.next([...currentLogs, messageLogEntry]);
                            this.logger.info('Added message field to logs');
                          }
                        }
                      } else {
                        // If no data field, add the message part only if available
                        if (parsedLog.message) {
                          const messageLogEntry = `${timestamp} - INFO - ${parsedLog.message}`;
                          const currentLogs = this.logsSubject.getValue();
                          this.logsSubject.next([...currentLogs, messageLogEntry]);
                          this.logger.info('Added message field to logs');
                        }
                      }
                    } else {
                      // It's JSON but not a code object, add as regular log
                      const logMessage = `${timestamp} - INFO - ${logContent}`;
                      const currentLogs = this.logsSubject.getValue();
                      this.logsSubject.next([...currentLogs, logMessage]);
                    }
                  } catch (e) {
                    // Not valid JSON, treat as regular string log
                    const logMessage = `${timestamp} - INFO - ${logContent}`;
                    const currentLogs = this.logsSubject.getValue();
                    this.logsSubject.next([...currentLogs, logMessage]);
                    this.logger.error('Error parsing JSON from log field:', e);
                  }
                } else {
                  // Regular string log
                  const logMessage = `${timestamp} - INFO - ${logContent}`;
                  const currentLogs = this.logsSubject.getValue();
                  this.logsSubject.next([...currentLogs, logMessage]);
                }
              } catch (e) {
                this.logger.error('Error processing log field:', e);
              }
            } else {
              this.logger.info('Not adding log to logs screen - Progress state unchanged or status COMPLETED');
            }

            // Check if there's any code content in the response and add it as a code log
            // Only show logs when there's a change in the Progress state
            if ((details.code || details.files) &&
                statusResponse.details.status !== 'COMPLETED' &&
                progressStateChanged) {
              try {
                const codeContent = details.code || details.files;
                if (codeContent) {
                  // Create a code log entry
                  const codeLogMessage = `${timestamp} - INFO - Generated code: ${JSON.stringify(codeContent)}`;

                  // Add to logs
                  const currentLogs = this.logsSubject.getValue();
                  this.logsSubject.next([...currentLogs, codeLogMessage]);

                  this.logger.info('Added code content to logs');
                }
              } catch (e) {
                this.logger.error('Error adding code content to logs:', e);
              }
            }
          }

          // Get the current Progress value from the response
          const details = statusResponse.details as any;
          const currentProgress = details && details.progress ? details.progress : '';

          // Check if Progress state has changed
          const progressStateChanged =
            currentProgress !== this.lastProgressValue &&
            currentProgress.trim() !== '';

          // Update the last progress value
          this.lastProgressValue = currentProgress;

          // Update the current step based on the progress state
          if (progressStateChanged) {
            // Extract step number from progress state if possible
            try {
              // Common progress states like STEP_1, STEP_2, etc.
              if (currentProgress.includes('STEP_')) {
                const stepMatch = currentProgress.match(/STEP_(\d+)/);
                if (stepMatch && stepMatch[1]) {
                  const stepNumber = parseInt(stepMatch[1], 10);
                  if (!isNaN(stepNumber)) {
                    this.updateCurrentStep(stepNumber);
                  }
                }
              }
            } catch (e) {
              this.logger.error('Error extracting step number from progress state:', e);
            }
          }

          // Check if progress description has changed
          const progressDescriptionChanged =
            progressDescription !== this.lastProgressDescription &&
            progressDescription.trim() !== '';

          // Update progress description if available and changed
          if (
            progressDescription &&
            progressDescription.trim() !== '' &&
            progressDescriptionChanged
          ) {
            // Store the new progress description
            this.lastProgressDescription = progressDescription;

            // Emit the new progress description - this will be shown in the chat window
            this.progressDescriptionSubject.next(progressDescription);

            // this.logger.info('Progress description updated:', progressDescription);

            // We don't want to add progress descriptions to the logs anymore
            // They will only be shown in the chat window
            // this.logger.info('Progress Description Updated (not added to logs):', progressDescription);
          }

          // Add the progress message to logs with timestamp if Progress state changed and status is not COMPLETED
          if (progressStateChanged && statusResponse.details.status !== 'COMPLETED') {
            const timestamp = this.getFormattedTimestamp();
            const logMessage = `${timestamp} - INFO - ${progressMessage}`;

            // Get current logs and append new log
            const currentLogs = this.logsSubject.getValue();
            this.logsSubject.next([...currentLogs, logMessage]);

            // this.logger.info('Added log message:', logMessage);
          }

          // Schedule next poll with exponential backoff
          this.scheduleNextPoll();
        }
        },
        error: (error: any) => {
          // Handle polling error with enhanced error tracking
          this.handlePollingError(error);

          // Increment retry count
          this.retryCount++;

          // If we've exceeded max retries, stop polling
          if (this.retryCount > this.maxRetries) {
            this.logger.error(`Exceeded maximum retries (${this.maxRetries}), stopping polling`);
            this.statusSubject.next('error');

            // Add an error log entry
            const timestamp = this.getFormattedTimestamp();
            const errorLogMessage = `${timestamp} - ERROR - Exceeded maximum retries (${this.maxRetries}). ${this.taskType} process failed.`;

            // Get current logs and append the error log
            const currentLogs = this.logsSubject.getValue();
            this.logsSubject.next([...currentLogs, errorLogMessage]);

            this.stopPolling();
          } else {
            // Otherwise, retry with adaptive backoff
            this.logger.info(`Retry ${this.retryCount}/${this.maxRetries} after error, scheduling next poll with adaptive interval...`);
            this.scheduleNextPoll();
          }
        }
      });
  }

  // Enhanced method to schedule the next poll with adaptive intervals
  private scheduleNextPoll(): void {
    // Clear any existing timeout
    if (this.pollingTimeoutId) {
      clearTimeout(this.pollingTimeoutId);
      this.pollingTimeoutId = null;
    }

    // Check circuit breaker state
    if (this.pollingState.isCircuitBreakerOpen) {
      this.logger.warn('Circuit breaker is open, skipping poll');
      this.scheduleCircuitBreakerRecovery();
      return;
    }

    // Calculate adaptive interval
    const adaptiveInterval = this.calculateAdaptiveInterval();

    this.currentPollingInterval = adaptiveInterval;
    this.pollingState.currentInterval = adaptiveInterval;

    this.logger.debug(`Scheduling next ${this.taskType} poll:`, {
      adaptiveInterval,
      consecutiveErrors: this.pollingState.consecutiveErrors,
      consecutiveSuccesses: this.pollingState.consecutiveSuccesses
    });

    // Schedule next poll with enhanced timeout management
    this.pollingTimeoutId = window.setTimeout(() => {
      if (this.isPollingSubject.value && this.pollingState.isActive) {
        this.checkCodeGenerationStatus();
      }
    }, this.currentPollingInterval);
  }

  // Helper method to check if status is completed
  private isCompleted(response: any): boolean {
    // Check if progress is DEPLOYED
    if (response.details && response.details.progress === StepperState.DEPLOYED) {
      this.logger.info('Detected DEPLOYED state in progress field');
      return true;
    }

    // Check if status is COMPLETED or success
    if (response.details &&
        (response.details.status === 'COMPLETED' || response.details.status === 'success')) {
      this.logger.info('Detected COMPLETED or success state in status field');
      return true;
    }

    // Check if status_code is 200 and status is COMPLETED
    if ('status_code' in response &&
        response.status_code === 200 &&
        response.details &&
        response.details.status === 'COMPLETED') {
      this.logger.info('Detected COMPLETED state with status_code 200');
      return true;
    }

    return false;
  }

  // Helper method to check if status is failed
  private isFailed(response: any): boolean {
    // this.logger.info('Checking if response indicates failure:', response);

    // Check for BUILD_FAILED in progress field
    if (response.details && response.details.progress === StepperState.BUILD_FAILED) {
      // this.logger.info('Detected BUILD_FAILED in progress field');
      return true;
    }

    // Check for FAILED in progress field
    if (response.details && response.details.progress === 'FAILED') {
      // this.logger.info('Detected FAILED in progress field');
      return true;
    }

    // Check for status_code 200 but with FAILED status
    if ('status_code' in response &&
        response.status_code === 200 &&
        response.details &&
        response.details.status === 'FAILED') {
      // this.logger.info('Detected status_code 200 with FAILED status');
      return true;
    }

    // Check for error message in progress_description
    if (response.details &&
        response.details.progress_description &&
        (response.details.progress_description.includes('error') ||
         response.details.progress_description.includes('failed') ||
         response.details.progress_description.includes('Error') ||
         response.details.progress_description.includes('Failed') ||
         response.details.progress_description.includes('Unexpected'))) {
      // this.logger.info('Detected error keywords in progress_description:', response.details.progress_description);
      return true;
    }

    // Check for status fields
    const statusFailed = (
      response.status === 'failed' ||
      response.status === 'error' ||
      (response.details && response.details.status === 'FAILED')
    );

    if (statusFailed) {
      // this.logger.info('Detected failed status in status fields');
    }

    return statusFailed;
  }

  // Helper method to get log content from response
  private getLogContent(response: any): string {
    if (response.details && response.details.log) {
      // Check if log is a string that might contain JSON with data field
      if (typeof response.details.log === 'string' &&
          response.details.log.includes('{') &&
          response.details.log.includes('}')) {

        try {
          // Try to parse the log as JSON
          const parsedLog = JSON.parse(response.details.log);

          // Check if this is a layout data log with message and data fields
          if (parsedLog.message && parsedLog.data) {
            // this.logger.info('Found message and data fields in log JSON - potential layout data');

            // If the data field contains layout information
            if (parsedLog.message.includes('Layout') ||
                (typeof parsedLog.data === 'string' && this.isLayoutKey(parsedLog.data))) {
              // this.logger.info('Detected layout information in data field:', parsedLog.data);
              // Return the entire parsed log as a string to preserve the message and data structure
              return JSON.stringify(parsedLog);
            }
          }

          // If it has a data field that contains the files, return that
          if (parsedLog.data) {
            // this.logger.info('Found data field in log JSON:', typeof parsedLog.data);

            // If data is a string that might be JSON, try to parse it
            if (typeof parsedLog.data === 'string' &&
                parsedLog.data.includes('{') &&
                parsedLog.data.includes('}')) {
              try {
                // Try to parse the data string as JSON
                const parsedData = JSON.parse(parsedLog.data);
                // this.logger.info('Successfully parsed data field as JSON:', typeof parsedData);
                return JSON.stringify(parsedData);
              } catch (innerError) {
                // this.logger.error('Error parsing data field as JSON:', innerError);
                // If parsing fails, return the data as is
                return parsedLog.data;
              }
            }

            // Return the data field as a string if it's not already
            return typeof parsedLog.data === 'string'
              ? parsedLog.data
              : JSON.stringify(parsedLog.data);
          }
        } catch (e) {
          // this.logger.error('Error parsing log as JSON:', e);
          // If parsing fails, return the original log
          return response.details.log;
        }
      }

      // Return the original log if we couldn't extract data
      return response.details.log;
    }
    return '';
  }

  // Helper method to get progress message from response
  private getProgressMessage(response: any): string {
    if ('status_code' in response && response.status_code === 200 && response.details) {
      // First check for progress field
      if (response.details.progress && response.details.status !== 'COMPLETED') {

        const progress = response.details.progress;

        // Check if the progress matches one of our StepperState values
        if (Object.values(StepperState).includes(progress as StepperState)) {
          return progress;
        }

        // If not a direct match, try to map common API response states to our StepperState values
        if (progress.includes('SEED_PROJECT')) {
          return StepperState.SEED_PROJECT_INITIALIZED;
        }
        else if (progress.includes('FILE_QUEUE')) {
          return StepperState.FILE_QUEUE;}
        else if (progress.includes('DESIGN_SYSTEM')) {
          return StepperState.DESIGN_SYSTEM_MAPPED;
        } else if (progress.includes('COMPONENT')) {
          return StepperState.COMPONENTS_CREATED;
        } else if (progress.includes('LAYOUT')) {
          return StepperState.LAYOUT_ANALYZED;
        } else if (progress.includes('PAGE')) {
          return StepperState.PAGES_GENERATED;
        } else if (progress.includes('BUILD_START')) {
          return StepperState.BUILD_STARTED;
        } else if (progress.includes('BUILD_SUCCESS')) {
          return StepperState.BUILD_SUCCEEDED;
        } else if (progress.includes('BUILD_FAIL')) {
          return StepperState.BUILD_FAILED;
        }else if (progress.includes('FILE') || progress.includes('FILES')) {
          return StepperState.FILES_GENERATED;}
        else if (progress.includes('DEPLOY')) {
          return StepperState.DEPLOYED;
        }

        // If no mapping found, return the original progress value
        return progress;

      }
      // Then check for log field as fallback
      else if (response.details.log && response.details.status !== 'COMPLETED') {
        return response.details.log;
      }

      else if (response.details.status === 'COMPLETED') {
        return StepperState.DEPLOYED;
      }


    }
    return 'Processing your request...';
  }

  /**
   * Gets the progress description from the response
   * @param response The API response
   * @returns The progress description or empty string
   */
  /**
   * Gets the progress description from the response
   * @param response The API response
   * @returns The progress description or empty string
   */
  private getProgressDescription(response: any): string {
    if ('status_code' in response && response.status_code === 200 && response.details) {
      // First check for progress_description field
      if (response.details.progress_description && response.details.status !== 'COMPLETED') {
        // Parse the progress description to extract content before <mlo_artifact> tag
        const progressDescription = response.details.progress_description;

        // Check if the progress description contains an <mlo_artifact> tag
        if (typeof progressDescription === 'string' && progressDescription.includes('<mlo_artifact>')) {
          // Extract the content before the <mlo_artifact> tag
          const parts = progressDescription.split('<mlo_artifact>');

          // Store the artifact data for later use
          if (parts.length > 1) {
            try {
              // Extract the content inside the <mlo_artifact> tag
              const artifactContent = parts[1].split('</mlo_artifact>')[0];

              // Try to parse the artifact content as JSON
              const artifactData = JSON.parse(artifactContent);

              // Store the artifact data in a new BehaviorSubject
              this.storeArtifactData(artifactData);

              // this.logger.info('Extracted artifact data:', artifactData);
            } catch (e) {
              // this.logger.error('Error parsing artifact data:', e);
            }
          }

          // Return only the content before the <mlo_artifact> tag
          return parts[0];
        }

        return progressDescription;
      }

      // If no progress_description but we have a log field and status is FAILED,
      // try to extract error message from the log field
      if (!response.details.progress_description &&
          response.details.log &&
          (response.details.status === 'FAILED' || response.details.progress === 'FAILED')) {
        try {
          // Check if log is a JSON string with a message field
          const logContent = response.details.log;
          if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
            const parsedLog = JSON.parse(logContent);
            if (parsedLog.message) {
              return parsedLog.message;
            }
          }
        } catch (e) {
          // this.logger.error('Error extracting error message from log field:', e);
        }

        // If we couldn't extract a message, return the log field as is
        return response.details.log;
      }
    }
    return '';
  }

  // Helper method to get status text from response
  private getStatusText(response: any): string {
    if ('status_code' in response && response.details && response.details.status) {
      return response.details.status;
    }
    return response.status || 'unknown';
  }

  /**
   * Stop polling and reset polling state
   */
  stopPolling(): void {
    this.logger.info('PollingService: Stopping polling');
    this.isPollingSubject.next(false);
    this.pollingState.isActive = false;

    // Clear any pending timeouts
    if (this.pollingTimeoutId) {
      clearTimeout(this.pollingTimeoutId);
      this.pollingTimeoutId = null;
    }

    this.destroy$.next();
  }

  /**
   * Reset processed states for new job/project
   * Delegates to the new polling response processor service
   */
  resetProcessedStates(): void {
    this.logger.info('🧹 PollingService: Resetting processed states for new job');
    this.newPollingResponseProcessor.resetProcessedStates();
  }

  /**
   * Clean up all subscriptions and resources
   * Call this when the service needs to be completely reset
   */
  cleanup(): void {
    // this.logger.info('PollingService: Cleaning up all resources');
    this.stopPolling();
    this.resetLogs();

    // Remove network status listeners
    if (this.networkStatusListener && typeof window !== 'undefined') {
      window.removeEventListener('online', this.networkStatusListener);
      window.removeEventListener('offline', this.networkStatusListener);
      this.networkStatusListener = null;
    }

    // Clear any pending timeouts
    if (this.pollingTimeoutId) {
      clearTimeout(this.pollingTimeoutId);
      this.pollingTimeoutId = null;
    }
  }

  /**
   * Reset logs and progress state
   * This is useful when navigating away from a component that uses logs
   */
  resetLogs(): void {
    // this.logger.info('PollingService: Resetting logs and progress state');
    // Reset logs
    this.logsSubject.next([]);

    // Reset progress and status
    this.progressSubject.next('');
    this.progressDescriptionSubject.next('');
    this.statusSubject.next('idle');

    // Reset artifact data
    this.artifactDataSubject.next(null);
    this.artifactDataMap.clear();

    // Reset internal state
    this.lastProgressValue = '';
    this.lastProgressDescription = '';
    this.lastStatusValue = '';
    this.lastStatusResponse = null;
  }

  private processGeneratedCode(code: string): void {
    // this.logger.info('Processing generated code...');
    // this.logger.info('Code type:', typeof code);
    if (typeof code === 'string') {
      // this.logger.info('Code preview:', code.substring(0, 100) + '...');
    }

    // Check if this is an edit response (stringified array of files)
    if (this.isEditResponse(code)) {
      this.processEditResponse(code);
      return;
    }

    // First, store the raw code in case we need it later
    this.codeSharingService.setGeneratedCode(code);
    try {
      // Try to parse the code as a JSON string
      let parsedCode;
      if (typeof code === 'string') {
        // Check if this is a layout data JSON with message and data fields
        if (code.includes('"message"') && code.includes('"data"')) {
          try {
            const layoutData = JSON.parse(code);
            // this.logger.info('Detected layout data in code:', layoutData);

            if (layoutData.message && layoutData.data) {
              // If it's layout data, add it to the progress description
              // This helps the code-window component detect layout changes
              this.progressDescriptionSubject.next(code);

              // For layout data, we don't need to process it as code
              // Just return early

              return;
            }
          } catch (e) {
            // this.logger.error('Error parsing layout data:', e);
          }
        }

        parsedCode = JSON.parse(code);

      } else {
        parsedCode = code;
      }

      // Check if the parsed code is an array (list of files with fileName and content)
      if (Array.isArray(parsedCode)) {

        // This is the format expected by the code-viewer component
        // Each item should have fileName and content properties
        const fileArray = parsedCode
          .map(item => {
            if (typeof item === 'object' && item !== null) {
              return {
                fileName: item.fileName || item.path || 'unknown.txt',
                content: item.content || '',
              };
            }
            return null;
          })
          .filter(item => item !== null);


        this.codeSharingService.setGeneratedCode(fileArray);
      } else if (typeof parsedCode === 'object' && parsedCode !== null) {
        // Handle object format (key-value pairs where key is file path and value is content)


        // Convert to array format expected by code-viewer
        if (parsedCode.files && Array.isArray(parsedCode.files)) {




          // Convert files array to expected format

          const fileArray = parsedCode.files

            .map((item: any) => {

              if (typeof item === 'object' && item !== null) {

                return {

                  fileName: item.fileName || item.path || item.name || 'unknown.txt',

                  content: item.content || '',

                };

              }

              return null;

            })

            .filter((item: any) => item !== null);





          this.codeSharingService.setGeneratedCode(fileArray);

        } else {

          // Regular object with file paths as keys

          // Convert to array format expected by code-viewer

          const fileArray = Object.entries(parsedCode).map(([path, content]) => ({

            fileName: path,

            content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),

          }));





          this.codeSharingService.setGeneratedCode(fileArray);

        }
      } else {
        // Fallback for unexpected format
        // this.logger.error('\u274c Unexpected code format:', typeof parsedCode);
        const fallbackCode = {
          fileName: 'src/app/app.component.ts',
          content: typeof code === 'string' ? code : JSON.stringify(code, null, 2),
        };
        this.codeSharingService.setGeneratedCode([fallbackCode]);
      }
    } catch (e) {
      // this.logger.error('\u274c Error processing code:', e);
      // Fallback to single file format
      const fallbackCode = {
        fileName: 'src/app/app.component.ts',
        content: typeof code === 'string' ? code : JSON.stringify(code, null, 2),
      };
      this.codeSharingService.setGeneratedCode([fallbackCode]);
    }

    // Update app state with code generated flag
    this.appStateService.updateProjectState({ codeGenerated: true });

  }

  /**
   * Check if the response is an edit response (stringified array of files)
   */
  private isEditResponse(code: string): boolean {
    try {
      // Try to parse the code as JSON
      const parsed = JSON.parse(code);

      // Check if it's an array of objects with fileName and content properties
      if (Array.isArray(parsed) && parsed.length > 0) {
        const firstItem = parsed[0];
        return (
          typeof firstItem === 'object' &&
          firstItem !== null &&
          ('fileName' in firstItem || 'filename' in firstItem) &&
          'content' in firstItem
        );
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /**
   * Process edit response and update existing files or add new ones
   */
  private processEditResponse(code: string): void {
    try {
      this.logger.info('🔄 Processing edit response...');

      // Parse the stringified JSON array
      const editedFiles = JSON.parse(code);

      if (!Array.isArray(editedFiles)) {
        this.logger.error('❌ Edit response is not an array:', editedFiles);
        return;
      }

      this.logger.info(`📝 Processing ${editedFiles.length} edited files`);

      // Get current files from code sharing service
      const currentCode = this.codeSharingService.getGeneratedCode();
      let currentFiles: any[] = [];

      // Convert current code to array format if needed
      if (Array.isArray(currentCode)) {
        currentFiles = [...currentCode];
      } else if (typeof currentCode === 'object' && currentCode !== null) {
        // Convert object format to array
        currentFiles = Object.entries(currentCode).map(([path, content]) => ({
          fileName: path,
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2)
        }));
      }

      // Process each edited file
      for (const editedFile of editedFiles) {
        const fileName = editedFile.fileName || editedFile.filename;
        const content = editedFile.content;

        if (!fileName || content === undefined) {
          this.logger.warn('⚠️ Skipping invalid file:', editedFile);
          continue;
        }

        // Check if file already exists
        const existingFileIndex = currentFiles.findIndex(
          file => (file.fileName || file.name) === fileName
        );

        if (existingFileIndex !== -1) {
          // Update existing file
          currentFiles[existingFileIndex].content = content;
          if (currentFiles[existingFileIndex].fileName) {
            currentFiles[existingFileIndex].fileName = fileName;
          } else {
            currentFiles[existingFileIndex].name = fileName;
          }
          this.logger.info(`✏️ Updated existing file: ${fileName}`);
        } else {
          // Add new file
          currentFiles.push({
            fileName: fileName,
            content: content
          });
          this.logger.info(`➕ Added new file: ${fileName}`);
        }
      }

      // Update the code sharing service with the merged files
      this.codeSharingService.setGeneratedCode(currentFiles);

      this.logger.info('✅ Edit response processed successfully');

    } catch (error) {
      this.logger.error('❌ Error processing edit response:', error);

      // Fallback: treat as regular code if edit processing fails
      this.codeSharingService.setGeneratedCode(code);
    }
  }

  /**
   * Returns a formatted timestamp string in the format HH:MM:SS.mmm
   */
  private getFormattedTimestamp(): string {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
    return `${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  /**
   * Get the last response received from the API
   * @returns The last response or null if no response has been received
   */
  // getLastResponse(): CodeGenerationStatusResponse | null {
  //   return this.lastResponse;
  // }

  /**
   * Check if a string is a valid layout key
   * @param key The key to check
   * @returns True if the key is a valid layout key
   */
  private isLayoutKey(key: string): boolean {
    // Common layout keys used in the application
    const layoutKeys = [
      'HB', 'HBF', 'HLSB', 'HLSBF', 'HBRS', 'HBRSF', 'HLSBRS', 'HLSBRSF'
    ];

    // Check if the key is in the list of valid layout keys
    return layoutKeys.includes(key);
  }

  /**
   * Get the last progress description
   * @returns The last progress description
   */
  getLastProgressDescription(): string {
    return this.lastProgressDescription;
  }

  /**
   * Get the last status response
   * @returns The last status response or null if no response has been received
   */
  getLastStatusResponse(): any {
    return this.lastStatusResponse;
  }

  /**
   * Extract error message from the last status response
   * Used for retry mechanism when BUILD status is FAILED
   * @returns The error message or empty string if not found
   */
  getErrorMessageFromLastResponse(): string {
    if (!this.lastStatusResponse) {
      return '';
    }

    try {
      // Check if response has details with log field
      if (this.lastStatusResponse.details && this.lastStatusResponse.details.log) {
        const logContent = this.lastStatusResponse.details.log;

        // If log is a JSON string, try to parse it and extract message
        if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
          try {
            const parsedLog = JSON.parse(logContent);
            if (parsedLog.message) {
              return parsedLog.message;
            }
            // If no message field, return the entire parsed log as string
            return JSON.stringify(parsedLog);
          } catch (e) {
            // If parsing fails, return the log content as is
            return logContent;
          }
        }

        // If log is not JSON, return it directly
        return logContent;
      }

      // Check if response has progress_description with error info
      if (this.lastStatusResponse.details && this.lastStatusResponse.details.progress_description) {
        const progressDesc = this.lastStatusResponse.details.progress_description;
        if (progressDesc.toLowerCase().includes('error') ||
            progressDesc.toLowerCase().includes('failed') ||
            progressDesc.toLowerCase().includes('unexpected')) {
          return progressDesc;
        }
      }

      // For new workflow format, check direct log field
      if (this.lastStatusResponse.log) {
        const logContent = this.lastStatusResponse.log;
        if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
          try {
            const parsedLog = JSON.parse(logContent);
            if (parsedLog.message) {
              return parsedLog.message;
            }
            return JSON.stringify(parsedLog);
          } catch (e) {
            return logContent;
          }
        }
        return logContent;
      }

      // Fallback: return a generic error message
      return 'Build process failed. Please check the logs for more details.';

    } catch (error) {
      this.logger.error('Error extracting error message from last response:', error);
      return 'An error occurred during the build process.';
    }
  }

  /**
   * Get the current step
   * @returns The current step number
   */
  getCurrentStep(): number {
    return this.currentStep;
  }

  /**
   * Store artifact data from the progress description
   * @param artifactData The artifact data to store
   */
  private storeArtifactData(artifactData: any): void {
    if (!artifactData) return;

    // Store the artifact data in the subject
    this.artifactDataSubject.next(artifactData);

    // If the data has a specific key for the current progress state, store it in the map
    if (this.lastProgressValue) {
      this.artifactDataMap.set(this.lastProgressValue, artifactData);
      // this.logger.info(`Stored artifact data for progress state: ${this.lastProgressValue}`);
    }
  }

  /**
   * Get the latest artifact data
   * @returns The latest artifact data
   */
  getLatestArtifactData(): any {
    return this.artifactDataSubject.getValue();
  }

  /**
   * Get artifact data for a specific progress state
   * @param progressState The progress state to get artifact data for
   * @returns The artifact data for the specified progress state, or null if not found
   */
  getArtifactDataForState(progressState: string): any {
    return this.artifactDataMap.get(progressState) || null;
  }

  /**
   * Get all stored artifact data
   * @returns A map of all stored artifact data by progress state
   */
  getAllArtifactData(): Map<string, any> {
    return new Map(this.artifactDataMap);
  }

  /**
   * Reset logs but keep the current step
   * This is used when retrying after an error
   */
  resetLogsButKeepStep(): void {
    // Store the current step
    const currentStepValue = this.currentStep;

    // Reset logs
    this.logsSubject.next([]);

    // Add a log entry indicating retry
    const timestamp = this.getFormattedTimestamp();
    this.logsSubject.next([`${timestamp} - INFO - Retrying operation from step ${currentStepValue}`]);

    // Keep the current step
    this.currentStep = currentStepValue;
  }

  /**
   * Update the current step
   * This is called when a new progress state is detected
   * @param step The new step number
   */
  updateCurrentStep(step: number): void {
    // Only update if the new step is greater than the current step
    if (step > this.currentStep) {
      this.currentStep = step;
      // this.logger.info(`Updated current step to: ${this.currentStep}`);
    }
  }

  /**
   * Check if the response is in the new polling response format
   */
  private isNewPollingResponseFormat(response: any): boolean {
    // Check if response has the new format structure
    if (response && response.details) {
      const details = response.details;
      const isValid = (
        typeof details.progress === 'string' &&
        typeof details.status === 'string' &&
        typeof details.log === 'string' &&
        typeof details.progress_description === 'string' &&
        Array.isArray(details.history) &&
        Array.isArray(details.metadata)
      );

      this.logger.debug('New polling format check (nested)', {
        hasResponse: !!response,
        hasDetails: !!response.details,
        hasProgress: typeof details?.progress === 'string',
        hasStatus: typeof details?.status === 'string',
        hasLog: typeof details?.log === 'string',
        hasProgressDescription: typeof details?.progress_description === 'string',
        hasHistory: Array.isArray(details?.history),
        hasMetadata: Array.isArray(details?.metadata),
        isValid: isValid,
        progressValue: details?.progress,
        statusValue: details?.status,
        metadataCount: details?.metadata?.length || 0,
        metadataTypes: details?.metadata?.map((m: any) => m.type) || []
      });

      return isValid;
    }

    this.logger.debug('New polling format check - no details property');
    return false;
  }

  /**
   * Check if the response is in the new workflow polling response format
   * This matches the test.json structure and handles prev_metadata
   */
  private isNewWorkflowPollingResponseFormat(response: any): boolean {
    const isValid = (
      response &&
      typeof response.progress === 'string' &&
      typeof response.status === 'string' &&
      typeof response.log === 'string' &&
      typeof response.progress_description === 'string' &&
      Array.isArray(response.history) &&
      Array.isArray(response.metadata)
      // REMOVED: Overly strict check for prev_metadata or history.length > 0
      // This was preventing valid responses from being processed
    );

    this.logger.debug('New workflow format check', {
      hasResponse: !!response,
      hasProgress: typeof response?.progress === 'string',
      hasStatus: typeof response?.status === 'string',
      hasLog: typeof response?.log === 'string',
      hasProgressDescription: typeof response?.progress_description === 'string',
      hasHistory: Array.isArray(response?.history),
      hasMetadata: Array.isArray(response?.metadata),
      isValid: isValid
    });

    return isValid;
  }

  /**
   * Check if the response is in the enhanced new polling response format
   * This matches the exact structure specified in requirements
   */
  private isEnhancedPollingResponseFormat(response: any): boolean {
    // Check if response has the enhanced format structure (direct properties, not nested in details)
    return (
      response &&
      typeof response.progress === 'string' &&
      typeof response.status === 'string' &&
      typeof response.log === 'string' &&
      typeof response.progress_description === 'string' &&
      Array.isArray(response.history) &&
      Array.isArray(response.metadata)
    );
  }

  /**
   * Check if polling should stop based on progress and status
   * Stop when:
   * 1. DEPLOYED/DEPLOY progress with COMPLETED or FAILED status (final deployment state)
   * 2. BUILD progress with FAILED status (build failure)
   * 3. DEPLOY progress with FAILED status (deployment failure)
   */
  private shouldStopPolling(progress: string, status: string): boolean {
    const progressUpper = progress.toUpperCase();
    const statusUpper = status.toUpperCase();

    // Stop polling for BUILD failures - this allows retry mechanism to work
    if (progressUpper === 'BUILD' && statusUpper === 'FAILED') {
      this.logger.info(`🛑 Stopping polling for BUILD FAILED state`);
      return true;
    }

    // Stop polling for DEPLOY failures - this allows retry mechanism to work
    if (progressUpper === 'DEPLOY' && statusUpper === 'FAILED') {
      this.logger.info(`🛑 Stopping polling for DEPLOY FAILED state`);
      return true;
    }

    // Stop polling only when we reach the final deployment state
    const isFinalState = (
      progressUpper === 'DEPLOYED' ||
      progressUpper === 'DEPLOY' ||
      progressUpper === 'COMPLETED'
    );

    const isTerminalStatus = (statusUpper === 'COMPLETED' || statusUpper === 'FAILED');

    const shouldStop = isFinalState && isTerminalStatus;

    // this.logger.info(`🔍 Checking if should stop polling:`, {
    //   progress: progress,
    //   status: status,
    //   isFinalState: isFinalState,
    //   isTerminalStatus: isTerminalStatus,
    //   shouldStop: shouldStop
    // });

    return shouldStop;
  }

  /**
   * Process response using the new workflow polling response format
   * This handles the test.json structure and metadata retrieval requirements
   */
  private processNewWorkflowResponse(response: any): void {
    try {
      const workflowResponse = response;

      this.logger.info('🚀 Processing new workflow polling response:', workflowResponse);

      // Use the new workflow processor to handle the response
      // This processor handles state-based log filtering internally
      this.newPollingResponseProcessor.processNewWorkflowResponse(workflowResponse);

      // Update local subjects for backward compatibility
      this.progressSubject.next(workflowResponse.progress);
      this.progressDescriptionSubject.next(workflowResponse.progress_description);
      this.statusSubject.next(workflowResponse.status);

      // DON'T add logs here - let the new processor handle log filtering based on state changes
      // The new processor will only emit logs when progress state actually changes

      // Handle completion or failure - only stop when we reach final deployment state
      if (this.shouldStopPolling(workflowResponse.progress, workflowResponse.status)) {
        if (workflowResponse.status === 'COMPLETED') {
          // this.logger.info(`🎉 ✅ ${this.taskType} completed successfully and deployed!`);
          this.statusSubject.next('completed');
        } else if (workflowResponse.status === 'FAILED') {
          // this.logger.error(`💥 ❌ ${this.taskType} failed during deployment!`);
          this.statusSubject.next('failed');
        }
        this.stopPolling();
      } else {
        // Continue polling - not at final deployment state yet
        // this.logger.info(`⏳ Continuing polling - Progress: ${workflowResponse.progress}, Status: ${workflowResponse.status}`);
        this.scheduleNextPoll();
      }
    } catch (error) {
      // this.logger.error('Error processing new workflow response:', error);
      this.scheduleNextPoll();
    }
  }

  /**
   * Process response using the enhanced new polling response format
   * This handles the exact structure specified in requirements
   */
  private processEnhancedResponse(response: any): void {
    try {
      const enhancedResponse: NewPollingResponse = response;

      // this.logger.info('Processing enhanced polling response:', enhancedResponse);

      // Use the enhanced processor to handle the response
      // This processor handles state-based log filtering internally
      this.newPollingResponseProcessor.processEnhancedResponse(enhancedResponse);

      // Update local subjects for backward compatibility
      this.progressSubject.next(enhancedResponse.progress);
      this.progressDescriptionSubject.next(enhancedResponse.progress_description);
      this.statusSubject.next(enhancedResponse.status);

      // DON'T add logs here - let the new processor handle log filtering based on state changes
      // The new processor will only emit logs when progress state actually changes

      // Handle completion or failure - only stop when we reach final deployment state
      if (this.shouldStopPolling(enhancedResponse.progress, enhancedResponse.status)) {
        if (enhancedResponse.status === 'COMPLETED') {
          // this.logger.info(`🎉 ✅ ${this.taskType} completed successfully and deployed!`);
          this.statusSubject.next('completed');
        } else if (enhancedResponse.status === 'FAILED') {
          // this.logger.error(`💥 ❌ ${this.taskType} failed during deployment!`);
          this.statusSubject.next('failed');
        }
        this.stopPolling();
      } else {
        // Continue polling - not at final deployment state yet
        // this.logger.info(`⏳ Continuing polling - Progress: ${enhancedResponse.progress}, Status: ${enhancedResponse.status}`);
        this.scheduleNextPoll();
      }

    } catch (error) {
      // this.logger.error('Error processing enhanced polling response:', error);
      // Fall back to old processing logic
      this.processOldPollingResponse(response);
    }
  }

  /**
   * Process response using the new polling response format
   */
  private processNewPollingResponse(response: any): void {
    try {
      const newResponse: NewPollingResponse = response.details;

      // Use the new processor to handle the response
      this.newPollingResponseProcessor.processResponse(newResponse);

      // Update local subjects for backward compatibility
      this.progressSubject.next(newResponse.progress);
      this.progressDescriptionSubject.next(newResponse.progress_description);
      this.statusSubject.next(newResponse.status);

      // Add log message with timestamp
      const timestamp = this.getFormattedTimestamp();
      const logMessage = `${timestamp} - INFO - ${newResponse.log}`;
      const currentLogs = this.logsSubject.getValue();
      this.logsSubject.next([...currentLogs, logMessage]);

      // Handle completion or failure - only stop when we reach final deployment state
      if (this.shouldStopPolling(newResponse.progress, newResponse.status)) {
        if (newResponse.status === 'COMPLETED') {
          // this.logger.info(`🎉 ✅ ${this.taskType} completed successfully and deployed!`);
          this.statusSubject.next('completed');
        } else if (newResponse.status === 'FAILED') {
          // this.logger.error(`💥 ❌ ${this.taskType} failed during deployment!`);
          this.statusSubject.next('failed');
        }
        this.stopPolling();
      } else {
        // Continue polling - not at final deployment state yet
        // this.logger.info(`⏳ Continuing polling - Progress: ${newResponse.progress}, Status: ${newResponse.status}`);
        this.scheduleNextPoll();
      }

    } catch (error) {
      // this.logger.error('Error processing new polling response:', error);
      // Fall back to old processing logic
      this.processOldPollingResponse(response);
    }
  }

  /**
   * Process response using the old polling response format (fallback)
   */
  private processOldPollingResponse(response: any): void {
    // Log legacy response
    this.pollingResponseLogger.logResponse(response, 'legacy');

    // Continue with the existing logic for backward compatibility
    // This is the original processing logic that was in checkCodeGenerationStatus

    // Handle completed status
    if (this.isCompleted(response)) {
      // Continue with existing completion logic...
      this.scheduleNextPoll();
    } else {
      // Continue with existing in-progress logic...
      this.scheduleNextPoll();
    }
  }

  /**
   * Log raw polling response for analysis and debugging
   */
  private logRawPollingResponse(response: any): void {
    try {
      // Determine response type
      let responseType: 'enhanced' | 'new' | 'legacy' = 'legacy';

      if (this.isEnhancedPollingResponseFormat(response)) {
        responseType = 'enhanced';
      } else if (this.isNewPollingResponseFormat(response)) {
        responseType = 'new';
      }

      // Log with additional context
      this.pollingResponseLogger.logResponse(response, responseType);

      // Write to codebase files using the file writer service
      this.pollingResponseFileWriter.writePollingResponse(response, responseType, {
        projectId: this.projectId,
        jobId: this.jobId,
        taskType: this.taskType,
        timestamp: new Date().toISOString()
      });

      // this..info(`Raw polling response logged and written to codebase files (${responseType}):`, {
      //   hasProgress: !!response?.progress,
      //   hasStatus: !!response?.status,
      //   hasDetails: !!response?.details,
      //   responseSize: JSON.stringify(response).length
      // });
    } catch (error) {
      this.logger.error('Error logging raw polling response:', error);
    }
  }

  /**
   * Export all logged polling responses to a file
   */
  exportPollingResponses(filename?: string): void {
    this.pollingResponseLogger.exportToFile(filename);
  }

  /**
   * Export polling responses in human-readable format
   */
  exportPollingResponsesReadable(filename?: string): void {
    this.pollingResponseLogger.exportToReadableFile(filename);
  }

  /**
   * Get current polling session statistics
   */
  getPollingSessionStats(): any {
    // return this.pollingResponseLogger.getSessionStats();
  }

  /**
   * Clear all logged polling responses
   */
  clearPollingResponseLogs(): void {
    // this.pollingResponseLogger.clearResponses();
  }

  // ============================================================================
  // POLLING RESPONSE FILE WRITER METHODS
  // ============================================================================

  /**
   * Enable writing polling responses to codebase files
   */
  enableCodebaseFileWriting(): void {
    this.pollingResponseFileWriter.enableFileWriting();
    // this.logger.info('Codebase file writing enabled for polling responses');
  }

  /**
   * Disable writing polling responses to codebase files
   */
  disableCodebaseFileWriting(): void {
    this.pollingResponseFileWriter.disableFileWriting();
    // this.logger.info('Codebase file writing disabled for polling responses');
  }

  /**
   * Check if codebase file writing is enabled
   */
  isCodebaseFileWritingEnabled(): Observable<boolean> {
    return this.pollingResponseFileWriter.isFileWritingEnabled();
  }

  /**
   * Get file writer session statistics
   */
  getFileWriterSessionStats(): Observable<any> {
    return this.pollingResponseFileWriter.getSessionStats();
  }

  /**
   * Export responses for a specific state from file writer
   */
  exportStateResponsesFromFileWriter(state: string): void {
    this.pollingResponseFileWriter.exportStateResponses(state);
  }

  /**
   * Export complete session data from file writer
   */
  exportCompleteSessionData(): void {
    this.pollingResponseFileWriter.exportSessionData();
  }

  /**
   * Clear file writer session and reset
   */
  clearFileWriterSession(): void {
    this.pollingResponseFileWriter.clearSession();
  }

  // ============================================================================
  // ADAPTIVE POLLING OPTIMIZATION METHODS
  // ============================================================================

  /**
   * Reset polling state for new polling session
   */
  private resetPollingState(): void {
    this.pollingState.isActive = false;
    this.pollingState.currentInterval = this.initialPollingInterval;
    this.pollingState.consecutiveErrors = 0;
    this.pollingState.consecutiveSuccesses = 0;
    this.pollingState.isCircuitBreakerOpen = false;
    this.pollingState.lastSuccessTime = 0;
    this.pollingState.lastErrorTime = 0;
    this.pollingState.totalRequests = 0;
    this.pollingState.totalErrors = 0;
    this.pollingState.averageResponseTime = 0;
    this.pollingState.lastRequestStartTime = 0;
  }



  /**
   * Setup network status monitoring for better error handling
   */
  private setupNetworkStatusMonitoring(): void {
    if (typeof navigator === 'undefined' || !('onLine' in navigator)) {
      return;
    }

    this.networkStatusListener = () => {
      const isOnline = navigator.onLine;
      this.logger.info(`Network status changed: ${isOnline ? 'online' : 'offline'}`);

      if (isOnline && this.pollingState.isCircuitBreakerOpen) {
        this.logger.info('Network back online, attempting to recover from circuit breaker');
        // Reset circuit breaker when network comes back online
        this.pollingState.consecutiveErrors = Math.max(0, this.pollingState.consecutiveErrors - 1);
      }
    };

    window.addEventListener('online', this.networkStatusListener);
    window.addEventListener('offline', this.networkStatusListener);
  }

  /**
   * Calculate adaptive polling interval based on success/error patterns
   */
  private calculateAdaptiveInterval(): number {
    if (!this.enableAdaptivePolling) {
      return this.initialPollingInterval;
    }

    let interval = this.initialPollingInterval;

    // Apply exponential backoff for consecutive errors
    if (this.pollingState.consecutiveErrors > 0) {
      interval = Math.min(
        this.initialPollingInterval * Math.pow(this.backoffFactor, this.pollingState.consecutiveErrors),
        this.maxPollingInterval
      );
    }
    // Reduce interval for consecutive successes (more responsive)
    else if (this.pollingState.consecutiveSuccesses > 2) {
      const reductionFactor = Math.max(0.5, 1 - (this.pollingState.consecutiveSuccesses - 2) * 0.1);
      interval = Math.max(this.initialPollingInterval * reductionFactor, 500); // Minimum 500ms
    }
    // Special case: If we've had many successes, we might be in a stable state
    // Gradually increase interval to reduce unnecessary polling
    else if (this.pollingState.consecutiveSuccesses > 10) {
      const stabilityFactor = Math.min(2.0, 1 + (this.pollingState.consecutiveSuccesses - 10) * 0.05);
      interval = Math.min(this.initialPollingInterval * stabilityFactor, this.maxPollingInterval);
    }

    // Apply jitter to prevent thundering herd
    const jitter = (Math.random() - 0.5) * 2 * this.jitterFactor * interval;
    interval = Math.max(interval + jitter, 100); // Minimum 100ms

    return Math.round(interval);
  }



  /**
   * Handle successful polling response
   */
  private handlePollingSuccess(): void {
    const now = Date.now();
    const responseTime = now - this.pollingState.lastRequestStartTime;

    // Update response time average
    if (this.pollingState.averageResponseTime === 0) {
      this.pollingState.averageResponseTime = responseTime;
    } else {
      // Exponential moving average
      this.pollingState.averageResponseTime =
        (this.pollingState.averageResponseTime * 0.8) + (responseTime * 0.2);
    }

    this.pollingState.consecutiveErrors = 0;
    this.pollingState.consecutiveSuccesses++;
    this.pollingState.lastSuccessTime = now;
    this.pollingState.totalRequests++;

    // Close circuit breaker on success
    if (this.pollingState.isCircuitBreakerOpen) {
      this.pollingState.isCircuitBreakerOpen = false;
      this.logger.info('Circuit breaker closed after successful response');
    }

    // Log performance metrics periodically
    if (this.pollingState.totalRequests % 10 === 0) {
      const metrics = this.getPollingMetrics();
      this.logger.debug('Polling performance metrics:', metrics);
    }
  }

  /**
   * Handle polling error with enhanced error tracking
   */
  private handlePollingError(error: any): void {
    this.pollingState.consecutiveSuccesses = 0;
    this.pollingState.consecutiveErrors++;
    this.pollingState.lastErrorTime = Date.now();
    this.pollingState.totalRequests++;
    this.pollingState.totalErrors++;

    this.logger.error(`Polling error (${this.pollingState.consecutiveErrors}/${this.circuitBreakerThreshold}):`, error);

    // Open circuit breaker if threshold reached
    if (this.pollingState.consecutiveErrors >= this.circuitBreakerThreshold) {
      this.pollingState.isCircuitBreakerOpen = true;
      this.logger.warn('Circuit breaker opened due to consecutive failures');
    }
  }

  /**
   * Schedule circuit breaker recovery attempt
   */
  private scheduleCircuitBreakerRecovery(): void {
    const recoveryInterval = this.maxPollingInterval * 2; // Wait longer before retry

    this.logger.info(`Scheduling circuit breaker recovery in ${recoveryInterval}ms`);

    this.pollingTimeoutId = window.setTimeout(() => {
      if (this.isPollingSubject.value && this.pollingState.isActive) {
        this.logger.info('Attempting circuit breaker recovery');
        this.pollingState.isCircuitBreakerOpen = false;
        this.pollingState.consecutiveErrors = Math.max(0, this.pollingState.consecutiveErrors - 1);
        this.checkCodeGenerationStatus();
      }
    }, recoveryInterval);
  }

  /**
   * Get current polling statistics for monitoring
   */
  getPollingStats(): PollingState {
    return { ...this.pollingState };
  }

  /**
   * Get polling performance metrics
   */
  getPollingMetrics() {
    const now = Date.now();
    const uptime = this.pollingState.lastSuccessTime > 0 ? now - this.pollingState.lastSuccessTime : 0;
    const errorRate = this.pollingState.totalRequests > 0 ?
      (this.pollingState.totalErrors / this.pollingState.totalRequests) * 100 : 0;

    return {
      uptime,
      errorRate: Math.round(errorRate * 100) / 100,
      totalRequests: this.pollingState.totalRequests,
      totalErrors: this.pollingState.totalErrors,
      consecutiveSuccesses: this.pollingState.consecutiveSuccesses,
      consecutiveErrors: this.pollingState.consecutiveErrors,
      currentInterval: this.pollingState.currentInterval,
      isCircuitBreakerOpen: this.pollingState.isCircuitBreakerOpen,
      averageResponseTime: Math.round(this.pollingState.averageResponseTime),
      adaptivePollingEnabled: this.enableAdaptivePolling
    };
  }

  /**
   * Force reset circuit breaker (for manual recovery)
   */
  resetCircuitBreaker(): void {
    this.pollingState.isCircuitBreakerOpen = false;
    this.pollingState.consecutiveErrors = 0;
    this.logger.info('Circuit breaker manually reset');
  }

  /**
   * Temporarily disable adaptive polling
   */
  disableAdaptivePolling(): void {
    this.enableAdaptivePolling = false;
    this.logger.info('Adaptive polling disabled');
  }

  /**
   * Re-enable adaptive polling
   */
  enableAdaptivePollingFeature(): void {
    this.enableAdaptivePolling = true;
    this.logger.info('Adaptive polling enabled');
  }

}


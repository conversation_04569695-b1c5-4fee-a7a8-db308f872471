import { Injectable, inject, DestroyRef } from '@angular/core';
import { Observable, BehaviorSubject, of, from } from 'rxjs';
import { map, tap, catchError, switchMap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { createLogger } from '../../utils/logger';

export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  expiresAt: number;
  lastAccessed: number;
  accessCount: number;
  contentHash?: string;
  metadata?: Record<string, any>;
}

export interface CacheConfig {
  maxMemorySize: number;
  maxPersistentSize: number;
  defaultTTL: number;
  enablePersistence: boolean;
  enableCompression: boolean;
  storagePrefix: string;
}

export interface CacheMetrics {
  memoryHits: number;
  memoryMisses: number;
  persistentHits: number;
  persistentMisses: number;
  totalRequests: number;
  memorySize: number;
  persistentSize: number;
  hitRatio: number;
  averageAccessTime: number;
}

@Injectable({
  providedIn: 'root'
})
export class MultiLevelCacheService {
  private readonly logger = createLogger('MultiLevelCacheService');
  private readonly destroyRef = inject(DestroyRef);

  // Memory cache (L1)
  private memoryCache = new Map<string, CacheEntry>();
  
  // Persistent cache keys (L2) - actual data stored in sessionStorage/localStorage
  private persistentKeys = new Set<string>();
  
  // Cache metrics
  private metrics$ = new BehaviorSubject<CacheMetrics>({
    memoryHits: 0,
    memoryMisses: 0,
    persistentHits: 0,
    persistentMisses: 0,
    totalRequests: 0,
    memorySize: 0,
    persistentSize: 0,
    hitRatio: 0,
    averageAccessTime: 0
  });

  private readonly config: CacheConfig = {
    maxMemorySize: 100, // Max entries in memory
    maxPersistentSize: 500, // Max entries in persistent storage
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    enablePersistence: true,
    enableCompression: false, // Can be enabled for large content
    storagePrefix: 'es_cache_'
  };

  constructor() {
    this.initializeCache();
    this.setupCleanupInterval();
  }

  /**
   * Get data from cache with fallback to source
   */
  get<T>(key: string, source?: () => Observable<T>, ttl?: number): Observable<T> {
    const startTime = performance.now();
    this.incrementTotalRequests();

    // Try memory cache first (L1)
    const memoryEntry = this.getFromMemory<T>(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      this.updateAccessStats(memoryEntry);
      this.incrementMemoryHits();
      this.recordAccessTime(performance.now() - startTime);
      return of(memoryEntry.data);
    }

    // Try persistent cache (L2)
    const persistentEntry = this.getFromPersistent<T>(key);
    if (persistentEntry && !this.isExpired(persistentEntry)) {
      // Promote to memory cache
      this.setInMemory(key, persistentEntry);
      this.updateAccessStats(persistentEntry);
      this.incrementPersistentHits();
      this.recordAccessTime(performance.now() - startTime);
      return of(persistentEntry.data);
    }

    // Cache miss - use source if provided
    if (source) {
      this.incrementMemoryMisses();
      return source().pipe(
        tap(data => {
          this.set(key, data, ttl);
          this.recordAccessTime(performance.now() - startTime);
        }),
        catchError(error => {
          this.logger.error(`Cache source error for key ${key}:`, error);
          throw error;
        })
      );
    }

    // No source provided and cache miss
    this.incrementMemoryMisses();
    this.recordAccessTime(performance.now() - startTime);
    throw new Error(`Cache miss for key: ${key} and no source provided`);
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      key,
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + (ttl || this.config.defaultTTL),
      lastAccessed: Date.now(),
      accessCount: 1,
      contentHash: this.generateContentHash(data)
    };

    // Set in memory cache
    this.setInMemory(key, entry);

    // Set in persistent cache if enabled
    if (this.config.enablePersistence) {
      this.setInPersistent(key, entry);
    }

    this.logger.debug(`Cached data for key: ${key}`);
  }

  /**
   * Check if data exists in cache and is not expired
   */
  has(key: string): boolean {
    const memoryEntry = this.getFromMemory(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return true;
    }

    const persistentEntry = this.getFromPersistent(key);
    return persistentEntry !== null && !this.isExpired(persistentEntry);
  }

  /**
   * Remove data from cache
   */
  delete(key: string): boolean {
    const memoryDeleted = this.memoryCache.delete(key);
    const persistentDeleted = this.deleteFromPersistent(key);
    
    if (memoryDeleted || persistentDeleted) {
      this.logger.debug(`Deleted cache entry for key: ${key}`);
      return true;
    }
    
    return false;
  }

  /**
   * Clear all cache data
   */
  clear(): void {
    this.memoryCache.clear();
    this.clearPersistentCache();
    this.resetMetrics();
    this.logger.info('Cache cleared');
  }

  /**
   * Get cache metrics
   */
  getMetrics(): Observable<CacheMetrics> {
    return this.metrics$.asObservable();
  }

  /**
   * Invalidate cache entries based on pattern
   */
  invalidatePattern(pattern: RegExp): number {
    let invalidatedCount = 0;

    // Invalidate memory cache
    for (const key of this.memoryCache.keys()) {
      if (pattern.test(key)) {
        this.memoryCache.delete(key);
        invalidatedCount++;
      }
    }

    // Invalidate persistent cache
    for (const key of this.persistentKeys) {
      if (pattern.test(key)) {
        this.deleteFromPersistent(key);
        invalidatedCount++;
      }
    }

    this.logger.info(`Invalidated ${invalidatedCount} cache entries matching pattern: ${pattern}`);
    return invalidatedCount;
  }

  /**
   * Warm cache with data
   */
  warmCache<T>(entries: Array<{ key: string; data: T; ttl?: number }>): void {
    entries.forEach(({ key, data, ttl }) => {
      this.set(key, data, ttl);
    });
    
    this.logger.info(`Warmed cache with ${entries.length} entries`);
  }

  // Private methods

  private initializeCache(): void {
    if (this.config.enablePersistence) {
      this.loadPersistentKeys();
    }
    this.logger.info('Multi-level cache initialized');
  }

  private setupCleanupInterval(): void {
    // Run cleanup every 5 minutes
    const interval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);

    // Clear interval on destroy
    this.destroyRef.onDestroy(() => {
      clearInterval(interval);
    });
  }

  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    // Clean memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    // Clean persistent cache
    for (const key of this.persistentKeys) {
      const entry = this.getFromPersistent(key);
      if (!entry || this.isExpired(entry)) {
        this.deleteFromPersistent(key);
        cleanedCount++;
      }
    }

    // Enforce size limits
    this.enforceSizeLimits();

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  private enforceSizeLimits(): void {
    // Enforce memory cache size limit
    if (this.memoryCache.size > this.config.maxMemorySize) {
      const entriesToRemove = this.memoryCache.size - this.config.maxMemorySize;
      const sortedEntries = Array.from(this.memoryCache.entries())
        .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

      for (let i = 0; i < entriesToRemove; i++) {
        this.memoryCache.delete(sortedEntries[i][0]);
      }
    }

    // Enforce persistent cache size limit
    if (this.persistentKeys.size > this.config.maxPersistentSize) {
      const entriesToRemove = this.persistentKeys.size - this.config.maxPersistentSize;
      const sortedKeys = Array.from(this.persistentKeys)
        .map(key => ({ key, entry: this.getFromPersistent(key) }))
        .filter(({ entry }) => entry !== null)
        .sort((a, b) => a.entry!.lastAccessed - b.entry!.lastAccessed);

      for (let i = 0; i < entriesToRemove && i < sortedKeys.length; i++) {
        this.deleteFromPersistent(sortedKeys[i].key);
      }
    }
  }

  private getFromMemory<T>(key: string): CacheEntry<T> | null {
    return this.memoryCache.get(key) || null;
  }

  private setInMemory<T>(key: string, entry: CacheEntry<T>): void {
    this.memoryCache.set(key, entry);
    this.updateMetrics();
  }

  private getFromPersistent<T>(key: string): CacheEntry<T> | null {
    if (!this.config.enablePersistence) return null;

    try {
      const storageKey = this.config.storagePrefix + key;
      const stored = sessionStorage.getItem(storageKey);
      if (!stored) return null;

      return JSON.parse(stored) as CacheEntry<T>;
    } catch (error) {
      this.logger.warn(`Failed to get persistent cache entry for key ${key}:`, error);
      return null;
    }
  }

  private setInPersistent<T>(key: string, entry: CacheEntry<T>): void {
    if (!this.config.enablePersistence) return;

    try {
      const storageKey = this.config.storagePrefix + key;
      sessionStorage.setItem(storageKey, JSON.stringify(entry));
      this.persistentKeys.add(key);
      this.updateMetrics();
    } catch (error) {
      this.logger.warn(`Failed to set persistent cache entry for key ${key}:`, error);
    }
  }

  private deleteFromPersistent(key: string): boolean {
    if (!this.config.enablePersistence) return false;

    try {
      const storageKey = this.config.storagePrefix + key;
      sessionStorage.removeItem(storageKey);
      this.persistentKeys.delete(key);
      this.updateMetrics();
      return true;
    } catch (error) {
      this.logger.warn(`Failed to delete persistent cache entry for key ${key}:`, error);
      return false;
    }
  }

  private loadPersistentKeys(): void {
    try {
      const prefix = this.config.storagePrefix;
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith(prefix)) {
          const cacheKey = key.substring(prefix.length);
          this.persistentKeys.add(cacheKey);
        }
      }
    } catch (error) {
      this.logger.warn('Failed to load persistent cache keys:', error);
    }
  }

  private clearPersistentCache(): void {
    if (!this.config.enablePersistence) return;

    try {
      const keysToRemove: string[] = [];
      const prefix = this.config.storagePrefix;
      
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith(prefix)) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => sessionStorage.removeItem(key));
      this.persistentKeys.clear();
    } catch (error) {
      this.logger.warn('Failed to clear persistent cache:', error);
    }
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expiresAt;
  }

  private updateAccessStats(entry: CacheEntry): void {
    entry.lastAccessed = Date.now();
    entry.accessCount++;
  }

  private generateContentHash(data: any): string {
    // Simple hash function for content comparison
    const str = typeof data === 'string' ? data : JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private updateMetrics(): void {
    const current = this.metrics$.value;
    const updated: CacheMetrics = {
      ...current,
      memorySize: this.memoryCache.size,
      persistentSize: this.persistentKeys.size,
      hitRatio: current.totalRequests > 0 
        ? (current.memoryHits + current.persistentHits) / current.totalRequests 
        : 0
    };
    this.metrics$.next(updated);
  }

  private incrementMemoryHits(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, memoryHits: current.memoryHits + 1 });
  }

  private incrementMemoryMisses(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, memoryMisses: current.memoryMisses + 1 });
  }

  private incrementPersistentHits(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, persistentHits: current.persistentHits + 1 });
  }

  private incrementTotalRequests(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, totalRequests: current.totalRequests + 1 });
  }

  private recordAccessTime(time: number): void {
    const current = this.metrics$.value;
    const newAverage = current.totalRequests > 1 
      ? (current.averageAccessTime * (current.totalRequests - 1) + time) / current.totalRequests
      : time;
    this.metrics$.next({ ...current, averageAccessTime: newAverage });
  }

  private resetMetrics(): void {
    this.metrics$.next({
      memoryHits: 0,
      memoryMisses: 0,
      persistentHits: 0,
      persistentMisses: 0,
      totalRequests: 0,
      memorySize: 0,
      persistentSize: 0,
      hitRatio: 0,
      averageAccessTime: 0
    });
  }
}

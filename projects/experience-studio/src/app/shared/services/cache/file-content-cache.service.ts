import { Injectable, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, tap, switchMap } from 'rxjs/operators';
import { MultiLevelCacheService } from './multi-level-cache.service';
import { createLogger } from '../../utils/logger';

export interface FileContentEntry {
  filePath: string;
  content: string;
  contentHash: string;
  lastModified: number;
  fileSize: number;
  language?: string;
  encoding?: string;
  metadata?: Record<string, any>;
}

export interface FileContentCacheConfig {
  defaultTTL: number;
  maxFileSize: number;
  enableContentHashing: boolean;
  enableLanguageDetection: boolean;
  compressionThreshold: number;
}

@Injectable({
  providedIn: 'root'
})
export class FileContentCacheService {
  private readonly logger = createLogger('FileContentCacheService');
  private readonly multiLevelCache = inject(MultiLevelCacheService);

  private readonly config: FileContentCacheConfig = {
    defaultTTL: 60 * 60 * 1000, // 1 hour for file content
    maxFileSize: 1024 * 1024, // 1MB max file size to cache
    enableContentHashing: true,
    enableLanguageDetection: true,
    compressionThreshold: 10 * 1024 // 10KB threshold for compression
  };

  /**
   * Get file content from cache or source
   */
  getFileContent(
    filePath: string,
    source?: () => Observable<string>,
    lastModified?: number
  ): Observable<string> {
    const cacheKey = this.generateFileKey(filePath);

    return this.multiLevelCache.get<FileContentEntry>(
      cacheKey,
      source ? () => this.createFileContentEntry(filePath, source(), lastModified) : undefined,
      this.config.defaultTTL
    ).pipe(
      switchMap(entry => {
        // Validate cache entry against file modification time
        if (lastModified && entry.lastModified < lastModified) {
          this.logger.debug(`File modified since cache: ${filePath}, invalidating`);
          this.invalidateFile(filePath);

          if (source) {
            // Use switchMap to handle the recursive Observable properly
            return this.getFileContent(filePath, source, lastModified);
          }
          throw new Error(`Cached file is stale: ${filePath}`);
        }

        return of(entry.content);
      })
    );
  }

  /**
   * Cache file content directly
   */
  cacheFileContent(
    filePath: string,
    content: string,
    lastModified?: number,
    metadata?: Record<string, any>
  ): void {
    if (content.length > this.config.maxFileSize) {
      this.logger.warn(`File too large to cache: ${filePath} (${content.length} bytes)`);
      return;
    }

    const entry: FileContentEntry = {
      filePath,
      content,
      contentHash: this.generateContentHash(content),
      lastModified: lastModified || Date.now(),
      fileSize: content.length,
      language: this.config.enableLanguageDetection ? this.detectLanguage(filePath) : undefined,
      encoding: 'utf-8',
      metadata
    };

    const cacheKey = this.generateFileKey(filePath);
    this.multiLevelCache.set(cacheKey, entry, this.config.defaultTTL);

    this.logger.debug(`Cached file content: ${filePath} (${content.length} chars)`);
  }

  /**
   * Check if file content is cached and valid
   */
  hasValidFileContent(filePath: string, lastModified?: number): Observable<boolean> {
    const cacheKey = this.generateFileKey(filePath);

    if (!this.multiLevelCache.has(cacheKey)) {
      return of(false);
    }

    return this.multiLevelCache.get<FileContentEntry>(cacheKey).pipe(
      map(entry => {
        if (lastModified && entry.lastModified < lastModified) {
          return false;
        }
        return true;
      })
    );
  }

  /**
   * Invalidate cached file content
   */
  invalidateFile(filePath: string): boolean {
    const cacheKey = this.generateFileKey(filePath);
    const deleted = this.multiLevelCache.delete(cacheKey);

    if (deleted) {
      this.logger.debug(`Invalidated file cache: ${filePath}`);
    }

    return deleted;
  }

  /**
   * Invalidate multiple files by pattern
   */
  invalidateFilesByPattern(pattern: RegExp): number {
    const filePattern = new RegExp(`^file:${pattern.source}`);
    return this.multiLevelCache.invalidatePattern(filePattern);
  }

  /**
   * Invalidate all files in a directory
   */
  invalidateDirectory(directoryPath: string): number {
    const normalizedPath = this.normalizePath(directoryPath);
    const pattern = new RegExp(`^file:${this.escapeRegExp(normalizedPath)}`);
    return this.multiLevelCache.invalidatePattern(pattern);
  }

  /**
   * Warm cache with multiple files
   */
  warmCacheWithFiles(files: Array<{
    filePath: string;
    content: string;
    lastModified?: number;
    metadata?: Record<string, any>;
  }>): void {
    const entries = files
      .filter(file => file.content.length <= this.config.maxFileSize)
      .map(file => ({
        key: this.generateFileKey(file.filePath),
        data: {
          filePath: file.filePath,
          content: file.content,
          contentHash: this.generateContentHash(file.content),
          lastModified: file.lastModified || Date.now(),
          fileSize: file.content.length,
          language: this.config.enableLanguageDetection ? this.detectLanguage(file.filePath) : undefined,
          encoding: 'utf-8',
          metadata: file.metadata
        } as FileContentEntry,
        ttl: this.config.defaultTTL
      }));

    this.multiLevelCache.warmCache(entries);
    this.logger.info(`Warmed file cache with ${entries.length} files`);
  }

  /**
   * Get file content with metadata
   */
  getFileContentWithMetadata(
    filePath: string,
    source?: () => Observable<string>,
    lastModified?: number
  ): Observable<FileContentEntry> {
    const cacheKey = this.generateFileKey(filePath);

    return this.multiLevelCache.get<FileContentEntry>(
      cacheKey,
      source ? () => this.createFileContentEntry(filePath, source(), lastModified) : undefined,
      this.config.defaultTTL
    ).pipe(
      tap(entry => {
        // Validate cache entry against file modification time
        if (lastModified && entry.lastModified < lastModified) {
          this.logger.debug(`File modified since cache: ${filePath}, invalidating`);
          this.invalidateFile(filePath);
        }
      })
    );
  }

  /**
   * Update file content in cache
   */
  updateFileContent(
    filePath: string,
    content: string,
    lastModified?: number,
    metadata?: Record<string, any>
  ): void {
    // First invalidate existing cache
    this.invalidateFile(filePath);

    // Then cache new content
    this.cacheFileContent(filePath, content, lastModified, metadata);
  }

  /**
   * Get cache statistics for files
   */
  getFilesCacheStats(): Observable<{
    totalFiles: number;
    totalSize: number;
    averageFileSize: number;
    languageDistribution: Record<string, number>;
  }> {
    return this.multiLevelCache.getMetrics().pipe(
      map(metrics => {
        // This is a simplified version - in a real implementation,
        // we'd need to iterate through cache entries to get detailed stats
        return {
          totalFiles: metrics.memorySize + metrics.persistentSize,
          totalSize: 0, // Would need to calculate from actual entries
          averageFileSize: 0, // Would need to calculate from actual entries
          languageDistribution: {} // Would need to analyze cached files
        };
      })
    );
  }

  /**
   * Batch cache multiple files efficiently
   */
  batchCacheFiles(files: Array<{
    filePath: string;
    content: string;
    lastModified?: number;
    metadata?: Record<string, any>;
  }>): void {
    const validFiles = files.filter(file => file.content.length <= this.config.maxFileSize);

    validFiles.forEach(file => {
      this.cacheFileContent(file.filePath, file.content, file.lastModified, file.metadata);
    });

    this.logger.info(`Batch cached ${validFiles.length} files (${files.length - validFiles.length} skipped due to size)`);
  }

  /**
   * Preload files based on access patterns
   */
  preloadRelatedFiles(
    currentFilePath: string,
    allFiles: Array<{ filePath: string; content: string; lastModified?: number }>
  ): void {
    const currentDir = this.getDirectoryPath(currentFilePath);
    const currentExt = this.getFileExtension(currentFilePath);

    // Preload files in the same directory or with same extension
    const relatedFiles = allFiles.filter(file => {
      const fileDir = this.getDirectoryPath(file.filePath);
      const fileExt = this.getFileExtension(file.filePath);

      return fileDir === currentDir || fileExt === currentExt;
    }).slice(0, 10); // Limit to 10 related files

    this.batchCacheFiles(relatedFiles);
    this.logger.debug(`Preloaded ${relatedFiles.length} related files for: ${currentFilePath}`);
  }

  // Private methods

  private createFileContentEntry(
    filePath: string,
    contentObservable: Observable<string>,
    lastModified?: number
  ): Observable<FileContentEntry> {
    return contentObservable.pipe(
      map(content => {
        if (content.length > this.config.maxFileSize) {
          this.logger.warn(`File too large to cache: ${filePath} (${content.length} bytes)`);
          // Still return the entry but don't cache it
        }

        return {
          filePath,
          content,
          contentHash: this.generateContentHash(content),
          lastModified: lastModified || Date.now(),
          fileSize: content.length,
          language: this.config.enableLanguageDetection ? this.detectLanguage(filePath) : undefined,
          encoding: 'utf-8'
        } as FileContentEntry;
      })
    );
  }

  private generateFileKey(filePath: string): string {
    return `file:${this.normalizePath(filePath)}`;
  }

  private normalizePath(filePath: string): string {
    return filePath.replace(/\\/g, '/').toLowerCase();
  }

  private generateContentHash(content: string): string {
    if (!this.config.enableContentHashing) {
      return '';
    }

    // Simple hash function for content comparison
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private detectLanguage(filePath: string): string {
    if (!this.config.enableLanguageDetection) {
      return 'plaintext';
    }

    const extension = filePath.split('.').pop()?.toLowerCase();

    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'js': 'javascript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'sql': 'sql',
      'sh': 'shell',
      'yml': 'yaml',
      'yaml': 'yaml'
    };

    return languageMap[extension || ''] || 'plaintext';
  }

  private getDirectoryPath(filePath: string): string {
    const normalizedPath = this.normalizePath(filePath);
    const lastSlash = normalizedPath.lastIndexOf('/');
    return lastSlash > 0 ? normalizedPath.substring(0, lastSlash) : '';
  }

  private getFileExtension(filePath: string): string {
    return filePath.split('.').pop()?.toLowerCase() || '';
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

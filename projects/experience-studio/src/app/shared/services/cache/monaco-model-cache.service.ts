import { Injectable, inject, DestroyRef } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, tap, switchMap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MultiLevelCacheService } from './multi-level-cache.service';
import { FileContentCacheService } from './file-content-cache.service';
import { createLogger } from '../../utils/logger';

export interface MonacoModelEntry {
  uri: string;
  content: string;
  language: string;
  contentHash: string;
  lastModified: number;
  model: any; // Monaco model instance
  isDisposed: boolean;
  accessCount: number;
  lastAccessed: number;
  metadata?: Record<string, any>;
}

export interface MonacoModelCacheConfig {
  maxModels: number;
  defaultTTL: number;
  enableContentValidation: boolean;
  enableModelReuse: boolean;
  disposeUnusedAfter: number; // milliseconds
}

export interface MonacoModelMetrics {
  totalModels: number;
  activeModels: number;
  disposedModels: number;
  cacheHits: number;
  cacheMisses: number;
  modelCreations: number;
  modelDisposals: number;
  hitRatio: number;
  memoryUsage: number; // estimated in bytes
}

@Injectable({
  providedIn: 'root'
})
export class MonacoModelCacheService {
  private readonly logger = createLogger('MonacoModelCacheService');
  private readonly destroyRef = inject(DestroyRef);
  private readonly multiLevelCache = inject(MultiLevelCacheService);
  private readonly fileContentCache = inject(FileContentCacheService);

  // Active Monaco models cache
  private activeModels = new Map<string, MonacoModelEntry>();
  
  // Metrics tracking
  private metrics$ = new BehaviorSubject<MonacoModelMetrics>({
    totalModels: 0,
    activeModels: 0,
    disposedModels: 0,
    cacheHits: 0,
    cacheMisses: 0,
    modelCreations: 0,
    modelDisposals: 0,
    hitRatio: 0,
    memoryUsage: 0
  });

  private readonly config: MonacoModelCacheConfig = {
    maxModels: 50, // Maximum number of active models
    defaultTTL: 2 * 60 * 60 * 1000, // 2 hours
    enableContentValidation: true,
    enableModelReuse: true,
    disposeUnusedAfter: 30 * 60 * 1000 // 30 minutes
  };

  constructor() {
    this.setupCleanupInterval();
    this.setupDestroyHandler();
  }

  /**
   * Get or create Monaco model with intelligent caching
   */
  getOrCreateModel(
    monaco: any,
    content: string,
    language: string,
    uri?: string
  ): Observable<any> {
    const modelUri = uri || this.generateModelUri(content, language);
    const contentHash = this.generateContentHash(content);

    // Check if we have a valid cached model
    const cachedEntry = this.activeModels.get(modelUri);
    if (cachedEntry && this.isValidCachedModel(cachedEntry, content, contentHash)) {
      this.updateModelAccess(cachedEntry);
      this.incrementCacheHits();
      this.logger.debug(`Cache hit for Monaco model: ${modelUri}`);
      return of(cachedEntry.model);
    }

    // Cache miss - create new model
    this.incrementCacheMisses();
    return this.createAndCacheModel(monaco, content, language, modelUri, contentHash);
  }

  /**
   * Create and cache a new Monaco model
   */
  private createAndCacheModel(
    monaco: any,
    content: string,
    language: string,
    uri: string,
    contentHash: string
  ): Observable<any> {
    return of(null).pipe(
      map(() => {
        // Dispose existing model if it exists
        const existingEntry = this.activeModels.get(uri);
        if (existingEntry && !existingEntry.isDisposed) {
          this.disposeModel(uri);
        }

        // Create new Monaco model
        const model = monaco.editor.createModel(
          content,
          language,
          uri ? monaco.Uri.parse(uri) : undefined
        );

        // Create cache entry
        const entry: MonacoModelEntry = {
          uri,
          content,
          language,
          contentHash,
          lastModified: Date.now(),
          model,
          isDisposed: false,
          accessCount: 1,
          lastAccessed: Date.now()
        };

        // Cache the model
        this.activeModels.set(uri, entry);
        this.incrementModelCreations();
        this.updateMetrics();

        // Enforce cache size limits
        this.enforceCacheLimits();

        this.logger.debug(`Created and cached Monaco model: ${uri} (${language})`);
        return model;
      })
    );
  }

  /**
   * Update existing model content
   */
  updateModelContent(uri: string, content: string): boolean {
    const entry = this.activeModels.get(uri);
    if (!entry || entry.isDisposed) {
      return false;
    }

    const newContentHash = this.generateContentHash(content);
    if (entry.contentHash === newContentHash) {
      // Content hasn't changed
      this.updateModelAccess(entry);
      return true;
    }

    // Update model content
    entry.model.setValue(content);
    entry.content = content;
    entry.contentHash = newContentHash;
    entry.lastModified = Date.now();
    this.updateModelAccess(entry);

    this.logger.debug(`Updated Monaco model content: ${uri}`);
    return true;
  }

  /**
   * Dispose a specific model
   */
  disposeModel(uri: string): boolean {
    const entry = this.activeModels.get(uri);
    if (!entry || entry.isDisposed) {
      return false;
    }

    try {
      entry.model.dispose();
      entry.isDisposed = true;
      this.activeModels.delete(uri);
      this.incrementModelDisposals();
      this.updateMetrics();

      this.logger.debug(`Disposed Monaco model: ${uri}`);
      return true;
    } catch (error) {
      this.logger.error(`Error disposing Monaco model ${uri}:`, error);
      return false;
    }
  }

  /**
   * Dispose all models
   */
  disposeAllModels(): number {
    let disposedCount = 0;
    
    for (const [uri, entry] of this.activeModels.entries()) {
      if (!entry.isDisposed) {
        try {
          entry.model.dispose();
          entry.isDisposed = true;
          disposedCount++;
        } catch (error) {
          this.logger.error(`Error disposing model ${uri}:`, error);
        }
      }
    }

    this.activeModels.clear();
    this.updateMetrics();
    
    this.logger.info(`Disposed ${disposedCount} Monaco models`);
    return disposedCount;
  }

  /**
   * Get model by URI
   */
  getModel(uri: string): any | null {
    const entry = this.activeModels.get(uri);
    if (entry && !entry.isDisposed) {
      this.updateModelAccess(entry);
      return entry.model;
    }
    return null;
  }

  /**
   * Check if model exists and is valid
   */
  hasValidModel(uri: string, content?: string): boolean {
    const entry = this.activeModels.get(uri);
    if (!entry || entry.isDisposed) {
      return false;
    }

    if (content && this.config.enableContentValidation) {
      const contentHash = this.generateContentHash(content);
      return entry.contentHash === contentHash;
    }

    return true;
  }

  /**
   * Get cache metrics
   */
  getMetrics(): Observable<MonacoModelMetrics> {
    return this.metrics$.asObservable();
  }

  /**
   * Warm cache with models
   */
  warmCache(
    monaco: any,
    models: Array<{
      uri: string;
      content: string;
      language: string;
    }>
  ): Observable<number> {
    return of(null).pipe(
      map(() => {
        let warmedCount = 0;
        
        models.forEach(({ uri, content, language }) => {
          if (!this.hasValidModel(uri, content)) {
            this.createAndCacheModel(
              monaco,
              content,
              language,
              uri,
              this.generateContentHash(content)
            ).subscribe(() => {
              warmedCount++;
            });
          }
        });

        this.logger.info(`Warmed Monaco model cache with ${warmedCount} models`);
        return warmedCount;
      })
    );
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalModels: number;
    activeModels: number;
    memoryUsage: number;
    oldestModel: string | null;
    newestModel: string | null;
  } {
    const entries = Array.from(this.activeModels.values());
    const activeEntries = entries.filter(e => !e.isDisposed);
    
    let oldestModel: string | null = null;
    let newestModel: string | null = null;
    let oldestTime = Date.now();
    let newestTime = 0;

    activeEntries.forEach(entry => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestModel = entry.uri;
      }
      if (entry.lastAccessed > newestTime) {
        newestTime = entry.lastAccessed;
        newestModel = entry.uri;
      }
    });

    return {
      totalModels: this.activeModels.size,
      activeModels: activeEntries.length,
      memoryUsage: this.estimateMemoryUsage(),
      oldestModel,
      newestModel
    };
  }

  // Private methods

  private isValidCachedModel(
    entry: MonacoModelEntry,
    content: string,
    contentHash: string
  ): boolean {
    if (entry.isDisposed) {
      return false;
    }

    if (this.config.enableContentValidation && entry.contentHash !== contentHash) {
      return false;
    }

    // Check if model is still valid (not disposed by Monaco)
    try {
      entry.model.getValue(); // This will throw if model is disposed
      return true;
    } catch {
      entry.isDisposed = true;
      return false;
    }
  }

  private updateModelAccess(entry: MonacoModelEntry): void {
    entry.lastAccessed = Date.now();
    entry.accessCount++;
  }

  private generateModelUri(content: string, language: string): string {
    const hash = this.generateContentHash(content);
    return `inmemory://model/${language}/${hash}`;
  }

  private generateContentHash(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private enforceCacheLimits(): void {
    if (this.activeModels.size <= this.config.maxModels) {
      return;
    }

    // Sort by last accessed time (oldest first)
    const entries = Array.from(this.activeModels.entries())
      .filter(([, entry]) => !entry.isDisposed)
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    const modelsToDispose = this.activeModels.size - this.config.maxModels;
    
    for (let i = 0; i < modelsToDispose && i < entries.length; i++) {
      const [uri] = entries[i];
      this.disposeModel(uri);
    }

    this.logger.debug(`Enforced cache limits, disposed ${modelsToDispose} models`);
  }

  private setupCleanupInterval(): void {
    const interval = setInterval(() => {
      this.cleanupUnusedModels();
    }, 5 * 60 * 1000); // Run every 5 minutes

    this.destroyRef.onDestroy(() => {
      clearInterval(interval);
    });
  }

  private cleanupUnusedModels(): void {
    const now = Date.now();
    const unusedThreshold = now - this.config.disposeUnusedAfter;
    let cleanedCount = 0;

    for (const [uri, entry] of this.activeModels.entries()) {
      if (!entry.isDisposed && entry.lastAccessed < unusedThreshold) {
        this.disposeModel(uri);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} unused Monaco models`);
    }
  }

  private setupDestroyHandler(): void {
    this.destroyRef.onDestroy(() => {
      this.disposeAllModels();
    });
  }

  private estimateMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.activeModels.values()) {
      if (!entry.isDisposed) {
        totalSize += entry.content.length * 2; // Rough estimate (UTF-16)
      }
    }
    return totalSize;
  }

  private updateMetrics(): void {
    const current = this.metrics$.value;
    const stats = this.getCacheStats();
    
    const updated: MonacoModelMetrics = {
      ...current,
      totalModels: stats.totalModels,
      activeModels: stats.activeModels,
      memoryUsage: stats.memoryUsage,
      hitRatio: current.cacheHits + current.cacheMisses > 0 
        ? current.cacheHits / (current.cacheHits + current.cacheMisses)
        : 0
    };

    this.metrics$.next(updated);
  }

  private incrementCacheHits(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, cacheHits: current.cacheHits + 1 });
  }

  private incrementCacheMisses(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, cacheMisses: current.cacheMisses + 1 });
  }

  private incrementModelCreations(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ ...current, modelCreations: current.modelCreations + 1 });
  }

  private incrementModelDisposals(): void {
    const current = this.metrics$.value;
    this.metrics$.next({ 
      ...current, 
      modelDisposals: current.modelDisposals + 1,
      disposedModels: current.disposedModels + 1
    });
  }
}

import { Injectable, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';

import { createLogger } from '../utils';
/**
 * Global error handler service to suppress known browser extension errors
 * and handle other application errors gracefully
 */
@Injectable({
  providedIn: 'root'
})
export class GlobalErrorHandlerService implements ErrorHandler {

  constructor(private ngZone: NgZone) {}

  private logger = createLogger('GlobalError')



  handleError(error: any): void {
    // Check if this is a message channel error from browser extensions
    if (this.isMessageChannelError(error)) {
      // Silently ignore these errors as they're from browser extensions
      // and don't affect our application functionality
      return;
    }

    // Check if this is a Chrome extension error
    if (this.isChromeExtensionError(error)) {
      // Silently ignore Chrome extension errors
      return;
    }

    // Check if this is a ResizeObserver loop limit exceeded error
    if (this.isResizeObserverError(error)) {
      // This is a benign error that can be safely ignored
      return;
    }

    // Check if this is a non-passive event listener warning
    if (this.isNonPassiveEventWarning(error)) {
      // We handle this properly in our code, so ignore the warning
      return;
    }

    // Check if this is a Monaco Editor font loading error
    if (this.isMonacoFontError(error)) {
      // Monaco Editor font loading errors are handled by webpack configuration
      // Silently ignore these as they don't affect functionality
      return;
    }

    // Check if this is a code preview generation error
    if (this.isCodePreviewError(error)) {
      // Code preview generation errors are often related to browser extensions
      // or async communication issues that don't affect core functionality
      return;
    }

    // For all other errors, log them to console in development
    if (this.isDevelopmentMode()) {
      this.logger.error('Application Error:', error);
    }

    // In production, you might want to send errors to a logging service
    // this.sendErrorToLoggingService(error);
  }

  /**
   * Check if the error is related to message channel communication
   * (typically from browser extensions)
   */
  private isMessageChannelError(error: any): boolean {
    const errorMessage = this.getErrorMessage(error);

    return errorMessage.includes('listener indicated an asynchronous response') ||
           errorMessage.includes('message channel closed before a response was received') ||
           errorMessage.includes('Extension context invalidated') ||
           errorMessage.includes('chrome-extension://');
  }

  /**
   * Check if the error is from a Chrome extension
   */
  private isChromeExtensionError(error: any): boolean {
    const errorMessage = this.getErrorMessage(error);
    const stack = error?.stack || '';

    return errorMessage.includes('chrome-extension://') ||
           stack.includes('chrome-extension://') ||
           errorMessage.includes('Extension context') ||
           errorMessage.includes('chrome.runtime');
  }

  /**
   * Check if the error is a ResizeObserver loop limit exceeded error
   */
  private isResizeObserverError(error: any): boolean {
    const errorMessage = this.getErrorMessage(error);

    return errorMessage.includes('ResizeObserver loop limit exceeded') ||
           errorMessage.includes('ResizeObserver loop completed with undelivered notifications');
  }

  /**
   * Check if this is a non-passive event listener warning
   */
  private isNonPassiveEventWarning(error: any): boolean {
    const errorMessage = this.getErrorMessage(error);

    return errorMessage.includes('Added non-passive event listener') ||
           errorMessage.includes('passive event listener');
  }

  /**
   * Check if this is a Monaco Editor font loading error
   */
  private isMonacoFontError(error: any): boolean {
    const errorMessage = this.getErrorMessage(error);
    const stack = error?.stack || '';

    return errorMessage.includes('codicon.ttf') ||
           errorMessage.includes('monaco-editor') ||
           stack.includes('codicon') ||
           errorMessage.includes('font loading failed');
  }

  /**
   * Check if this is a code preview generation error
   */
  private isCodePreviewError(error: any): boolean {
    const errorMessage = this.getErrorMessage(error);
    const stack = error?.stack || '';

    return errorMessage.includes('6generate-application/code-preview') ||
           stack.includes('code-preview') ||
           (errorMessage.includes('listener indicated an asynchronous response') &&
            stack.includes('generate-application'));
  }

  /**
   * Extract error message from various error formats
   */
  private getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error.toLowerCase();
    }

    if (error?.message) {
      return error.message.toLowerCase();
    }

    if (error?.error?.message) {
      return error.error.message.toLowerCase();
    }

    return String(error).toLowerCase();
  }

  /**
   * Check if we're in development mode
   */
  private isDevelopmentMode(): boolean {
    return !environment.production;
  }

  /**
   * Send error to logging service (implement as needed)
   */
  private sendErrorToLoggingService(error: any): void {
    // Implement your logging service integration here
    // For example: this.loggingService.logError(error);
  }
}

// Import environment (you may need to adjust the path)
declare const environment: { production: boolean };

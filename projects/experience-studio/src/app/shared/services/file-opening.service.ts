import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';
import { createLogger } from '../utils/logger';

export interface FileOpenRequest {
  fileName: string;
  filePath: string;
  source: 'accordion' | 'external';
  switchToCodeTab?: boolean;
}

export interface TabSwitchRequest {
  tab: 'code' | 'preview' | 'logs' | 'artifacts' | 'overview'; // DEPRECATED: 'logs' - now integrated into artifacts
  source: 'file-opening' | 'user-click';
}

@Injectable({
  providedIn: 'root'
})
export class FileOpeningService {
  private logger = createLogger('FileOpeningService');
  private fileOpenRequests$ = new Subject<FileOpenRequest>();
  private tabSwitchRequests$ = new Subject<TabSwitchRequest>();

  /**
   * Observable for components to listen to file open requests
   */
  get fileOpenRequests(): Observable<FileOpenRequest> {
    return this.fileOpenRequests$.asObservable();
  }

  /**
   * Observable for components to listen to tab switch requests
   */
  get tabSwitchRequests(): Observable<TabSwitchRequest> {
    return this.tabSwitchRequests$.asObservable();
  }

  /**
   * Request to open a file in the code viewer
   * @param fileName The name of the file to open
   * @param filePath The full path of the file
   * @param source The source of the request
   * @param switchToCodeTab Whether to switch to the code tab after opening the file
   */
  openFile(fileName: string, filePath: string, source: 'accordion' | 'external' = 'accordion', switchToCodeTab: boolean = true): void {
    this.logger.info(`📂 File open request: ${fileName} (${filePath}) from ${source}, switchToCodeTab: ${switchToCodeTab}`);

    const request: FileOpenRequest = {
      fileName,
      filePath,
      source,
      switchToCodeTab
    };

    this.fileOpenRequests$.next(request);

    // If requested, also switch to the code tab
    if (switchToCodeTab) {
      this.switchToTab('code', 'file-opening');
    }
  }

  /**
   * Request to switch to a specific tab
   * @param tab The tab to switch to (logs redirects to artifacts)
   * @param source The source of the request
   */
  switchToTab(tab: 'code' | 'preview' | 'logs' | 'artifacts' | 'overview', source: 'file-opening' | 'user-click' = 'user-click'): void {
    this.logger.info(`🔄 Tab switch request: ${tab} from ${source}`);

    const request: TabSwitchRequest = {
      tab,
      source
    };

    this.tabSwitchRequests$.next(request);
  }

  /**
   * Normalize file path for consistent matching
   * @param filePath The file path to normalize
   * @returns Normalized file path
   */
  normalizeFilePath(filePath: string): string {
    return filePath.replace(/\\/g, '/').replace(/^\/+/, '');
  }

  /**
   * Extract file name from file path
   * @param filePath The file path
   * @returns The file name
   */
  extractFileName(filePath: string): string {
    const normalizedPath = this.normalizeFilePath(filePath);
    return normalizedPath.split('/').pop() || filePath;
  }
}

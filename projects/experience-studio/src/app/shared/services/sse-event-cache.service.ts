import { Injectable, DestroyRef, inject, signal, computed } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
 * Interface for cached SSE event information
 */
export interface CachedSSEEvent {
  id: string;
  timestamp: number;
  eventType: 'initial-code-gen' | 'code-regen' | 'update' | 'message';
  status?: string;
  progress?: string;
  sessionKey?: string;
  processed: boolean;
}

/**
 * Interface for event cache statistics
 */
export interface EventCacheStats {
  totalCached: number;
  processedCount: number;
  unprocessedCount: number;
  oldestEventTimestamp: number | null;
  newestEventTimestamp: number | null;
  sessionKeys: string[];
}

/**
 * SSE Event Cache Service
 * 
 * Provides intelligent caching and checkpoint-based processing for SSE events
 * during regeneration processes. Implements Angular 19+ patterns with proper
 * lifecycle management and memory leak prevention.
 * 
 * Features:
 * - Timestamp-based event ID caching with automatic cleanup
 * - Duplicate event detection and prevention
 * - Session-based event isolation for regeneration processes
 * - Memory-efficient cache management with size limits
 * - Angular 19+ patterns: inject(), Signals, takeUntilDestroyed()
 * - Comprehensive logging and debugging support
 */
@Injectable({
  providedIn: 'root'
})
export class SSEEventCacheService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('SSEEventCacheService');

  // Cache configuration
  private readonly MAX_CACHE_SIZE = 1000;
  private readonly CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_EVENT_AGE = 30 * 60 * 1000; // 30 minutes

  // Angular 19+ Signals for reactive state management
  private readonly eventCache = signal<Map<string, CachedSSEEvent>>(new Map());
  private readonly sessionEventIds = signal<Map<string, Set<string>>>(new Map());
  private readonly cacheStats = signal<EventCacheStats>({
    totalCached: 0,
    processedCount: 0,
    unprocessedCount: 0,
    oldestEventTimestamp: null,
    newestEventTimestamp: null,
    sessionKeys: []
  });

  // Computed signals for derived state
  public readonly cacheSize = computed(() => this.eventCache().size);
  public readonly sessionCount = computed(() => this.sessionEventIds().size);
  public readonly stats = computed(() => this.cacheStats());

  // BehaviorSubjects for observable streams
  private readonly cacheUpdated$ = new BehaviorSubject<boolean>(false);
  private readonly eventProcessed$ = new BehaviorSubject<string | null>(null);

  // Cleanup timer
  private cleanupTimer: number | null = null;

  constructor() {
    this.initializeService();
    this.startPeriodicCleanup();
    this.logger.info('✅ SSE Event Cache Service initialized with Angular 19+ patterns');
  }

  /**
   * Initialize the service with proper lifecycle management
   */
  private initializeService(): void {
    // Set up automatic cleanup on component destruction
    this.destroyRef.onDestroy(() => {
      this.cleanup();
    });

    this.logger.info('🔧 SSE Event Cache Service initialized:', {
      maxCacheSize: this.MAX_CACHE_SIZE,
      cleanupInterval: this.CACHE_CLEANUP_INTERVAL,
      maxEventAge: this.MAX_EVENT_AGE
    });
  }

  /**
   * Check if an event ID has been processed
   * @param eventId The event ID to check
   * @param sessionKey Optional session key for session-specific checking
   * @returns True if the event has been processed, false otherwise
   */
  public isEventProcessed(eventId: string, sessionKey?: string): boolean {
    if (!eventId) {
      return false;
    }

    const cache = this.eventCache();
    const cachedEvent = cache.get(eventId);

    if (!cachedEvent) {
      this.logger.debug('🔍 Event ID not found in cache:', {
        eventId,
        sessionKey,
        cacheSize: cache.size
      });
      return false;
    }

    // Check session-specific processing if session key is provided
    if (sessionKey && cachedEvent.sessionKey !== sessionKey) {
      this.logger.debug('🔍 Event ID found but different session:', {
        eventId,
        requestedSession: sessionKey,
        cachedSession: cachedEvent.sessionKey
      });
      return false;
    }

    const isProcessed = cachedEvent.processed;
    this.logger.debug('🔍 Event ID cache check result:', {
      eventId,
      sessionKey,
      isProcessed,
      eventType: cachedEvent.eventType,
      timestamp: new Date(cachedEvent.timestamp).toISOString()
    });

    return isProcessed;
  }

  /**
   * Cache an SSE event for duplicate prevention
   * @param eventId The event ID to cache
   * @param eventType The type of SSE event
   * @param sessionKey Optional session key for session isolation
   * @param status Optional event status
   * @param progress Optional event progress
   * @returns True if the event was newly cached, false if it was already cached
   */
  public cacheEvent(
    eventId: string,
    eventType: 'initial-code-gen' | 'code-regen' | 'update' | 'message',
    sessionKey?: string,
    status?: string,
    progress?: string
  ): boolean {
    if (!eventId) {
      this.logger.warn('⚠️ Cannot cache event without ID');
      return false;
    }

    const currentCache = this.eventCache();
    const existingEvent = currentCache.get(eventId);

    if (existingEvent) {
      this.logger.debug('📋 Event already cached:', {
        eventId,
        eventType,
        sessionKey,
        existingType: existingEvent.eventType,
        existingSession: existingEvent.sessionKey,
        alreadyProcessed: existingEvent.processed
      });
      return false;
    }

    // Create new cached event
    const cachedEvent: CachedSSEEvent = {
      id: eventId,
      timestamp: Date.now(),
      eventType,
      status,
      progress,
      sessionKey,
      processed: false
    };

    // Update cache
    const newCache = new Map(currentCache);
    newCache.set(eventId, cachedEvent);
    this.eventCache.set(newCache);

    // Update session tracking
    if (sessionKey) {
      this.addEventToSession(sessionKey, eventId);
    }

    // Update statistics
    this.updateCacheStats();

    // Trigger cache size management
    this.manageCacheSize();

    this.logger.info('💾 Event cached successfully:', {
      eventId,
      eventType,
      sessionKey,
      status,
      progress,
      cacheSize: newCache.size,
      timestamp: new Date(cachedEvent.timestamp).toISOString()
    });

    // Emit cache update
    this.cacheUpdated$.next(true);

    return true;
  }

  /**
   * Mark an event as processed
   * @param eventId The event ID to mark as processed
   * @param sessionKey Optional session key for validation
   * @returns True if the event was marked as processed, false if not found
   */
  public markEventProcessed(eventId: string, sessionKey?: string): boolean {
    if (!eventId) {
      return false;
    }

    const currentCache = this.eventCache();
    const cachedEvent = currentCache.get(eventId);

    if (!cachedEvent) {
      this.logger.warn('⚠️ Cannot mark non-existent event as processed:', {
        eventId,
        sessionKey
      });
      return false;
    }

    // Validate session if provided
    if (sessionKey && cachedEvent.sessionKey !== sessionKey) {
      this.logger.warn('⚠️ Session mismatch when marking event as processed:', {
        eventId,
        requestedSession: sessionKey,
        cachedSession: cachedEvent.sessionKey
      });
      return false;
    }

    // Update the cached event
    const updatedEvent: CachedSSEEvent = {
      ...cachedEvent,
      processed: true
    };

    const newCache = new Map(currentCache);
    newCache.set(eventId, updatedEvent);
    this.eventCache.set(newCache);

    // Update statistics
    this.updateCacheStats();

    this.logger.info('✅ Event marked as processed:', {
      eventId,
      sessionKey,
      eventType: updatedEvent.eventType,
      timestamp: new Date(updatedEvent.timestamp).toISOString()
    });

    // Emit processing notification
    this.eventProcessed$.next(eventId);

    return true;
  }

  /**
   * Get observable for cache updates
   */
  public getCacheUpdates(): Observable<boolean> {
    return this.cacheUpdated$.asObservable();
  }

  /**
   * Get observable for event processing notifications
   */
  public getEventProcessed(): Observable<string | null> {
    return this.eventProcessed$.asObservable();
  }

  /**
   * Add event to session tracking
   */
  private addEventToSession(sessionKey: string, eventId: string): void {
    const currentSessions = this.sessionEventIds();
    const newSessions = new Map(currentSessions);
    
    if (!newSessions.has(sessionKey)) {
      newSessions.set(sessionKey, new Set());
    }
    
    newSessions.get(sessionKey)!.add(eventId);
    this.sessionEventIds.set(newSessions);
  }

  /**
   * Update cache statistics
   */
  private updateCacheStats(): void {
    const cache = this.eventCache();
    const sessions = this.sessionEventIds();
    
    let processedCount = 0;
    let oldestTimestamp: number | null = null;
    let newestTimestamp: number | null = null;

    for (const event of cache.values()) {
      if (event.processed) {
        processedCount++;
      }
      
      if (oldestTimestamp === null || event.timestamp < oldestTimestamp) {
        oldestTimestamp = event.timestamp;
      }
      
      if (newestTimestamp === null || event.timestamp > newestTimestamp) {
        newestTimestamp = event.timestamp;
      }
    }

    this.cacheStats.set({
      totalCached: cache.size,
      processedCount,
      unprocessedCount: cache.size - processedCount,
      oldestEventTimestamp: oldestTimestamp,
      newestEventTimestamp: newestTimestamp,
      sessionKeys: Array.from(sessions.keys())
    });
  }

  /**
   * Manage cache size to prevent memory issues
   */
  private manageCacheSize(): void {
    const cache = this.eventCache();
    
    if (cache.size <= this.MAX_CACHE_SIZE) {
      return;
    }

    this.logger.info('🧹 Cache size limit exceeded, cleaning up old events:', {
      currentSize: cache.size,
      maxSize: this.MAX_CACHE_SIZE
    });

    // Remove oldest processed events first
    const sortedEvents = Array.from(cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);

    const newCache = new Map(cache);
    let removedCount = 0;

    for (const [eventId, event] of sortedEvents) {
      if (newCache.size <= this.MAX_CACHE_SIZE * 0.8) {
        break;
      }

      if (event.processed) {
        newCache.delete(eventId);
        removedCount++;
      }
    }

    this.eventCache.set(newCache);
    this.updateCacheStats();

    this.logger.info('✅ Cache cleanup completed:', {
      removedEvents: removedCount,
      newSize: newCache.size
    });
  }

  /**
   * Start periodic cleanup of old events
   */
  private startPeriodicCleanup(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanupOldEvents();
    }, this.CACHE_CLEANUP_INTERVAL);
  }

  /**
   * Clean up old events based on age
   */
  private cleanupOldEvents(): void {
    const cache = this.eventCache();
    const cutoffTime = Date.now() - this.MAX_EVENT_AGE;
    const newCache = new Map(cache);
    let removedCount = 0;

    for (const [eventId, event] of cache.entries()) {
      if (event.timestamp < cutoffTime) {
        newCache.delete(eventId);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      this.eventCache.set(newCache);
      this.updateCacheStats();

      this.logger.info('🧹 Periodic cleanup removed old events:', {
        removedCount,
        cutoffTime: new Date(cutoffTime).toISOString(),
        newCacheSize: newCache.size
      });
    }
  }

  /**
   * Clean up all resources
   */
  private cleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    this.eventCache.set(new Map());
    this.sessionEventIds.set(new Map());
    this.cacheUpdated$.complete();
    this.eventProcessed$.complete();

    this.logger.info('🧹 SSE Event Cache Service cleaned up');
  }
}

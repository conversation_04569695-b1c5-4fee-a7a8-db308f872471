import { Injectable, DestroyRef, inject, signal, computed } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, Subject, combineLatest } from 'rxjs';
import { filter, map, distinctUntilChanged, tap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { SSEEventCacheService } from './sse-event-cache.service';
import { SSEDataProcessorService } from './sse-data-processor.service';
import { EnhancedSSEService } from './enhanced-sse.service';
import { SequentialRegenerationService } from './sequential-regeneration.service';

/**
 * Interface for regeneration checkpoint state
 */
export interface RegenerationCheckpointState {
  sessionKey: string;
  projectId: string;
  jobId: string;
  isActive: boolean;
  processedEventCount: number;
  lastProcessedEventId: string | null;
  lastProcessedTimestamp: number | null;
  completionStatus: 'pending' | 'completed' | 'failed' | 'cancelled';
}

/**
 * Interface for regeneration progress with checkpoint tracking
 */
export interface CheckpointRegenerationProgress {
  sessionKey: string;
  eventId: string | null;
  eventType: 'initial-code-gen' | 'code-regen' | 'update' | 'message';
  progress: string;
  status: string;
  isNewEvent: boolean;
  processedAt: number;
  metadata?: any;
  logs?: string[];
}

/**
 * Regeneration Checkpoint Service
 * 
 * Coordinates intelligent checkpoint-based processing for regeneration events
 * using Angular 19+ patterns. Integrates with SSE Event Cache Service to provide
 * duplicate prevention and efficient event processing during regeneration cycles.
 * 
 * Features:
 * - Session-based checkpoint tracking for regeneration processes
 * - Intelligent event filtering and duplicate prevention
 * - Integration with existing regeneration services
 * - Reactive state management with Angular Signals
 * - Proper lifecycle management with takeUntilDestroyed()
 * - Comprehensive logging and debugging support
 */
@Injectable({
  providedIn: 'root'
})
export class RegenerationCheckpointService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('RegenerationCheckpointService');
  private readonly eventCacheService = inject(SSEEventCacheService);
  private readonly sseDataProcessor = inject(SSEDataProcessorService);
  private readonly enhancedSSEService = inject(EnhancedSSEService);
  private readonly sequentialRegenerationService = inject(SequentialRegenerationService);

  // Angular 19+ Signals for reactive state management
  private readonly activeCheckpoints = signal<Map<string, RegenerationCheckpointState>>(new Map());
  private readonly currentSessionKey = signal<string | null>(null);
  private readonly isProcessingEvents = signal<boolean>(false);

  // Computed signals for derived state
  public readonly activeCheckpointCount = computed(() => this.activeCheckpoints().size);
  public readonly currentSession = computed(() => {
    const sessionKey = this.currentSessionKey();
    return sessionKey ? this.activeCheckpoints().get(sessionKey) || null : null;
  });
  public readonly isActive = computed(() => this.isProcessingEvents());

  // BehaviorSubjects for observable streams
  private readonly checkpointProgress$ = new BehaviorSubject<CheckpointRegenerationProgress | null>(null);
  private readonly sessionStateChanged$ = new BehaviorSubject<RegenerationCheckpointState | null>(null);
  private readonly eventProcessingError$ = new Subject<{ sessionKey: string; error: any; eventId?: string }>();

  constructor() {
    this.initializeService();
    this.setupEventCacheIntegration();
    this.logger.info('✅ Regeneration Checkpoint Service initialized with Angular 19+ patterns');
  }

  /**
   * Initialize the service with proper lifecycle management
   */
  private initializeService(): void {
    // Set up automatic cleanup on component destruction
    this.destroyRef.onDestroy(() => {
      this.cleanup();
    });

    this.logger.info('🔧 Regeneration Checkpoint Service initialized');
  }

  /**
   * Setup integration with event cache service
   */
  private setupEventCacheIntegration(): void {
    // Subscribe to event processing notifications
    this.eventCacheService.getEventProcessed()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(eventId => {
        if (eventId) {
          this.handleEventProcessed(eventId);
        }
      });

    // Subscribe to cache updates
    this.eventCacheService.getCacheUpdates()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.updateCheckpointStates();
      });

    this.logger.info('🔗 Event cache integration setup complete');
  }

  /**
   * Start checkpoint tracking for a regeneration session
   * @param projectId The project ID
   * @param jobId The job ID
   * @returns The session key for this regeneration session
   */
  public startCheckpointSession(projectId: string, jobId: string): string {
    const sessionKey = `${projectId}-${jobId}`;
    
    const checkpointState: RegenerationCheckpointState = {
      sessionKey,
      projectId,
      jobId,
      isActive: true,
      processedEventCount: 0,
      lastProcessedEventId: null,
      lastProcessedTimestamp: null,
      completionStatus: 'pending'
    };

    // Update active checkpoints
    const currentCheckpoints = this.activeCheckpoints();
    const newCheckpoints = new Map(currentCheckpoints);
    newCheckpoints.set(sessionKey, checkpointState);
    this.activeCheckpoints.set(newCheckpoints);

    // Set as current session
    this.currentSessionKey.set(sessionKey);
    this.isProcessingEvents.set(true);

    this.logger.info('🚀 Started checkpoint session for regeneration:', {
      sessionKey,
      projectId,
      jobId,
      activeCheckpoints: newCheckpoints.size
    });

    // Emit session state change
    this.sessionStateChanged$.next(checkpointState);

    return sessionKey;
  }

  /**
   * Process an SSE event with checkpoint validation
   * @param sseEvent The SSE event to process
   * @param sessionKey The session key for this regeneration
   * @returns Observable of processed event or null if already processed
   */
  public processEventWithCheckpoint(sseEvent: any, sessionKey: string): Observable<any> {
    return new Observable(observer => {
      try {
        // Validate session
        const checkpoint = this.activeCheckpoints().get(sessionKey);
        if (!checkpoint || !checkpoint.isActive) {
          this.logger.warn('⚠️ Cannot process event for inactive session:', {
            sessionKey,
            hasCheckpoint: !!checkpoint,
            isActive: checkpoint?.isActive
          });
          observer.complete();
          return;
        }

        // Check if event has already been processed
        if (sseEvent.id && this.eventCacheService.isEventProcessed(sseEvent.id, sessionKey)) {
          this.logger.info('⏭️ Event already processed (checkpoint validation):', {
            eventId: sseEvent.id,
            sessionKey,
            eventType: sseEvent.event
          });
          observer.complete();
          return;
        }

        // Process event through SSE data processor with session context
        this.sseDataProcessor.processSSEEvent(sseEvent, sessionKey)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: (processedEvent) => {
              // Update checkpoint state
              this.updateCheckpointAfterProcessing(sessionKey, sseEvent.id, sseEvent.event);
              
              // Emit progress update
              const progress: CheckpointRegenerationProgress = {
                sessionKey,
                eventId: sseEvent.id,
                eventType: sseEvent.event,
                progress: processedEvent.progress || '',
                status: processedEvent.status || '',
                isNewEvent: true,
                processedAt: Date.now(),
                metadata: processedEvent.metadata,
                logs: processedEvent.logs
              };
              
              this.checkpointProgress$.next(progress);
              observer.next(processedEvent);
              observer.complete();
            },
            error: (error) => {
              this.logger.error('❌ Error processing event with checkpoint:', {
                sessionKey,
                eventId: sseEvent.id,
                error
              });
              
              this.eventProcessingError$.next({
                sessionKey,
                error,
                eventId: sseEvent.id
              });
              
              observer.error(error);
            }
          });

      } catch (error) {
        this.logger.error('❌ Checkpoint processing error:', error);
        observer.error(error);
      }
    });
  }

  /**
   * Complete a checkpoint session
   * @param sessionKey The session key to complete
   * @param status The completion status
   */
  public completeCheckpointSession(sessionKey: string, status: 'completed' | 'failed' | 'cancelled'): void {
    const currentCheckpoints = this.activeCheckpoints();
    const checkpoint = currentCheckpoints.get(sessionKey);

    if (!checkpoint) {
      this.logger.warn('⚠️ Cannot complete non-existent checkpoint session:', sessionKey);
      return;
    }

    // Update checkpoint state
    const updatedCheckpoint: RegenerationCheckpointState = {
      ...checkpoint,
      isActive: false,
      completionStatus: status
    };

    const newCheckpoints = new Map(currentCheckpoints);
    newCheckpoints.set(sessionKey, updatedCheckpoint);
    this.activeCheckpoints.set(newCheckpoints);

    // Clear current session if it matches
    if (this.currentSessionKey() === sessionKey) {
      this.currentSessionKey.set(null);
      this.isProcessingEvents.set(false);
    }

    this.logger.info('✅ Completed checkpoint session:', {
      sessionKey,
      status,
      processedEventCount: updatedCheckpoint.processedEventCount,
      lastProcessedEventId: updatedCheckpoint.lastProcessedEventId
    });

    // Emit session state change
    this.sessionStateChanged$.next(updatedCheckpoint);
  }

  /**
   * Get observable for checkpoint progress updates
   */
  public getCheckpointProgress(): Observable<CheckpointRegenerationProgress | null> {
    return this.checkpointProgress$.asObservable();
  }

  /**
   * Get observable for session state changes
   */
  public getSessionStateChanges(): Observable<RegenerationCheckpointState | null> {
    return this.sessionStateChanged$.asObservable();
  }

  /**
   * Get observable for event processing errors
   */
  public getEventProcessingErrors(): Observable<{ sessionKey: string; error: any; eventId?: string }> {
    return this.eventProcessingError$.asObservable();
  }

  /**
   * Get checkpoint state for a session
   * @param sessionKey The session key
   * @returns The checkpoint state or null if not found
   */
  public getCheckpointState(sessionKey: string): RegenerationCheckpointState | null {
    return this.activeCheckpoints().get(sessionKey) || null;
  }

  /**
   * Handle event processed notification from cache service
   */
  private handleEventProcessed(eventId: string): void {
    // Find which session this event belongs to and update its state
    const currentCheckpoints = this.activeCheckpoints();
    
    for (const [sessionKey, checkpoint] of currentCheckpoints.entries()) {
      if (checkpoint.isActive) {
        // Update the checkpoint state for this session
        this.updateCheckpointAfterProcessing(sessionKey, eventId, 'unknown');
        break;
      }
    }
  }

  /**
   * Update checkpoint state after processing an event
   */
  private updateCheckpointAfterProcessing(sessionKey: string, eventId: string | null, eventType: string): void {
    if (!eventId) return;

    const currentCheckpoints = this.activeCheckpoints();
    const checkpoint = currentCheckpoints.get(sessionKey);

    if (!checkpoint) return;

    const updatedCheckpoint: RegenerationCheckpointState = {
      ...checkpoint,
      processedEventCount: checkpoint.processedEventCount + 1,
      lastProcessedEventId: eventId,
      lastProcessedTimestamp: Date.now()
    };

    const newCheckpoints = new Map(currentCheckpoints);
    newCheckpoints.set(sessionKey, updatedCheckpoint);
    this.activeCheckpoints.set(newCheckpoints);

    this.logger.debug('📊 Updated checkpoint state after processing:', {
      sessionKey,
      eventId,
      eventType,
      processedEventCount: updatedCheckpoint.processedEventCount
    });
  }

  /**
   * Update checkpoint states based on cache changes
   */
  private updateCheckpointStates(): void {
    // This method can be used to sync checkpoint states with cache statistics
    const cacheStats = this.eventCacheService.stats();
    
    this.logger.debug('📊 Cache stats update:', {
      totalCached: cacheStats.totalCached,
      processedCount: cacheStats.processedCount,
      activeCheckpoints: this.activeCheckpointCount()
    });
  }

  /**
   * Clean up all resources
   */
  private cleanup(): void {
    // Complete all active sessions
    const currentCheckpoints = this.activeCheckpoints();
    for (const sessionKey of currentCheckpoints.keys()) {
      this.completeCheckpointSession(sessionKey, 'cancelled');
    }

    // Clear all state
    this.activeCheckpoints.set(new Map());
    this.currentSessionKey.set(null);
    this.isProcessingEvents.set(false);

    // Complete observables
    this.checkpointProgress$.complete();
    this.sessionStateChanged$.complete();
    this.eventProcessingError$.complete();

    this.logger.info('🧹 Regeneration Checkpoint Service cleaned up');
  }
}

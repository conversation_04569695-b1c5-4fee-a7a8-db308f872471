/**
 * Performance monitoring utility for tracking optimization improvements
 * Provides methods to measure execution times and memory usage
 */
export class PerformanceMonitor {
  private static measurements = new Map<string, PerformanceMeasurement[]>();
  private static isEnabled = true;

  /**
   * Start a performance measurement
   * @param name Unique name for the measurement
   * @returns Measurement ID for stopping the measurement
   */
  static start(name: string): string {
    if (!this.isEnabled) return '';

    const measurementId = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Use Performance API if available
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(`${measurementId}_start`);
    }

    return measurementId;
  }

  /**
   * Stop a performance measurement and record the result
   * @param measurementId ID returned from start()
   * @param additionalData Optional additional data to record
   */
  static stop(measurementId: string, additionalData?: any): PerformanceMeasurement | null {
    if (!this.isEnabled || !measurementId) return null;

    const endTime = Date.now();
    let duration = 0;

    // Use Performance API if available
    if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
      try {
        const startMark = `${measurementId}_start`;
        const endMark = `${measurementId}_end`;
        const measureName = `${measurementId}_measure`;

        performance.mark(endMark);
        performance.measure(measureName, startMark, endMark);

        const measure = performance.getEntriesByName(measureName)[0];
        duration = measure ? measure.duration : 0;

        // Clean up marks and measures
        performance.clearMarks(startMark);
        performance.clearMarks(endMark);
        performance.clearMeasures(measureName);
      } catch (error) {
        // Fallback to basic timing
        duration = 0;
      }
    }

    const measurement: PerformanceMeasurement = {
      id: measurementId,
      name: this.extractNameFromId(measurementId),
      duration: duration,
      timestamp: endTime,
      additionalData: additionalData || {}
    };

    this.recordMeasurement(measurement);
    return measurement;
  }

  /**
   * Measure the execution time of a function
   * @param name Name for the measurement
   * @param fn Function to measure
   * @param context Optional context for the function
   * @returns Result of the function execution along with measurement
   */
  static measure<T>(name: string, fn: () => T, context?: any): MeasurementResult<T> {
    const measurementId = this.start(name);
    const startTime = performance.now();

    try {
      const result = context ? fn.call(context) : fn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const measurement = this.stop(measurementId, {
        success: true,
        manualDuration: duration
      });

      return {
        result,
        measurement,
        success: true
      };
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      const measurement = this.stop(measurementId, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        manualDuration: duration
      });

      return {
        result: null as T,
        measurement,
        success: false,
        error
      };
    }
  }

  /**
   * Get performance statistics for a specific measurement name
   * @param name Name of the measurements to analyze
   * @returns Performance statistics
   */
  static getStats(name: string): PerformanceStats | null {
    const measurements = this.measurements.get(name);
    if (!measurements || measurements.length === 0) return null;

    const durations = measurements.map(m => m.duration).filter(d => d > 0);
    if (durations.length === 0) return null;

    const sum = durations.reduce((a, b) => a + b, 0);
    const avg = sum / durations.length;
    const min = Math.min(...durations);
    const max = Math.max(...durations);

    // Calculate median
    const sorted = durations.sort((a, b) => a - b);
    const median = sorted.length % 2 === 0
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)];

    // Calculate 95th percentile
    const p95Index = Math.floor(sorted.length * 0.95);
    const p95 = sorted[p95Index] || max;

    return {
      name,
      count: measurements.length,
      totalDuration: sum,
      averageDuration: avg,
      minDuration: min,
      maxDuration: max,
      medianDuration: median,
      p95Duration: p95,
      measurements: measurements.slice(-10) // Last 10 measurements
    };
  }

  /**
   * Get all performance statistics
   * @returns Map of all performance statistics
   */
  static getAllStats(): Map<string, PerformanceStats> {
    const stats = new Map<string, PerformanceStats>();

    for (const [name] of this.measurements) {
      const stat = this.getStats(name);
      if (stat) {
        stats.set(name, stat);
      }
    }

    return stats;
  }

  /**
   * Clear all measurements for a specific name
   * @param name Name of measurements to clear
   */
  static clearMeasurements(name: string): void {
    this.measurements.delete(name);
  }

  /**
   * Clear all measurements
   */
  static clearAllMeasurements(): void {
    this.measurements.clear();
  }

  /**
   * Enable or disable performance monitoring
   * @param enabled Whether to enable monitoring
   */
  static setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Check if performance monitoring is enabled
   * @returns Whether monitoring is enabled
   */
  static isMonitoringEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Get memory usage information (if available)
   * @returns Memory usage information
   */
  static getMemoryUsage(): MemoryUsage | null {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        timestamp: Date.now()
      };
    }
    return null;
  }

  /**
   * Log performance summaryconsole to
   * @param name Optional specific measurement name to log
   */
  static logSummary(name?: string): void {
    if (!this.isEnabled) return;

    if (name) {
      const stats = this.getStats(name);
      if (stats) {
      }
    } else {
      const allStats = this.getAllStats();
    }
  }

  private static recordMeasurement(measurement: PerformanceMeasurement): void {
    const name = measurement.name;

    if (!this.measurements.has(name)) {
      this.measurements.set(name, []);
    }

    const measurements = this.measurements.get(name)!;
    measurements.push(measurement);

    // Keep only the last 100 measurements per name to prevent memory leaks
    if (measurements.length > 100) {
      measurements.splice(0, measurements.length - 100);
    }
  }

  private static extractNameFromId(measurementId: string): string {
    return measurementId.split('_')[0];
  }
}

// Supporting interfaces
export interface PerformanceMeasurement {
  id: string;
  name: string;
  duration: number;
  timestamp: number;
  additionalData: any;
}

export interface PerformanceStats {
  name: string;
  count: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  medianDuration: number;
  p95Duration: number;
  measurements: PerformanceMeasurement[];
}

export interface MeasurementResult<T> {
  result: T;
  measurement: PerformanceMeasurement | null;
  success: boolean;
  error?: any;
}

export interface MemoryUsage {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

// Decorator for automatic method performance monitoring
export function MonitorPerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const measurementName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      return PerformanceMonitor.measure(measurementName, () => {
        return originalMethod.apply(this, args);
      }, this).result;
    };

    return descriptor;
  };
}

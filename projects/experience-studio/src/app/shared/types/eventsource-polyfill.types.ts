/**
 * Enhanced EventSource Types for Polyfill Support
 *
 * This file provides TypeScript type definitions for the event-source-polyfill
 * package, which extends the native EventSource API with additional features
 * like custom headers support.
 *
 * Features supported by the polyfill:
 * - Custom headers (including last-event-id)
 * - CORS with credentials
 * - Enhanced error handling
 * - Proxy support
 * - SSL configuration
 */

/**
 * Enhanced EventSource initialization options
 * Extends the standard EventSourceInit with polyfill-specific options
 */
export interface EnhancedEventSourceInit extends EventSourceInit {
  /**
   * Whether to include credentials in CORS requests
   */
  withCredentials?: boolean;

  /**
   * Custom headers to send with the request
   * This is the key feature that enables last-event-id header support
   */
  headers?: Record<string, string>;

  /**
   * Proxy configuration for the connection
   */
  proxy?: string;

  /**
   * HTTPS configuration options
   */
  https?: {
    rejectUnauthorized?: boolean;
    [key: string]: any;
  };

  /**
   * Whether to reject unauthorized SSL certificates
   */
  rejectUnauthorized?: boolean;

  /**
   * Heartbeat interval in milliseconds
   * Some polyfills support this for connection health monitoring
   */
  heartbeatTimeout?: number;
}

/**
 * Enhanced EventSource constructor interface
 * Provides type safety for the polyfill's extended functionality
 */
export interface EnhancedEventSource extends EventSource {
  /**
   * The URL used for the connection
   */
  readonly url: string;

  /**
   * The current ready state of the connection
   */
  readonly readyState: number;

  /**
   * Whether credentials are included in requests
   */
  readonly withCredentials: boolean;

  /**
   * Custom headers used for the connection
   */
  readonly headers?: Record<string, string>;
}

/**
 * Enhanced EventSource constructor type
 */
export interface EnhancedEventSourceConstructor {
  new(url: string, eventSourceInitDict?: EnhancedEventSourceInit): EnhancedEventSource;
  readonly CLOSED: number;
  readonly CONNECTING: number;
  readonly OPEN: number;
}

/**
 * Type guard to check if EventSource supports custom headers
 * ENHANCED: More robust detection of polyfill capabilities
 */
export function supportsCustomHeaders(eventSource: EventSource): eventSource is EnhancedEventSource {
  try {
    // Method 1: Check for headers property
    if ('headers' in eventSource || typeof (eventSource as any).headers !== 'undefined') {
      return true;
    }

    // Method 2: Check constructor for polyfill indicators
    const constructor = eventSource.constructor;
    if (constructor && constructor.toString().includes('headers')) {
      return true;
    }

    // Method 3: Check for polyfill-specific properties
    if ((eventSource as any).polyfill === true || (constructor as any).polyfill === true) {
      return true;
    }

    // Method 4: Check if we can access polyfill-specific methods
    if (typeof (eventSource as any).setRequestHeader === 'function') {
      return true;
    }

    return false;
  } catch (error) {
    // If any check fails, assume no polyfill support
    return false;
  }
}

/**
 * Utility type for EventSource configuration with cache ID support
 * This combines our SSE options with the enhanced polyfill capabilities
 */
export interface SSEConfigWithHeaders {
  /**
   * Base URL for the SSE connection
   */
  url: string;

  /**
   * Whether to include credentials
   */
  withCredentials?: boolean;

  /**
   * Custom headers including last-event-id for checkpointing
   */
  headers?: {
    'last-event-id'?: string;
    'Cache-Control'?: string;
    'Accept'?: string;
    'Authorization'?: string;
    [key: string]: string | undefined;
  };

  /**
   * Additional polyfill-specific options
   */
  polyfillOptions?: Omit<EnhancedEventSourceInit, 'withCredentials' | 'headers'>;
}

/**
 * EventSource factory function type
 * Provides a consistent interface for creating EventSource instances
 * with or without polyfill support
 */
export type EventSourceFactory = (
  url: string,
  config?: EnhancedEventSourceInit
) => EventSource | EnhancedEventSource;

/**
 * Default EventSource factory implementation
 * Uses the global EventSource constructor (which may be polyfilled)
 */
export const createEventSource: EventSourceFactory = (url, config) => {
  return new EventSource(url, config);
};

/**
 * Utility function to create headers object with cache ID
 * Helps build the headers object for EventSource with last-event-id support
 */
export function createSSEHeaders(cacheId?: string, additionalHeaders?: Record<string, string>): Record<string, string> {
  const headers: Record<string, string> = {
    'Cache-Control': 'no-cache',
    'Accept': 'text/event-stream',
    ...additionalHeaders
  };

  if (cacheId) {
    // Use last-event-id header name as requested
    headers['last-event-id'] = cacheId;
  }

  return headers;
}

/**
 * Configuration interface for SSE service with polyfill support
 * Extends existing SSE options with header-based capabilities
 */
export interface PolyfillSSEOptions {
  /**
   * Whether to use custom headers instead of URL parameters for last-event-id
   */
  useHeadersForEventId?: boolean;

  /**
   * Custom headers to include in the request
   */
  customHeaders?: Record<string, string>;

  /**
   * Whether to enable credentials for CORS requests
   */
  withCredentials?: boolean;

  /**
   * SSL configuration for HTTPS connections
   */
  httpsOptions?: {
    rejectUnauthorized?: boolean;
    [key: string]: any;
  };
}

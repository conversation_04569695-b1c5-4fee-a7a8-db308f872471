@use '../src/assets/styles/padding';
@use '../src/assets/styles/variables';
@use '../src/assets/styles/mixins';
@use '../src/assets/styles/animation';

* {
  font-family: 'Mulish', sans-serif !important;
  &::-webkit-scrollbar {
    width: 6px !important;
    height: 8px !important;
  }
  &::-webkit-scrollbar-button {
    display: none !important;
  }
  &::-webkit-scrollbar-track {
    background: transparent !important;
    border-radius: 4px !important;
  }
  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, rgba(0, 0, 0, 0.2)) !important;
    border-radius: 4px !important;
    transition: background 0.3s ease !important;
    &:hover {
      background: var(--scrollbar-thumb-hover, rgba(0, 0, 0, 0.4)) !important;
    }
  }

  scrollbar-width: thin !important;
  scrollbar-color: var(--scrollbar-thumb, rgba(0, 0, 0, 0.2)) transparent !important;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}
html {
  overflow: hidden !important;
  border: 0 !important;
  font-size: 13px !important;
  @media (min-width: 600px) and (max-width: 1023px) {
    font-size: 14px !important;
  }
  @media (min-width: 1024px) {
    font-size: 16px !important;
  }
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  background: {
    image: url(./assets/svgs/main-bg-light.svg);
    color: var(--background-color);
    size: cover;
    position: center center;
    repeat: no-repeat;
    attachment: fixed;
  }
  color: var(--body-text-color);
  transition: all 0.3s ease;
}

app-root {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.cursor-pointer {
  cursor: pointer;
}

.text-center {
  text-align: center;
}

.w-100 {
  width: 100%;
}

.container {
  background-color: var(--background-color);
  color: var(--body-text-color);
}

.option-card {
  border: 1.5px solid var(--card-border-color);
  background-color: var(--card-hover-bg);
  &:hover {
    background-color: var(--card-hover-bg);
  }
  &.selected {
    border-color: var(--selected-card-border);
    background-color: var(--selected-card-bg);
  }
}

.button {
  @include mixins.themeify('background-color', '--button-bg');
  @include mixins.themeify('color', '--body-text-color');
  transition: all 0.2s ease;
  &:hover {
    @include mixins.themeify('background-color', '--button-hover-bg');
  }
}

/* Monaco Editor Font Fallbacks */
.monaco-editor {
  /* Ensure Monaco Editor uses proper font fallbacks */
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace !important;
}

/* Monaco Editor Codicon Font Fallbacks */
.codicon {
  /* Provide fallback for codicon font loading issues */
  font-family: 'codicon', 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace !important;

  /* Fallback for missing codicon font */
  &::before {
    /* Use Unicode fallbacks for common codicon symbols */
    content: attr(data-fallback) !important;
  }
}

/* Monaco Editor Icon Fallbacks */
.monaco-icon-label::before,
.monaco-tree-row .monaco-icon-label::before {
  /* Provide fallback styling for Monaco Editor icons */
  font-family: 'codicon', 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace !important;

  /* Ensure icons are visible even if font fails to load */
  display: inline-block !important;
  width: 16px !important;
  height: 16px !important;
  text-align: center !important;
  line-height: 16px !important;
}
// Main dialog container
.user-biography-dialog {
  background: #ffffff;
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 540px;
  width: 100%;
  margin: 0 auto;
  
  @media (max-width: 576px) {
    margin: 1rem;
    max-width: calc(100% - 2rem);
    border-radius: 16px;
  }
}

// Dialog content
.dialog-content {
  padding: 32px;
  
  @media (max-width: 576px) {
    padding: 24px;
  }
}

// Dialog title
.dialog-title {
  font-size: 24px;
  font-weight: 600;
  color: #1F2937;
  margin: 0 0 32px 0;
  line-height: 1.2;
  
  @media (max-width: 576px) {
    font-size: 20px;
    margin-bottom: 24px;
  }
}

// Form styling
.bio-form {
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 24px;
    
    @media (max-width: 576px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
  
  .form-group {
    &.full-width {
      margin-bottom: 24px;
    }
  }
  
  .form-label {
    font-size: 14px;
    font-weight: 400;
    color: #6B7280;
    margin-bottom: 8px;
    display: block;
  }
  
  .form-control,
  .form-select {
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #1F2937;
    background-color: #ffffff;
    transition: all 0.2s ease-in-out;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    &:focus {
      border-color: #6366F1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      outline: none;
    }
    
    &::placeholder {
      color: #9CA3AF;
    }
  }
  
  .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
    padding-right: 40px;
    appearance: none;
  }
  
  .form-text {
    font-size: 12px;
    color: #9CA3AF;
    margin-top: 4px;
    line-height: 1.4;
  }
}

// Quote textarea specific styling
.quote-textarea {
  resize: vertical;
  min-height: 88px;
  line-height: 1.5;
}

// Regenerate section
.regenerate-section {
  background-color: transparent;
  padding: 0;
  margin-top: 32px;
  margin-bottom: 32px;
  
  .regenerate-label {
    font-size: 16px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 12px;
    display: block;
  }
  
  .regenerate-input-wrapper {
    position: relative;
    
    .regenerate-input {
      padding: 16px;
      padding-right: 56px;
      resize: none;
      min-height: 88px;
      border: 1px solid #E5E7EB;
      border-radius: 8px;
      font-size: 14px;
      line-height: 1.5;
      
      &:focus {
        border-color: #6366F1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }
    }
    
    .send-button {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background: #6366F1;
      border: none;
      border-radius: 6px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease-in-out;
      cursor: pointer;
      
      &:hover {
        background: #5856EB;
        transform: translateY(-1px);
      }
      
      &:active {
        transform: translateY(0);
      }
      
      svg {
        width: 16px;
        height: 16px;
        color: white;
      }
    }
  }
}

// Dialog actions
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
  
  @media (max-width: 576px) {
    flex-direction: column-reverse;
    gap: 12px;
  }
  
  .btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid;
    transition: all 0.2s ease-in-out;
    min-width: 88px;
    cursor: pointer;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    @media (max-width: 576px) {
      width: 100%;
    }
    
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
  }
  
  .btn-cancel {
    background-color: #ffffff;
    color: #6B7280;
    border-color: #D1D5DB;
    
    &:hover {
      background-color: #F9FAFB;
      border-color: #9CA3AF;
      color: #374151;
    }
  }
  
  .btn-update {
    background-color: #6366F1;
    color: #ffffff;
    border-color: #6366F1;
    
    &:hover {
      background-color: #5856EB;
      border-color: #5856EB;
    }
    
    &:active {
      background-color: #4F46E5;
      border-color: #4F46E5;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .user-biography-dialog {
    max-width: 100%;
    margin: 0.5rem;
    border-radius: 16px;
  }
}

@media (max-width: 576px) {
  .dialog-content {
    padding: 20px;
  }
  
  .bio-form .form-row {
    margin-bottom: 20px;
  }
  
  .regenerate-section {
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

// Focus states for accessibility
.form-control:focus,
.form-select:focus,
.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

// Remove unnecessary styles


// Custom scrollbar for textareas
.form-control::-webkit-scrollbar {
  width: 6px;
}

.form-control::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 3px;
}

.form-control::-webkit-scrollbar-thumb {
  background: #CBD5E1;
  border-radius: 3px;
}

.form-control::-webkit-scrollbar-thumb:hover {
  background: #94A3B8;
}
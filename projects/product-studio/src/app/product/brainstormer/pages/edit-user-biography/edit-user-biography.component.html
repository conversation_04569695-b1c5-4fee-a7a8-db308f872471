<div class="user-biography-dialog">
  <div class="dialog-content">
    <h1 class="dialog-title">Edit User Biography</h1>
    
    <form #bioForm="ngForm" class="bio-form">
      <!-- Role and Age Row -->
      <div class="form-row">
        <div class="form-group">
          <label for="role" class="form-label">Role :</label>
          <input 
            type="text" 
            id="role" 
            name="role" 
            class="form-control" 
            [(ngModel)]="formData.role"
            value="Sales Manager">
        </div>
        <div class="form-group">
          <label for="age" class="form-label">Age :</label>
          <input 
            type="text" 
            id="age" 
            name="age" 
            class="form-control" 
            [(ngModel)]="formData.age"
            value="38">
        </div>
      </div>

      <!-- Education -->
      <div class="form-group full-width">
        <label for="education" class="form-label">Education :</label>
        <input 
          type="text" 
          id="education" 
          name="education" 
          class="form-control" 
          [(ngModel)]="formData.education"
          value="MBA">
      </div>

      <!-- Status and Tech Literacy Row -->
      <div class="form-row">
        <div class="form-group">
          <label for="status" class="form-label">Status :</label>
          <input 
            type="text" 
            id="status" 
            name="status" 
            class="form-control" 
            [(ngModel)]="formData.status"
            value="Married">
        </div>
        <div class="form-group">
          <label for="techLiteracy" class="form-label">Tech Literacy :</label>
          <select 
            id="techLiteracy" 
            name="techLiteracy" 
            class="form-select" 
            [(ngModel)]="formData.techLiteracy">
            <option value="Low">Low</option>
            <option value="Medium" selected>Medium</option>
            <option value="High">High</option>
          </select>
        </div>
      </div>

      <!-- Quote -->
      <div class="form-group full-width">
        <label for="quote" class="form-label">Quote :</label>
        <textarea 
          id="quote" 
          name="quote" 
          class="form-control quote-textarea" 
          rows="4" 
          [(ngModel)]="formData.quote">I am used to with online service and I usually do my online shopping from</textarea>
      </div>

      <!-- Personality -->
      <div class="form-group full-width">
        <label for="personality" class="form-label">Personality :</label>
        <input 
          type="text" 
          id="personality" 
          name="personality" 
          class="form-control" 
          [(ngModel)]="formData.personality"
          value="Introvert, Thinker, Spender, Tech-savy">
        <div class="form-text">Enter the personalities, separating with a comma</div>
      </div>

      <!-- Regenerate Section -->
      <div class="regenerate-section">
        <label class="regenerate-label">Regenerate</label>
        <div class="regenerate-input-wrapper">
          <textarea 
            class="form-control regenerate-input" 
            rows="3"
            placeholder="What would you like to change?"></textarea>
          <button type="button" class="send-button">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m3 3 3 9-3 9 19-9Z"/>
              <path d="m6 12h16"/>
            </svg>
          </button>
        </div>
      </div>
    </form>

    <!-- Dialog Actions -->
    <div class="dialog-actions">
      <button type="button" class="btn btn-cancel" (click)="onCancel()">
        Cancel
      </button>
      <button type="button" class="btn btn-update" (click)="onUpdate()">
        Update
      </button>
    </div>
  </div>
</div>
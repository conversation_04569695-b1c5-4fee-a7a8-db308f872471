import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

// This interface can be shared or defined here
export interface UserBiography {
  role: string;
  age: number;
  education: string;
  status: string;
  techLiteracy: string;
  quote: string;
  personality: string; // We'll handle personality as a comma-separated string in the form
}

@Component({
  selector: 'app-edit-user-biography-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule
  ],
  templateUrl: './edit-user-biography.component.html',
  styleUrls: ['./edit-user-biography.component.scss']
})
export class EditUserBiographyDialogComponent {
  // Create a mutable copy of the data to avoid changing the original until 'Update' is clicked
  formData: UserBiography;
  sendIcon: string = '/icons/send.svg'; // Path to your send icon

  constructor(
    public dialogRef: MatDialogRef<EditUserBiographyDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: UserBiography
  ) {
    this.formData = { ...data }; // Clone the passed data
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onUpdate(): void {
    // Pass the updated data back to the parent component
    this.dialogRef.close(this.formData);
  }
}
// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap");

// Letter spacing utility (Bootstrap doesn't have this)
.ls-1 {
  letter-spacing: 1px;
}
// Section Headers
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: auto;
  padding: 16px; // Adds space from the left and right edges
  background: linear-gradient(295deg, #ffadc8 9.05%, #437ff6 50.58%);
  border-radius: 12px 12px 0px 0px;
  .section-title {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 1px;
  }
  .section-action {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 16px;
    color: #2f68c5;
    padding: 16px;
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
  }
}

// Section-specific button colors
.add-more-button {
  background: #989898;
  display: flex;
  height: 48px;
  padding: 8px 133px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;

  button {
    // background: #989898;
    color: #fff;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;

    span {
      color: #fff;
      font-family: Mulish;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
}

// CDK Drag and Drop animations - Smoother
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.feature-list.cdk-drop-list-dragging .feature-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

// Card hover effects
.feature-card {
  // transition: all 0.2s ease, cursor 0.1s ease;
  background: #fbfbfb;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.25);
  min-height: 236px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }

  &:active {
    cursor: grabbing !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
  }
}

button {
  min-width: 0px;
  border: none;
  background: none;
}

.border-buttom {
  border-bottom: 1px solid #303233;
}

// Custom dropdown styling
.dropdown {
  position: relative;
  height: 44px;

  .dropdown-menu {
    display: none;
    position: absolute;
    top: 35%;
    right: 70%;
    min-width: 120px;
    background-color: #fff;
    // border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    // padding: 0.5rem 0;
    z-index: 1000;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      width: 100%;
      // border-bottom: 1px solid #303233;
      // padding: 0.375rem 1rem;
      clear: both;
      font-weight: 400;
      color: #212529;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      // border: 0;
      transition: background-color 0.15s ease-in-out;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }
    }
  }
}
// Progress bar styling
.progress {
  background-color: #e9ecef;
  border-radius: 3px;
  height: 6px;

  .progress-bar {
    border-radius: 3px;
    transition: width 0.3s ease;
  }
}

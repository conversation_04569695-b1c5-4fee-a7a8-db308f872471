<div class="bg-light min-vh-100">
  <div class="container-fluid p-3 p-md-4">
    <!-- TOP HEADER BAR -->
    <header
      class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center mb-4 gap-3">
      <button class="btn btn-outline-secondary d-flex align-items-center" (click)="goToPrevious()">
        <i class="bi bi-arrow-left me-2"></i>
        <span class="d-none d-sm-inline">Persona List</span>
      </button>

      <!-- Custom Dropdown Wrapper -->
      <div class="position-relative">
        <button class="btn btn-light border dropdown-toggle d-flex align-items-center" type="button" (click)="togglePersonaSelector()">
          <img [src]="selectedPersona.avatar" alt="avatar" class="rounded-circle me-2" style="width: 24px; height: 24px; object-fit: cover;">
          <span>{{ selectedPersona.role }}</span>
        </button>

        <!-- Custom Persona Selector Card (The new dropdown) -->
        <div *ngIf="isPersonaSelectorOpen" class="persona-selector-card">
          <awe-card cardClass="shadow-lg border-light" [applyHeaderPadding]="false" [applyBodyPadding]="false">
            <div class="p-3 border-bottom">
              <h2 class="h6 fw-bold mb-0">User Persona</h2>
            </div>
            
            <div class="p-2">
              <button *ngFor="let p of personas; let i = index" 
                      (click)="selectPersona(i)" 
                      class="list-group-item list-group-item-action border-0 rounded-3 mb-1 persona-item">
                <div class="d-flex align-items-center">
                  <img [src]="p.avatar" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                  <div>
                    <div class="fw-bold text-dark">{{ p.name }}</div>
                    <div class="text-muted small">{{ p.role }}</div>
                  </div>
                </div>
              </button>
            </div>
            
            <!-- Enlarge Icon Button -->
            <div class="position-absolute bottom-0 end-0 p-2">
               <button class="btn btn-light rounded-circle p-2 enlarge-btn d-flex align-items-center justify-content-center">
                  <img [src]="InlargeIcon" alt="Enlarge" style="width: 20px; height: 20px;">
               </button>
            </div>
          </awe-card>
        </div>
      </div>
    </header>

    <!-- MAIN CONTENT GRID -->
    <div class="row g-3 g-lg-4">
      <!-- LEFT COLUMN: PERSONA SUMMARY CARD -->
      <div class="col-12 col-lg-4 col-xl-3">
        <awe-card cardClass="h-100 position-relative border-0 shadow-sm" [applyHeaderPadding]="false"
          [applyBodyPadding]="false" data-section="profile">

          <!-- Edit Button -->
          <button class="btn btn-sm btn-link text-muted position-absolute top-0 end-0 m-2 p-1"
      (click)="openEditProfileDialog()">
      <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
    </button>

         

          <!-- Avatar and Role -->
          <div class="text-center p-3 pb-2">
            <img [src]="selectedPersona.avatar" [alt]="selectedPersona.role + ' avatar'"
              class="rounded-circle mb-3 border border-3"
              style="width: 90px; height: 90px; object-fit: cover; border-color: #e8e6f5 !important;">
            <h1 class="h5 fw-bold text-primary mb-0" data-editable="true">{{ selectedPersona.role }}</h1>
          </div>

          <!-- Profile Information -->
          <div class="px-3 pb-2">
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom border-light">
              <span class="text-muted small">Age</span>
              <span class="fw-medium small" data-editable="true">{{ selectedPersona.age }}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom border-light">
              <span class="text-muted small">Education</span>
              <span class="fw-medium small" data-editable="true">{{ selectedPersona.education }}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom border-light">
              <span class="text-muted small">Status</span>
              <span class="fw-medium small" data-editable="true">{{ selectedPersona.status }}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom border-light">
              <span class="text-muted small">Location</span>
              <span class="fw-medium small" data-editable="true">{{ selectedPersona.location }}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2">
              <span class="text-muted small">Tech Literacy</span>
              <span class="fw-medium small" data-editable="true">{{ selectedPersona.techLiteracy }}</span>
            </div>
          </div>

          <!-- Quote Section -->
          <div class="p-3 pt-2">
            <div class="p-3 rounded-3 position-relative"
              style="background-color: #f7f7fe; border-left: 3px solid #e8e6f5;">
              <div class="d-flex align-items-start gap-2">
                <img [src]="colon" alt=":" class="mx-1" style="width: 8px; height: 8px;">
                <p class="mb-0 text-muted fst-italic small quote-text">{{ selectedPersona.quote }}</p>
              </div>
            </div>
          </div>

          <!-- Personality Tags -->
          <div class="px-3 pb-3">
            <p class="small text-muted mb-2 fw-medium">Personality</p>
            <div class="d-flex flex-wrap gap-2">
              <span *ngFor="let trait of selectedPersona.personality"
                class="badge rounded-pill bg-light text-dark border fw-normal px-3 py-2 small">
                {{ trait }}
              </span>
            </div>
          </div>
        </awe-card>
      </div>

      <!-- RIGHT COLUMN: DETAIL CARDS -->
      <div class="col-12 col-lg-8 col-xl-9">
        <div class="row g-3 g-lg-4">
          <!-- CARD: Pain Points -->
          <div class="col-12 col-xl-6">
            <awe-card cardClass="h-100 border-0 shadow-sm" data-section="painPoints">
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h2 class="h6 mb-0 fw-semibold">Pain Points</h2>
                <button class="btn btn-sm btn-link text-muted p-1" 
                  *ngIf="!editStates['painPoints']">
                  <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
                </button>
                <div class="d-flex gap-1" *ngIf="editStates['painPoints']">
                  <button class="btn btn-sm btn-success px-2 py-1" >
                    <i class="bi bi-check-lg"></i>
                  </button>
                  <button class="btn btn-sm btn-secondary px-2 py-1" >
                    <i class="bi bi-x-lg"></i>
                  </button>
                </div>
              </div>
              <ul class="list-unstyled mb-0 small pt-2">
                <li *ngFor="let point of selectedPersona.painPoints" class="d-flex align-items-start mb-2">
                  <span class="me-2 text-muted">•</span>
                  <span class="list-item-text">{{ point }}</span>
                </li>
              </ul>
            </awe-card>
          </div>

          <!-- CARD: Goals -->
          <div class="col-12 col-xl-6">
            <awe-card cardClass="h-100 border-0 shadow-sm" data-section="goals">
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h2 class="h6 mb-0 fw-semibold">Goals</h2>
                <button class="btn btn-sm btn-link text-muted p-1" 
                  *ngIf="!editStates['goals']">
                  <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
                </button>
                <div class="d-flex gap-1" *ngIf="editStates['goals']">
                  <button class="btn btn-sm btn-success px-2 py-1" >
                    <i class="bi bi-check-lg"></i>
                  </button>
                  <button class="btn btn-sm btn-secondary px-2 py-1" >
                    <i class="bi bi-x-lg"></i>
                  </button>
                </div>
              </div>
              <ul class="list-unstyled mb-0 small pt-2">
                <li *ngFor="let goal of selectedPersona.goals" class="d-flex align-items-start mb-2">
                  <span class="me-2 text-muted">•</span>
                  <span class="list-item-text">{{ goal }}</span>
                </li>
              </ul>
            </awe-card>
          </div>

          <!-- CARD: Motivation -->
          <div class="col-12 col-xl-6">
            <awe-card cardClass="h-100 border-0 shadow-sm" data-section="motivation">
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h2 class="h6 mb-0 fw-semibold">Motivation</h2>
                <button class="btn btn-sm btn-link text-muted p-1" 
                  *ngIf="!editStates['motivation']">
                  <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
                </button>
                <div class="d-flex gap-1" *ngIf="editStates['motivation']">
                  <button class="btn btn-sm btn-success px-2 py-1" >
                    <i class="bi bi-check-lg"></i>
                  </button>
                  <button class="btn btn-sm btn-secondary px-2 py-1" >
                    <i class="bi bi-x-lg"></i>
                  </button>
                </div>
              </div>
              <ul class="list-unstyled mb-0 small pt-2">
                <li *ngFor="let item of selectedPersona.motivation" class="d-flex align-items-start mb-2">
                  <span class="me-2 text-muted">•</span>
                  <span class="list-item-text">{{ item }}</span>
                </li>
              </ul>
            </awe-card>
          </div>

          <!-- CARD: Expectations -->
          <div class="col-12 col-xl-6">
            <awe-card cardClass="h-100 border-0 shadow-sm" data-section="expectations">
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h2 class="h6 mb-0 fw-semibold">Expectations</h2>
                <button class="btn btn-sm btn-link text-muted p-1" 
                  *ngIf="!editStates['expectations']">
                  <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
                </button>
                <div class="d-flex gap-1" *ngIf="editStates['expectations']">
                  <button class="btn btn-sm btn-success px-2 py-1" >
                    <i class="bi bi-check-lg"></i>
                  </button>
                  <button class="btn btn-sm btn-secondary px-2 py-1" >
                    <i class="bi bi-x-lg"></i>
                  </button>
                </div>
              </div>
              <ul class="list-unstyled mb-0 small pt-2">
                <li *ngFor="let item of selectedPersona.expectations" class="d-flex align-items-start mb-2">
                  <span class="me-2 text-muted">•</span>
                  <span class="list-item-text">{{ item }}</span>
                </li>
              </ul>
            </awe-card>
          </div>

          <!-- CARD: Skills -->
          <div class="col-12 col-xl-6">
            <awe-card cardClass="h-100 border-0 shadow-sm" data-section="skills">
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h2 class="h6 mb-0 fw-semibold">Skills</h2>
                <button class="btn btn-sm btn-link text-muted p-1" 
                  *ngIf="!editStates['skills']">
                  <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
                </button>
                <div class="d-flex gap-1" *ngIf="editStates['skills']">
                  <button class="btn btn-sm btn-success px-2 py-1" >
                    <i class="bi bi-check-lg"></i>
                  </button>
                  <button class="btn btn-sm btn-secondary px-2 py-1" >
                    <i class="bi bi-x-lg"></i>
                  </button>
                </div>
              </div>
              <div class="pt-2">
                <div *ngFor="let skill of selectedPersona.skills; let i = index" class="mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="small text-muted skill-name">{{ skill.name }}</span>
                    <span class="small text-muted">{{ skill.level }}%</span>
                  </div>
                  <div class="progress" style="height: 8px; background-color: #e8e6f5;">
                    <div class="progress-bar" [ngClass]="getProgressBarClass(i)" role="progressbar"
                      [style.width.%]="skill.level" [attr.aria-valuenow]="skill.level" aria-valuemin="0"
                      aria-valuemax="100">
                    </div>
                  </div>
                </div>
              </div>
            </awe-card>
          </div>

          <!-- CARD: Devices -->
          <div class="col-12 col-xl-6">
            <awe-card cardClass="h-100 border-0 shadow-sm d-flex flex-column" data-section="devices">
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h2 class="h6 mb-0 fw-semibold">Devices</h2>
                <button class="btn btn-sm btn-link text-muted p-1" 
                  *ngIf="!editStates['devices']">
                  <img [src]="PencilEditIcon" alt="Edit" width="16" height="16">
                </button>
                <div class="d-flex gap-1" *ngIf="editStates['devices']">
                  <button class="btn btn-sm btn-success px-2 py-1" >
                    <i class="bi bi-check-lg"></i>
                  </button>
                  <button class="btn btn-sm btn-secondary px-2 py-1" >
                    <i class="bi bi-x-lg"></i>
                  </button>
                </div>
              </div>
              <div class="flex-grow-1 d-flex justify-content-center align-items-center gap-4 pt-3">
                <div *ngIf="selectedPersona.devices.includes('mobile')" class="text-center">
                  <img [src]="this.MobileIcon" alt="Mobile" width="48" height="48">
                  <div class="small text-muted mt-1">Mobile</div>
                </div>
                <div *ngIf="selectedPersona.devices.includes('laptop')" class="text-center">
                  <img [src]="this.LaptopIcon" alt="Laptop" width="48" height="48">
                  <div class="small text-muted mt-1">Laptop</div>
                </div>
              </div>
            </awe-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
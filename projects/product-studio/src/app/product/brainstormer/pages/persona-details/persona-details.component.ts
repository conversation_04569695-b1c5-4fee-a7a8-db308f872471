import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AweCardComponent } from "../../components/awe-card/awe-card.component";
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { EditUserBiographyDialogComponent } from "../../pages/edit-user-biography/edit-user-biography.component";

interface PersonaData {

  role: string;
  age: number;
  education: string;
  status: string;
  location: string;
  techLiteracy: string;
  quote: string;
  personality: string[];
  painPoints: string[];
  goals: string[];
  motivation: string[];
  expectations: string[];
  skills: { name: string; level: number }[];
  devices: string[];
  avatar: string;
  name: string;
}

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [CommonModule, AweCardComponent,MatDialogModule,EditUserBiographyDialogComponent],
  templateUrl: './persona-details.component.html',
  styleUrls: ['./persona-details.component.scss']
})
export class PersonaDetailsComponent {
  PencilEditIcon: string = '/icons/pencil-edit.svg';
  MobileIcon: string = '/icons/mobile-icon-dark.svg';
  LaptopIcon: string = '/icons/web-icon-dark.svg';
  salesAvatar: string = '/svgs/sales-avatar.svg';
  managerAvatar: string = '/svgs/manager-avatar.svg';
  developerAvatar: string = '/svgs/developer-avatar.svg';
  teacherAvatar: string = '/svgs/teacher-avatar.svg';
  designerAvatar: string = '/svgs/designer-avatar.svg';
  colon: string = '/svgs/colon.svg'
  dropdownIcon: string = '/svgs/dropdown-icon.svg';
  inlargeIcon: string = '/svgs/inlarge-icon.svg';
  editStates: Record<string, boolean> = {};
  originalData: Record<string, string> = {};

  public isPersonaSelectorOpen: boolean = false;
  InlargeIcon = this.inlargeIcon;
  DropdownIcon = this.dropdownIcon;

  personas: PersonaData[] = [
    {
      name: 'Debbie Perkins', 
      role: 'Sales Manager',
      age: 38,
      education: 'MBA',
      status: 'Married',
      location: 'Mumbai',
      techLiteracy: 'Medium',
      avatar: this.managerAvatar,
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      painPoints: [
        'Faces PIN issues when using ATMs',
        'Often forgets passwords for online banking',
        'Concerned about online fraud while shopping'
      ],
      goals: [
        'Fast and easy payments without carrying cash',
        'Secure online transactions',
        'Budget-friendly banking solutions'
      ],
      motivation: [
        'Wants a simple, secure, and student-friendly debit card',
        'Prefers biometric authentication over remembering PINs',
        'Seeks a smooth online shopping experience'
      ],
      expectations: [
        'Seamless biometric authentication for fast, PIN-free transactions.',
        'Secure online payments with fraud protection',
        'User-friendly mobile banking for tracking spending.'
      ],
      skills: [
        { name: 'Digital Marketing', level: 85 },
        { name: 'Customer Relations', level: 70 },
        { name: 'Data Analysis', level: 60 }
      ],
      devices: ['mobile', 'laptop']
    },
    {
      name: 'Donald Terry',
      role: 'Designer',
      age: 27,
      education: 'M.Des',
      status: 'Single',
      location: 'Abu Dhabi',
      techLiteracy: 'High',
      avatar: this.designerAvatar,
      quote: 'I love creating beautiful and functional designs that users enjoy.',
      personality: ['Creative', 'Detail-oriented', 'Collaborative'],
      painPoints: [
        'Struggles with complex financial interfaces',
        'Needs visual feedback for transactions',
        'Wants seamless design tools integration'
      ],
      goals: [
        'Streamlined payment workflows',
        'Visual transaction confirmations',
        'Creative-friendly banking features'
      ],
      motivation: [
        'Seeks aesthetically pleasing interfaces',
        'Values user experience consistency',
        'Wants efficient creative workflow support'
      ],
      expectations: [
        'Beautiful, intuitive interface design',
        'Quick visual feedback on transactions',
        'Integration with creative tools and platforms'
      ],
      skills: [
        { name: 'UI/UX Design', level: 90 },
        { name: 'Creative Software', level: 85 },
        { name: 'User Research', level: 75 }
      ],
      devices: [this.MobileIcon, this.LaptopIcon]
    }
  ];
  selectedPersonaIndex: number = 0;

  get selectedPersona(): PersonaData {
    return this.personas[this.selectedPersonaIndex];
  }

  constructor(private router: Router, public dialog: MatDialog) { }

  openEditProfileDialog(): void {
    const dialogRef = this.dialog.open(EditUserBiographyDialogComponent, {
      width: 'clamp(300px, 90vw, 650px)', // Responsive width
      autoFocus: false, // Prevents auto-focus on the first form field
      data: {
        // Pass a copy of the data to the dialog
        role: this.selectedPersona.role,
        age: this.selectedPersona.age,
        education: this.selectedPersona.education,
        status: this.selectedPersona.status,
        techLiteracy: this.selectedPersona.techLiteracy,
        quote: this.selectedPersona.quote,
        // Convert personality array to a comma-separated string for the input field
        personality: this.selectedPersona.personality.join(', ')
      },
      panelClass: 'custom-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const personaToUpdate = this.personas[this.selectedPersonaIndex];
        Object.assign(personaToUpdate, {
          ...result,
          // Convert the personality string back to an array of strings, filtering out empty values
          personality: result.personality.split(',').map((p: string) => p.trim()).filter(Boolean)
        });
      }
    });
  }
  // Toggles the custom dropdown's visibility
  togglePersonaSelector(): void {
    this.isPersonaSelectorOpen = !this.isPersonaSelectorOpen;
  }

  // Selects a persona and closes the dropdown
  selectPersona(index: number): void {
    this.selectedPersonaIndex = index;
    this.isPersonaSelectorOpen = false;
  }
  goToPrevious(): void {
    this.router.navigate(['/product/brainstormer/user-persona']);
  }

  selectAvatar(index: number): void {
    document.querySelectorAll('.avatar').forEach((avatar, i) => {
      avatar.classList.toggle('selected', i === index);
    });
  }
  getProgressBarClass(index: number): string {
    const classes = ['bg-primary', 'bg-info', 'bg-success'];
    return classes[index % classes.length];
  }
}

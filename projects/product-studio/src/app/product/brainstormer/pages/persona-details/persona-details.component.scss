// Set a light background for the entire page to match the design
:host {
  background-color: #f8f9fa;
  display: block;
  min-height: 100vh;
}


// Style for the tiny avatar in the top-right dropdown
.persona-dropdown-avatar {
  width: 24px;
  height: 24px;
  object-fit: cover;
}

// Main avatar on the left summary card
.persona-avatar-img {
  width: 90px;
  height: 90px;
  object-fit: cover;
  border: 4px solid #f0f0ff; // Light purple border
}

// Styling for the quote block on the left card
.quote-block {
  background-color: #f7f7fe;
  border-left: 3px solid #e8e6f5 !important; // Use important to override bootstrap border color
}

// Style the quote icon for better alignment
.quote-icon {
  font-size: 1.5rem;
  line-height: 1;
  position: relative;
  top: -4px;
}

// Set custom background/border for all detail cards on the right.
// ::ng-deep pierces component encapsulation to style the inner wrapper of awe-card.
::ng-deep .detail-card .awe-card-wrapper {
  background-color: #f7f7fe;
  border-color: #e8e6f5;
}

// Custom colors for the skill progress bars
.progress-bar {
  &.bg-primary {
    background: linear-gradient(90deg, #6c56f7 0%, #c869f7 100%) !important;
  }

  &.bg-info {
    background: linear-gradient(90deg, #6c56f7 0%, #a869f7 100%) !important;
  }

  &.bg-success {
    background: linear-gradient(90deg, #6c56f7 0%, #c869f7 100%) !important;
  }
}

.edit-mode {
  .form-control {
    border-color: #6c56f7;
    box-shadow: 0 0 0 0.2rem rgba(108, 86, 247, 0.25);
  }
}

// Styling for the device icons
.device-icon {
  color: #8b80d7; // Custom purple-gray color

  svg {
    width: 48px;
    height: 48px;
  }

  /* styles.scss */
  .custom-dialog-container .mat-mdc-dialog-container .mdc-dialog__surface {
    padding: 0;
    border-radius: 1rem; // Match the card style
  }
}
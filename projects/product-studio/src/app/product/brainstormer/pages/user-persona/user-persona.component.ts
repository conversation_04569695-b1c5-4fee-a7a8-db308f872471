import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserPersona } from '../../models/user-persona.model';
import { AweCardComponent } from "../../components/awe-card/awe-card.component";

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [
    CommonModule,
    AweCardComponent
],
  templateUrl: './user-persona.component.html',
  styleUrls: ['./user-persona.component.scss']
})
export class UserPersonaComponent {
  trashIcon: string = '/icons/awe_trash.svg';
  salesAvatar: string = '/svgs/sales-avatar.svg';
  managerAvatar: string = '/svgs/manager-avatar.svg';
  developerAvatar: string = '/svgs/developer-avatar.svg';
  teacherAvatar: string = '/svgs/teacher-avatar.svg';
  designerAvatar: string = '/svgs/designer-avatar.svg';
  colon:string = '/svgs/colon.svg'
  personas: UserPersona[] = [
    {
      role: 'Sales Manager',
      age: 38,
      education: 'MBA',
      status: 'Married',
      location: 'Mumbai',
      techLiteracy: 'Medium',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      avatar: this.managerAvatar,
    },
    {
      role: 'Designer',
      age: 27,
      education: 'M.Des',
      status: 'Single',
      location: 'Abu Dhabi',
      techLiteracy: 'Medium',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      avatar: this.designerAvatar
    },
    {
      role: 'Teacher',
      age: 38,
      education: 'MBA',
      status: 'Married',
      location: 'Melbourne',
      techLiteracy: 'High',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      avatar: this.teacherAvatar
    },
    {
      role: 'Developer',
      age: 30,
      education: 'MCA',
      status: 'Single',
      location: 'Qatar',
      techLiteracy: 'High',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      avatar:this.developerAvatar
    },
    {
      role: 'Sales Manager',
      age: 38,
      education: 'MBA',
      status: 'Married',
      location: 'Mumbai',
      techLiteracy: 'Medium',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      avatar: this.managerAvatar,
    }
  ];

  ngOnInit(): void {
    // Initialization logic if needed
  }

  removePersona(persona: UserPersona): void {
    const index = this.personas.indexOf(persona);
    if (index > -1) {
      this.personas.splice(index, 1);
    }
  }
  trackByPersona(index: number, persona: any): any {
    return persona.id || index;
  }
}
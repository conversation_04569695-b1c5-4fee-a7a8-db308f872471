:host {
  display: block;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}

// Custom badge colors to match the design
.badge {
  &.bg-success {
    background-color: #d4edda !important;
    
  }
  
  &.bg-info {
    background-color: #d1ecf1 !important;
    
  }
  
  &.bg-warning {
    background-color: #fff3cd !important;
    
  }
}

// Ensure cards have consistent height
awe-card {
  display: flex;
  flex-direction: column;
}

// Quote styling improvements
.bg-light {
  border-left: 3px solid #007bff;
}

// Responsive font adjustments
@media (max-width: 576px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  h5 {
    font-size: 1rem;
  }
}
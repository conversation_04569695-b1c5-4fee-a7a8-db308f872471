<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12 d-flex justify-content-between align-items-center">
      <h1 class="mb-0">User Personas</h1>
      <button class="btn btn-primary">
        Add new +
      </button>
    </div>
  </div>

  <!-- Personas Grid -->
  <div class="row g-4">
    <div class="col-12 col-md-6 col-lg-4 col-xl-3" *ngFor="let persona of personas; trackBy: trackByPersona">
      <awe-card 
        [showHeader]="true" 
        [showBody]="true" 
        [showFooter]="false"
        [applyHeaderPadding]="true"
        [applyBodyPadding]="true"
        class="h-100">
        
        <!-- Card Header -->
        <div awe-card-header-content class="d-flex justify-content-between align-items-center">
          <div class="d-flex flex-column align-items-center flex-grow-1">
            <img [src]="persona.avatar" 
                 [alt]="persona.role" 
                 class="rounded-circle mb-2"
                 style="width: 60px; height: 60px; object-fit: cover;">
            <h5 class="text-primary mb-0 text-center">{{ persona.role }}</h5>
          </div>
          <button class="btn btn-link p-0 ms-2" 
                  (click)="removePersona(persona)"
                  aria-label="Delete persona">
            <img [src]="trashIcon" 
                 alt="Delete" 
                 style="width: 16px; height: 16px;">
          </button>
        </div>

        <!-- Card Body -->
        <div awe-card-body-content>
          <!-- Personal Information -->
          <div class="mb-3">
            <div class="row g-2">
              <div class="col-6">
                <div class="d-flex align-items-center">
                  <small class="text-muted me-1">Age</small>
                  <small class="fw-semibold">{{ persona.age }}</small>
                </div>
              </div>
              <div class="col-6">
                <div class="d-flex align-items-center">
                  <small class="text-muted me-1">Education</small>
                  <small class="fw-semibold">{{ persona.education }}</small>
                </div>
              </div>
              <div class="col-6">
                <div class="d-flex align-items-center">
                  <small class="text-muted me-1">Status</small>
                  <small class="fw-semibold">{{ persona.status }}</small>
                </div>
              </div>
              <div class="col-6">
                <div class="d-flex align-items-center">
                  <small class="text-muted me-1">Location</small>
                  <small class="fw-semibold">{{ persona.location }}</small>
                </div>
              </div>
              <div class="col-12">
                <div class="d-flex align-items-center">
                  <small class="text-muted me-1">Tech Literacy</small>
                  <small class="fw-semibold">{{ persona.techLiteracy }}</small>
                </div>
              </div>
            </div>
          </div>

          <!-- Quote Section -->
          <div class="mb-3">
            <div class="bg-light p-2 rounded position-relative">
              <div class="d-flex">
                <div class="text-primary me-2" style="font-size: 1.2em;"></div>
                                  
                
                <img [src]="colon" alt=":" class="mx-1" style="width: 8px; height: 8px;">

                <small class="text-muted flex-grow-1">{{ persona.quote }}</small>
              </div>
            </div>
          </div>

          <!-- Personality Tags -->
          <div>
            <small class="text-muted d-block mb-2">Personality</small>
            <div class="d-flex flex-wrap gap-1">
              <span *ngFor="let trait of persona.personality" 
                    class="badge rounded-pill"
                    [ngClass]="{
                      'bg-success': trait === 'Introvert',
                      'bg-info': trait === 'Thinker', 
                      'bg-warning': trait === 'Spender'
                    }">
                {{ trait }}
              </span>
            </div>
          </div>
        </div>
      </awe-card>
    </div>
  </div>
</div>
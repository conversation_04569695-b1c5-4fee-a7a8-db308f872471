<div class="brainstorming-container p-4">
  <!-- <PERSON> Header -->
  <!-- <div class="page-header mb-4">
    <h1 class="page-title mb-1">{{ currentStep?.label || 'Brainstorming' }}</h1>
  </div> -->

  <!-- Custom Stepper -->
  <section class="stepper-section mb-4">
    <app-brainstormer-stepper
      [showDescriptions]="false"
      [showProgressBar]="false"
      [allowStepClick]="false"
      (stepChanged)="onStepChanged($event)"
    >
    </app-brainstormer-stepper>
  </section>

  <section class="m-3">
    <div class="sub-header">
      <h2 class="sub-header-title">
        {{ currentStep?.label || "Brainstorming" }}
      </h2>

      <div class="sub-header-actions">
        <button (click)="previousStep()" [disabled]="!canGoPrevious">
          <awe-icons class="next-icon" iconName="awe_chevron_left"></awe-icons>
        </button>
        <button (click)="nextStep()" [disabled]="!canGoNext">
          <awe-icons class="next-icon" iconName="awe_chevron_right"></awe-icons>
        </button>
      </div>
    </div>
  </section>

  <!-- AI Assistant Panel -->
  <!-- <div class="left-panel" *ngIf="isLeftPanelOpen">
    <app-ai-assistant></app-ai-assistant>
  </div> -->

  <!-- Dynamic Component Content -->
  <div class="content-container">
    <!-- Understanding Component -->
    <div *ngIf="shouldShowComponent('understanding')" class="step-content">
      <app-understanding></app-understanding>
    </div>

    <!-- User Persona Component -->
    <div *ngIf="shouldShowComponent('persona')" class="step-content">
      <app-user-persona></app-user-persona>
    </div>

    <!-- Feature List Component -->
    <div *ngIf="shouldShowComponent('features')" class="step-content">
      <app-feature-list></app-feature-list>
    </div>

    <!-- SWOT Analysis Component (Placeholder) -->
    <div *ngIf="shouldShowComponent('swot')" class="step-content">
      <app-swot-analysis></app-swot-analysis>
    </div>

    <!-- Product Roadmap Component (Placeholder) -->
    <div *ngIf="shouldShowComponent('roadmap')" class="step-content">
      <app-product-roadmap></app-product-roadmap>
    </div>
  </div>

  <!-- Navigation Buttons -->
  <div class="action-buttons">
    <!-- AI Assistant Button -->
    <button class="fab-button" (click)="toggleLeftPanel()">
      <img [src]="roboBallIcon" alt="AI Assistant" loading="eager" />
    </button>

    <!-- Navigation Buttons -->
    <div class="navigation-buttons">
      <awe-button
        class="nav-button"
        label="Previous"
        state="warning"
        (click)="previousStep()"
        [disabled]="!canGoPrevious"
      >
      </awe-button>
      <awe-button
        class="nav-button"
        icon="arrow-right"
        state="danger"
        (click)="nextStep()"
        [disabled]="!canGoNext"
      >
      </awe-button>
    </div>
  </div>
</div>

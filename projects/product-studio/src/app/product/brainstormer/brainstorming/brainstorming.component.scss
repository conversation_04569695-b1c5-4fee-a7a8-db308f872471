.brainstorming-container {
  // height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;

  .page-header {
    text-align: center;

    .page-title {
      font-size: 28px;
      font-weight: bold;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .page-description {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .sub-header {
    display: flex;
    height: 54px;
    padding: 9px 26px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 12px;
    background: linear-gradient(90deg, #8b5cf6 0%, #3c82f6 100%);

    .sub-header-title {
      font-size: 20px;
      font-weight: bold;
      color: #fff;
    }

    .sub-header-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 0.5rem;

      button {
        min-width: 0;
        height: 40px;
        font-size: 14px;
        padding: 0 1rem;
        border-radius: 50px;
        background: #fff;
      }
    }
  }

  .stepper-section {
    flex-shrink: 0;
  }

  .content-container {
    flex: 1;
    // overflow-y: auto;
    // padding: 1rem 0;

    .step-content {
      // width: 100%;
      // height: 100%;
      animation: fadeIn 0.3s ease-in-out;
    }

    // .placeholder-content {
    //   display: flex;
    //   flex-direction: column;
    //   align-items: center;
    //   justify-content: center;
    //   height: 400px;
    //   background: #f8f9fa;
    //   border-radius: 12px;
    //   border: 2px dashed #dee2e6;
    //   text-align: center;

    //   h3 {
    //     color: #495057;
    //     margin-bottom: 1rem;
    //     font-size: 24px;
    //   }

    //   p {
    //     color: #6c757d;
    //     font-size: 16px;
    //     margin: 0;
    //   }
    // }
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    margin-top: auto;
    flex-shrink: 0;

    button {
      min-width: 0;
    }

    .fab-button {
      width: 85px;
      height: 85px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(237, 237, 243, 0.3) 100%
      );
      border-radius: 42.5px;
      border: 1px solid #6566cd;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      img {
        width: 40px;
        height: 40px;
      }

      button {
        min-width: 0;
      }
    }

    .navigation-buttons {
      display: flex;
      gap: 1rem;
      align-items: center;

      .nav-button {
        min-width: 120px;
        height: 48px;
        font-size: 16px;
        border-radius: 24px;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .brainstorming-container {
    padding: 0.5rem;
    gap: 1rem;

    .page-header {
      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 1rem;

      .fab-button {
        width: 70px;
        height: 70px;
        order: 2;

        img {
          width: 32px;
          height: 32px;
        }
      }

      .navigation-buttons {
        order: 1;
        width: 100%;
        justify-content: space-between;

        .nav-button {
          flex: 1;
          max-width: 150px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .brainstorming-container {
    .navigation-buttons {
      flex-direction: column;
      width: 100%;

      .nav-button {
        width: 100%;
        max-width: none;
      }
    }
  }
}

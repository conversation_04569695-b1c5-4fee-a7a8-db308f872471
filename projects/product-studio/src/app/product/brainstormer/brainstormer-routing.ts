import { SwotAnalysisComponent } from './pages/swot-analysis/swot-analysis.component';
import { Routes } from '@angular/router';

export const BRAINSTORMER_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./brainstormer.component').then((m) => m.BrainstormerComponent),
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./components/promt-screen/promt-screen.component').then(
            (m) => m.PromtScreenComponent,
          ),
      },
      {
        path: 'understanding',
        loadComponent: () =>
          import('./pages/understanding/understanding.component').then(
            (m) => m.UnderstandingComponent,
          ),
      },
      {
        path: 'feature-list',
        loadComponent: () =>
          import('./pages/feature-list/feature-list.component').then(
            (m) => m.FeatureListComponent,
          ),
      },
      {
        path: 'persona',
        loadComponent: () =>
          import('./pages/user-persona/user-persona.component').then(
            m => m.UserPersonaComponent
          ),
      },
      {
        path: 'persona-details',
        loadComponent: () =>
          import('./pages/persona-details/persona-details.component').then(
            m => m.PersonaDetailsComponent
          ),
      },
       {
        path: 'swot',
        loadComponent: () =>
          import('./pages/swot-analysis/swot-analysis.component').then(
            m => m.SwotAnalysisComponent
          ),
      },
      {
        path: 'roadmap',
        loadComponent: () =>
          import('./pages/product-roadmap/product-roadmap.component').then(
            m => m.ProductRoadmapComponent
          ),
      },
      {
        path: 'brainstorming',
        loadComponent: () =>
          import('./brainstorming/brainstorming.component').then(
            (m) => m.BrainstormingComponent,
          ),
      },
    ],
  },
];

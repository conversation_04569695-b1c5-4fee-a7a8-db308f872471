@import './variables';

body {
  min-height: 100vh;
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  // background-image: url('../svgs/main-bg-light.svg');
  background-color: var(--background-color);
  // color: var(--body-text-color);
  transition: all 0.3s ease;
}

.container {
  background-color: var(--background-color);
  color: var(--body-text-color);
}

.option-card {
  border: 1.5px solid var(--card-border-color);
  background-color: var(--card-hover-bg);

  &:hover {
    background-color: var(--card-hover-bg);
  }

  &.selected {
    border-color: var(--selected-card-border);
    background-color: var(--selected-card-bg);
  }
}

.button {
  background-color: var(--button-bg);
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--button-hover-bg);
  }
}
